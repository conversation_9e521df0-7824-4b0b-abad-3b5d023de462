// 最小测试服务器
require('dotenv').config();
const express = require('express');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3004;

// 基础中间件
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true
}));
app.use(express.json());

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 简单的注册接口
app.post('/api/auth/register', (req, res) => {
  console.log('Registration request:', req.body);
  
  const { username, email, password, confirmPassword } = req.body;
  
  if (!username || !email || !password || !confirmPassword) {
    return res.status(400).json({
      success: false,
      message: 'Missing required fields'
    });
  }
  
  if (password !== confirmPassword) {
    return res.status(400).json({
      success: false,
      message: 'Passwords do not match'
    });
  }
  
  // 模拟成功注册
  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    data: {
      user: {
        userId: require('uuid').v4(),
        username,
        email,
        nickname: username
      },
      tokens: {
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token'
      }
    }
  });
});

// 简单的登录接口
app.post('/api/auth/login', (req, res) => {
  console.log('Login request:', req.body);
  
  const { identifier, password } = req.body;
  
  res.json({
    success: true,
    message: 'Login successful',
    data: {
      user: {
        userId: require('uuid').v4(),
        username: identifier,
        email: identifier,
        nickname: identifier
      },
      tokens: {
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token'
      }
    }
  });
});

// 任务管理API
app.get('/api/tasks', (req, res) => {
  console.log('Get tasks request');
  res.json({
    success: true,
    data: {
      tasks: [],
      total: 0,
      page: 1,
      limit: 10
    }
  });
});

app.post('/api/tasks', (req, res) => {
  console.log('Create task request:', req.body);

  const taskId = require('uuid').v4();
  const task = {
    id: taskId,
    ...req.body,
    status: 'pending',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };

  res.status(201).json({
    success: true,
    message: 'Task created successfully',
    data: { task }
  });
});

// 复习管理API
app.get('/api/reviews', (req, res) => {
  console.log('Get reviews request');
  res.json({
    success: true,
    data: {
      reviews: [],
      total: 0
    }
  });
});

// 思维导图API
app.get('/api/mindmaps', (req, res) => {
  console.log('Get mindmaps request');
  res.json({
    success: true,
    data: {
      mindmaps: [],
      total: 0
    }
  });
});

// 仪表板数据API
app.get('/api/dashboard', (req, res) => {
  console.log('Get dashboard data request');
  res.json({
    success: true,
    data: {
      todayTasks: [],
      todayReviews: [],
      recentMindmaps: [],
      stats: {
        totalTasks: 0,
        completedTasks: 0,
        pendingReviews: 0,
        studyTime: 0
      }
    }
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 最小测试服务器启动成功`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`💚 健康检查: http://localhost:${PORT}/health`);
});

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection:', reason);
  process.exit(1);
});
