#!/usr/bin/env node

// scripts/validate-swagger.js
// 验证Swagger配置和API文档

const fs = require('fs');
const path = require('path');

const logger = {
  info: (msg) => console.log(`[INFO] ${msg}`),
  success: (msg) => console.log(`[SUCCESS] ${msg}`),
  warning: (msg) => console.log(`[WARNING] ${msg}`),
  error: (msg) => console.error(`[ERROR] ${msg}`)
};

/**
 * Swagger文档验证器
 */
class SwaggerValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
  }

  /**
   * 验证Swagger配置
   */
  validateSwaggerConfig() {
    try {
      logger.info('验证Swagger配置...');
      
      const { swaggerSpec } = require('../src/config/swagger');
      
      // 检查基本结构
      if (!swaggerSpec.openapi) {
        this.errors.push('缺少openapi版本信息');
      }
      
      if (!swaggerSpec.info) {
        this.errors.push('缺少API信息');
      } else {
        if (!swaggerSpec.info.title) this.errors.push('缺少API标题');
        if (!swaggerSpec.info.version) this.errors.push('缺少API版本');
      }
      
      if (!swaggerSpec.servers || swaggerSpec.servers.length === 0) {
        this.warnings.push('未配置服务器信息');
      }
      
      if (!swaggerSpec.components) {
        this.warnings.push('未定义组件');
      } else {
        if (!swaggerSpec.components.schemas) {
          this.warnings.push('未定义数据模型');
        }
        if (!swaggerSpec.components.securitySchemes) {
          this.warnings.push('未定义安全方案');
        }
      }
      
      logger.success('Swagger配置验证完成');
      return true;
      
    } catch (error) {
      this.errors.push(`Swagger配置加载失败: ${error.message}`);
      return false;
    }
  }

  /**
   * 验证路由文件中的Swagger注释
   */
  validateRouteDocumentation() {
    logger.info('验证路由文档注释...');
    
    const routesDir = path.join(__dirname, '../src/routes');
    const routeFiles = fs.readdirSync(routesDir).filter(file => file.endsWith('.js'));
    
    let documentedRoutes = 0;
    let totalRoutes = 0;
    
    for (const file of routeFiles) {
      const filePath = path.join(routesDir, file);
      const content = fs.readFileSync(filePath, 'utf8');
      
      // 统计路由数量
      const routeMatches = content.match(/router\.(get|post|put|patch|delete)\(/g);
      if (routeMatches) {
        totalRoutes += routeMatches.length;
      }
      
      // 统计文档化的路由
      const swaggerMatches = content.match(/@swagger/g);
      if (swaggerMatches) {
        documentedRoutes += swaggerMatches.length;
      }
      
      logger.info(`${file}: 发现 ${routeMatches ? routeMatches.length : 0} 个路由`);
    }
    
    const documentationRate = totalRoutes > 0 ? (documentedRoutes / totalRoutes * 100).toFixed(1) : 0;
    
    if (documentationRate < 50) {
      this.warnings.push(`路由文档化率较低: ${documentationRate}%`);
    }
    
    logger.success(`路由文档验证完成 - 文档化率: ${documentationRate}%`);
    return { totalRoutes, documentedRoutes, documentationRate };
  }

  /**
   * 验证API文档文件
   */
  validateDocumentationFiles() {
    logger.info('验证文档文件...');
    
    const docsDir = path.join(__dirname, '../docs');
    const requiredDocs = ['API.md'];
    
    for (const doc of requiredDocs) {
      const docPath = path.join(docsDir, doc);
      if (!fs.existsSync(docPath)) {
        this.errors.push(`缺少文档文件: ${doc}`);
      } else {
        const content = fs.readFileSync(docPath, 'utf8');
        if (content.length < 1000) {
          this.warnings.push(`文档文件内容较少: ${doc}`);
        }
        logger.info(`✓ 文档文件存在: ${doc}`);
      }
    }
    
    logger.success('文档文件验证完成');
  }

  /**
   * 验证依赖包
   */
  validateDependencies() {
    logger.info('验证Swagger依赖包...');
    
    const packageJsonPath = path.join(__dirname, '../package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    const requiredDeps = ['swagger-jsdoc', 'swagger-ui-express', 'js-yaml'];
    const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    for (const dep of requiredDeps) {
      if (!allDeps[dep]) {
        this.errors.push(`缺少依赖包: ${dep}`);
      } else {
        logger.info(`✓ 依赖包已安装: ${dep}@${allDeps[dep]}`);
      }
    }
    
    logger.success('依赖包验证完成');
  }

  /**
   * 生成验证报告
   */
  generateReport() {
    logger.info('生成验证报告...');
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        errors: this.errors.length,
        warnings: this.warnings.length,
        status: this.errors.length === 0 ? 'PASS' : 'FAIL'
      },
      errors: this.errors,
      warnings: this.warnings,
      recommendations: []
    };
    
    // 生成建议
    if (this.errors.length > 0) {
      report.recommendations.push('修复所有错误后重新验证');
    }
    
    if (this.warnings.length > 0) {
      report.recommendations.push('考虑解决警告以提高文档质量');
    }
    
    if (this.errors.length === 0 && this.warnings.length === 0) {
      report.recommendations.push('API文档配置良好，可以正常使用');
    }
    
    // 保存报告
    const reportPath = path.join(__dirname, '../docs/swagger-validation-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    logger.success(`验证报告已保存: ${reportPath}`);
    return report;
  }

  /**
   * 执行完整验证
   */
  async runFullValidation() {
    logger.info('开始API文档验证...');
    
    // 验证各个组件
    this.validateSwaggerConfig();
    this.validateRouteDocumentation();
    this.validateDocumentationFiles();
    this.validateDependencies();
    
    // 生成报告
    const report = this.generateReport();
    
    // 输出结果
    console.log('\n=== 验证结果 ===');
    console.log(`状态: ${report.summary.status}`);
    console.log(`错误: ${report.summary.errors} 个`);
    console.log(`警告: ${report.summary.warnings} 个`);
    
    if (report.errors.length > 0) {
      console.log('\n❌ 错误:');
      report.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    if (report.warnings.length > 0) {
      console.log('\n⚠️  警告:');
      report.warnings.forEach(warning => console.log(`  - ${warning}`));
    }
    
    if (report.recommendations.length > 0) {
      console.log('\n💡 建议:');
      report.recommendations.forEach(rec => console.log(`  - ${rec}`));
    }
    
    console.log('\n📊 API文档访问地址:');
    console.log('  - Swagger UI: http://localhost:3001/api/docs');
    console.log('  - OpenAPI JSON: http://localhost:3001/api/docs/json');
    console.log('  - OpenAPI YAML: http://localhost:3001/api/docs/yaml');
    
    return report.summary.status === 'PASS';
  }
}

/**
 * 主函数
 */
async function main() {
  const validator = new SwaggerValidator();
  
  try {
    const success = await validator.runFullValidation();
    process.exit(success ? 0 : 1);
  } catch (error) {
    logger.error(`验证过程中发生错误: ${error.message}`);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('未处理的错误:', error);
    process.exit(1);
  });
}

module.exports = SwaggerValidator;
