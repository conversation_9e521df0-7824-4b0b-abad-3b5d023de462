#!/usr/bin/env node

// scripts/backup.js
// 数据库备份脚本

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const util = require('util');

// 加载环境配置
require('dotenv').config();

const execAsync = util.promisify(exec);

const logger = {
  info: (msg) => console.log(`[INFO] ${new Date().toISOString()} ${msg}`),
  success: (msg) => console.log(`[SUCCESS] ${new Date().toISOString()} ${msg}`),
  warning: (msg) => console.log(`[WARNING] ${new Date().toISOString()} ${msg}`),
  error: (msg) => console.error(`[ERROR] ${new Date().toISOString()} ${msg}`)
};

/**
 * 备份管理器
 */
class BackupManager {
  constructor() {
    this.backupDir = process.env.BACKUP_DIR || './backups';
    this.retentionDays = parseInt(process.env.BACKUP_RETENTION_DAYS) || 30;
    this.mongoUri = process.env.MONGODB_URI;
    this.redisHost = process.env.REDIS_HOST || 'localhost';
    this.redisPort = process.env.REDIS_PORT || 6379;
    this.redisDb = process.env.REDIS_DB || 0;
  }

  /**
   * 初始化备份目录
   */
  initBackupDirectory() {
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
      logger.info(`Created backup directory: ${this.backupDir}`);
    }
  }

  /**
   * 生成备份文件名
   */
  generateBackupName(type) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    return `${type}_backup_${timestamp}`;
  }

  /**
   * 备份MongoDB数据库
   */
  async backupMongoDB() {
    try {
      logger.info('Starting MongoDB backup...');
      
      if (!this.mongoUri) {
        throw new Error('MONGODB_URI environment variable is required');
      }

      const backupName = this.generateBackupName('mongodb');
      const backupPath = path.join(this.backupDir, backupName);
      
      // 使用mongodump进行备份
      const command = `mongodump --uri="${this.mongoUri}" --out="${backupPath}"`;
      
      const { stdout, stderr } = await execAsync(command);
      
      if (stderr && !stderr.includes('done dumping')) {
        logger.warning(`MongoDB backup warnings: ${stderr}`);
      }
      
      // 压缩备份文件
      const tarCommand = `tar -czf "${backupPath}.tar.gz" -C "${this.backupDir}" "${backupName}"`;
      await execAsync(tarCommand);
      
      // 删除未压缩的目录
      await execAsync(`rm -rf "${backupPath}"`);
      
      logger.success(`MongoDB backup completed: ${backupName}.tar.gz`);
      return `${backupName}.tar.gz`;
      
    } catch (error) {
      logger.error(`MongoDB backup failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 备份Redis数据
   */
  async backupRedis() {
    try {
      logger.info('Starting Redis backup...');
      
      const backupName = this.generateBackupName('redis');
      const backupPath = path.join(this.backupDir, `${backupName}.rdb`);
      
      // 使用redis-cli进行备份
      const command = `redis-cli -h ${this.redisHost} -p ${this.redisPort} -n ${this.redisDb} --rdb "${backupPath}"`;
      
      const { stdout, stderr } = await execAsync(command);
      
      if (stderr) {
        logger.warning(`Redis backup warnings: ${stderr}`);
      }
      
      // 压缩备份文件
      const gzipCommand = `gzip "${backupPath}"`;
      await execAsync(gzipCommand);
      
      logger.success(`Redis backup completed: ${backupName}.rdb.gz`);
      return `${backupName}.rdb.gz`;
      
    } catch (error) {
      logger.error(`Redis backup failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 备份应用文件
   */
  async backupApplicationFiles() {
    try {
      logger.info('Starting application files backup...');
      
      const backupName = this.generateBackupName('app_files');
      const backupPath = path.join(this.backupDir, `${backupName}.tar.gz`);
      
      // 备份重要的应用文件
      const filesToBackup = [
        'uploads',
        'logs',
        '.env',
        '.env.production',
        'package.json',
        'ecosystem.config.js'
      ].filter(file => fs.existsSync(file));
      
      if (filesToBackup.length === 0) {
        logger.warning('No application files found to backup');
        return null;
      }
      
      const tarCommand = `tar -czf "${backupPath}" ${filesToBackup.join(' ')}`;
      await execAsync(tarCommand);
      
      logger.success(`Application files backup completed: ${backupName}.tar.gz`);
      return `${backupName}.tar.gz`;
      
    } catch (error) {
      logger.error(`Application files backup failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 清理过期备份
   */
  async cleanupOldBackups() {
    try {
      logger.info('Cleaning up old backups...');
      
      const files = fs.readdirSync(this.backupDir);
      const now = new Date();
      const cutoffDate = new Date(now.getTime() - (this.retentionDays * 24 * 60 * 60 * 1000));
      
      let deletedCount = 0;
      
      for (const file of files) {
        const filePath = path.join(this.backupDir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.mtime < cutoffDate) {
          fs.unlinkSync(filePath);
          deletedCount++;
          logger.info(`Deleted old backup: ${file}`);
        }
      }
      
      logger.success(`Cleanup completed. Deleted ${deletedCount} old backup files`);
      
    } catch (error) {
      logger.error(`Cleanup failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 生成备份报告
   */
  generateBackupReport(backups) {
    const report = {
      timestamp: new Date().toISOString(),
      backups: backups.filter(Boolean),
      backupDirectory: this.backupDir,
      retentionDays: this.retentionDays
    };
    
    const reportPath = path.join(this.backupDir, 'backup_report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    logger.info(`Backup report generated: ${reportPath}`);
    return report;
  }

  /**
   * 执行完整备份
   */
  async performFullBackup() {
    try {
      logger.info('Starting full backup process...');
      
      this.initBackupDirectory();
      
      const backups = [];
      
      // 备份MongoDB
      try {
        const mongoBackup = await this.backupMongoDB();
        backups.push({ type: 'mongodb', file: mongoBackup, status: 'success' });
      } catch (error) {
        backups.push({ type: 'mongodb', error: error.message, status: 'failed' });
      }
      
      // 备份Redis
      try {
        const redisBackup = await this.backupRedis();
        backups.push({ type: 'redis', file: redisBackup, status: 'success' });
      } catch (error) {
        backups.push({ type: 'redis', error: error.message, status: 'failed' });
      }
      
      // 备份应用文件
      try {
        const appBackup = await this.backupApplicationFiles();
        if (appBackup) {
          backups.push({ type: 'application', file: appBackup, status: 'success' });
        }
      } catch (error) {
        backups.push({ type: 'application', error: error.message, status: 'failed' });
      }
      
      // 清理过期备份
      await this.cleanupOldBackups();
      
      // 生成报告
      const report = this.generateBackupReport(backups);
      
      const successCount = backups.filter(b => b.status === 'success').length;
      const failCount = backups.filter(b => b.status === 'failed').length;
      
      logger.success(`Full backup completed. Success: ${successCount}, Failed: ${failCount}`);
      
      return report;
      
    } catch (error) {
      logger.error(`Full backup failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 列出可用备份
   */
  listBackups() {
    try {
      if (!fs.existsSync(this.backupDir)) {
        logger.info('No backup directory found');
        return [];
      }
      
      const files = fs.readdirSync(this.backupDir)
        .filter(file => file.endsWith('.tar.gz') || file.endsWith('.rdb.gz'))
        .map(file => {
          const filePath = path.join(this.backupDir, file);
          const stats = fs.statSync(filePath);
          return {
            name: file,
            size: stats.size,
            created: stats.mtime,
            type: file.includes('mongodb') ? 'mongodb' : 
                  file.includes('redis') ? 'redis' : 'application'
          };
        })
        .sort((a, b) => b.created - a.created);
      
      return files;
      
    } catch (error) {
      logger.error(`Failed to list backups: ${error.message}`);
      throw error;
    }
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'full';
  
  const backupManager = new BackupManager();
  
  try {
    switch (command) {
      case 'full':
        await backupManager.performFullBackup();
        break;
        
      case 'mongodb':
        backupManager.initBackupDirectory();
        await backupManager.backupMongoDB();
        break;
        
      case 'redis':
        backupManager.initBackupDirectory();
        await backupManager.backupRedis();
        break;
        
      case 'files':
        backupManager.initBackupDirectory();
        await backupManager.backupApplicationFiles();
        break;
        
      case 'cleanup':
        await backupManager.cleanupOldBackups();
        break;
        
      case 'list':
        const backups = backupManager.listBackups();
        console.log('\nAvailable backups:');
        backups.forEach(backup => {
          console.log(`  ${backup.name} (${backup.type}) - ${backup.created.toISOString()}`);
        });
        break;
        
      default:
        console.log(`
Usage: node backup.js <command>

Commands:
  full      Perform full backup (default)
  mongodb   Backup MongoDB only
  redis     Backup Redis only
  files     Backup application files only
  cleanup   Clean up old backups
  list      List available backups

Examples:
  node backup.js full
  node backup.js mongodb
  node backup.js cleanup
        `);
        break;
    }
    
  } catch (error) {
    logger.error(`Backup operation failed: ${error.message}`);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = BackupManager;
