#!/usr/bin/env node
// scripts/health-check.js - 服务器健康检查脚本

require('dotenv').config();
const http = require('http');
const { exec } = require('child_process');
const util = require('util');

const execAsync = util.promisify(exec);

class HealthChecker {
  constructor() {
    this.baseURL = `http://localhost:${process.env.PORT || 3002}`;
    this.checks = [];
    this.results = {
      passed: 0,
      failed: 0,
      total: 0
    };
  }

  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const icons = {
      info: 'ℹ️',
      success: '✅',
      error: '❌',
      warning: '⚠️'
    };
    console.log(`${timestamp} ${icons[type]} ${message}`);
  }

  async checkProcess() {
    this.log('检查Node.js进程...', 'info');
    try {
      const { stdout } = await execAsync('pgrep -f "node.*server.js" || tasklist /FI "IMAGENAME eq node.exe" 2>nul');
      if (stdout.trim()) {
        this.log('Node.js进程运行中', 'success');
        return true;
      } else {
        this.log('Node.js进程未运行', 'error');
        return false;
      }
    } catch (error) {
      this.log('无法检查进程状态', 'error');
      return false;
    }
  }

  async checkPort() {
    this.log(`检查端口${process.env.PORT || 3002}...`, 'info');
    try {
      const port = process.env.PORT || 3002;
      const { stdout } = await execAsync(`netstat -tlnp 2>/dev/null | grep :${port} || netstat -an | findstr :${port} 2>nul`);
      if (stdout.includes(`:${port}`)) {
        this.log(`端口${port}正在监听`, 'success');
        return true;
      } else {
        this.log(`端口${port}未监听`, 'error');
        return false;
      }
    } catch (error) {
      this.log('无法检查端口状态', 'error');
      return false;
    }
  }

  async checkAPI() {
    this.log('检查API响应...', 'info');
    return new Promise((resolve) => {
      const req = http.get(`${this.baseURL}/health`, { timeout: 5000 }, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            const response = JSON.parse(data);
            if (response.status === 'healthy') {
              this.log('API响应正常', 'success');
              resolve(true);
            } else {
              this.log(`API状态异常: ${response.status}`, 'error');
              resolve(false);
            }
          } catch (error) {
            this.log('API响应格式错误', 'error');
            resolve(false);
          }
        });
      });

      req.on('error', (error) => {
        this.log(`API请求失败: ${error.message}`, 'error');
        resolve(false);
      });

      req.on('timeout', () => {
        this.log('API请求超时', 'error');
        req.destroy();
        resolve(false);
      });
    });
  }

  async checkDatabase() {
    this.log('检查数据库连接...', 'info');
    try {
      const response = await this.makeRequest('/health');
      if (response && response.services && response.services.database && response.services.database.connected) {
        this.log('数据库连接正常', 'success');
        return true;
      } else {
        this.log('数据库连接失败', 'error');
        return false;
      }
    } catch (error) {
      this.log(`数据库检查失败: ${error.message}`, 'error');
      return false;
    }
  }

  async checkRedis() {
    this.log('检查Redis连接...', 'info');
    try {
      const response = await this.makeRequest('/health');
      if (response && response.services && response.services.redis) {
        if (response.services.redis.connected) {
          this.log('Redis连接正常', 'success');
          return true;
        } else {
          this.log('Redis连接失败', 'warning');
          return true; // Redis是可选的，不影响整体健康状态
        }
      } else {
        this.log('Redis未配置', 'warning');
        return true;
      }
    } catch (error) {
      this.log(`Redis检查失败: ${error.message}`, 'warning');
      return true; // Redis是可选的
    }
  }

  async makeRequest(path) {
    return new Promise((resolve, reject) => {
      const req = http.get(`${this.baseURL}${path}`, { timeout: 5000 }, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          try {
            resolve(JSON.parse(data));
          } catch (error) {
            reject(new Error('Invalid JSON response'));
          }
        });
      });

      req.on('error', reject);
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }

  async runCheck(name, checkFunction) {
    this.results.total++;
    try {
      const result = await checkFunction();
      if (result) {
        this.results.passed++;
      } else {
        this.results.failed++;
      }
      return result;
    } catch (error) {
      this.log(`检查 ${name} 时发生错误: ${error.message}`, 'error');
      this.results.failed++;
      return false;
    }
  }

  async runAllChecks() {
    console.log('🔍 开始健康检查...\n');

    // 基础检查
    await this.runCheck('进程检查', () => this.checkProcess());
    await this.runCheck('端口检查', () => this.checkPort());
    await this.runCheck('API检查', () => this.checkAPI());
    
    // 服务检查
    await this.runCheck('数据库检查', () => this.checkDatabase());
    await this.runCheck('Redis检查', () => this.checkRedis());

    // 输出结果
    console.log('\n📊 检查结果汇总:');
    console.log(`总检查项: ${this.results.total}`);
    console.log(`通过: ${this.results.passed}`);
    console.log(`失败: ${this.results.failed}`);

    const healthScore = (this.results.passed / this.results.total) * 100;
    console.log(`健康评分: ${healthScore.toFixed(1)}%`);

    if (this.results.failed === 0) {
      this.log('🎉 所有检查通过，服务器健康状态良好！', 'success');
      process.exit(0);
    } else if (healthScore >= 80) {
      this.log('⚠️ 大部分检查通过，但存在一些问题', 'warning');
      process.exit(1);
    } else {
      this.log('🚨 多项检查失败，服务器状态异常', 'error');
      process.exit(2);
    }
  }

  async getDetailedStatus() {
    try {
      const response = await this.makeRequest('/health');
      console.log('\n📋 详细状态信息:');
      console.log(JSON.stringify(response, null, 2));
    } catch (error) {
      this.log('无法获取详细状态信息', 'error');
    }
  }
}

// 主函数
async function main() {
  const checker = new HealthChecker();
  
  // 检查命令行参数
  const args = process.argv.slice(2);
  
  if (args.includes('--detailed') || args.includes('-d')) {
    await checker.getDetailedStatus();
    return;
  }
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
使用方法: node scripts/health-check.js [选项]

选项:
  -h, --help      显示帮助信息
  -d, --detailed  显示详细状态信息
  
示例:
  node scripts/health-check.js           # 运行基本健康检查
  node scripts/health-check.js -d        # 显示详细状态
    `);
    return;
  }
  
  await checker.runAllChecks();
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error.message);
  process.exit(3);
});

process.on('unhandledRejection', (reason) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(3);
});

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 健康检查失败:', error.message);
    process.exit(3);
  });
}

module.exports = HealthChecker;
