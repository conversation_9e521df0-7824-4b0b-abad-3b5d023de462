#!/usr/bin/env node

// scripts/restore.js
// 数据库恢复脚本

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const util = require('util');
const readline = require('readline');

// 加载环境配置
require('dotenv').config();

const execAsync = util.promisify(exec);

const logger = {
  info: (msg) => console.log(`[INFO] ${new Date().toISOString()} ${msg}`),
  success: (msg) => console.log(`[SUCCESS] ${new Date().toISOString()} ${msg}`),
  warning: (msg) => console.log(`[WARNING] ${new Date().toISOString()} ${msg}`),
  error: (msg) => console.error(`[ERROR] ${new Date().toISOString()} ${msg}`)
};

/**
 * 恢复管理器
 */
class RestoreManager {
  constructor() {
    this.backupDir = process.env.BACKUP_DIR || './backups';
    this.mongoUri = process.env.MONGODB_URI;
    this.redisHost = process.env.REDIS_HOST || 'localhost';
    this.redisPort = process.env.REDIS_PORT || 6379;
    this.redisDb = process.env.REDIS_DB || 0;
  }

  /**
   * 获取用户确认
   */
  async getUserConfirmation(message) {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve) => {
      rl.question(`${message} (y/N): `, (answer) => {
        rl.close();
        resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
      });
    });
  }

  /**
   * 列出可用备份
   */
  listAvailableBackups(type = null) {
    try {
      if (!fs.existsSync(this.backupDir)) {
        logger.error('Backup directory not found');
        return [];
      }

      const files = fs.readdirSync(this.backupDir)
        .filter(file => {
          if (type) {
            return file.includes(type) && (file.endsWith('.tar.gz') || file.endsWith('.rdb.gz'));
          }
          return file.endsWith('.tar.gz') || file.endsWith('.rdb.gz');
        })
        .map(file => {
          const filePath = path.join(this.backupDir, file);
          const stats = fs.statSync(filePath);
          return {
            name: file,
            path: filePath,
            size: stats.size,
            created: stats.mtime,
            type: file.includes('mongodb') ? 'mongodb' : 
                  file.includes('redis') ? 'redis' : 'application'
          };
        })
        .sort((a, b) => b.created - a.created);

      return files;
    } catch (error) {
      logger.error(`Failed to list backups: ${error.message}`);
      throw error;
    }
  }

  /**
   * 选择备份文件
   */
  async selectBackupFile(type) {
    const backups = this.listAvailableBackups(type);
    
    if (backups.length === 0) {
      throw new Error(`No ${type} backups found`);
    }

    console.log(`\nAvailable ${type} backups:`);
    backups.forEach((backup, index) => {
      console.log(`  ${index + 1}. ${backup.name} - ${backup.created.toISOString()}`);
    });

    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    return new Promise((resolve, reject) => {
      rl.question('\nSelect backup number (or press Enter for latest): ', (answer) => {
        rl.close();
        
        if (!answer.trim()) {
          resolve(backups[0]);
        } else {
          const index = parseInt(answer) - 1;
          if (index >= 0 && index < backups.length) {
            resolve(backups[index]);
          } else {
            reject(new Error('Invalid backup selection'));
          }
        }
      });
    });
  }

  /**
   * 恢复MongoDB数据库
   */
  async restoreMongoDB(backupFile = null) {
    try {
      logger.info('Starting MongoDB restore...');

      if (!this.mongoUri) {
        throw new Error('MONGODB_URI environment variable is required');
      }

      // 选择备份文件
      const backup = backupFile || await this.selectBackupFile('mongodb');
      
      logger.info(`Selected backup: ${backup.name}`);

      // 确认操作
      const confirmed = await this.getUserConfirmation(
        '⚠️  This will REPLACE all data in the database. Are you sure?'
      );
      
      if (!confirmed) {
        logger.info('Restore operation cancelled');
        return;
      }

      // 创建临时目录
      const tempDir = path.join(this.backupDir, 'temp_restore');
      if (fs.existsSync(tempDir)) {
        await execAsync(`rm -rf "${tempDir}"`);
      }
      fs.mkdirSync(tempDir, { recursive: true });

      try {
        // 解压备份文件
        logger.info('Extracting backup file...');
        await execAsync(`tar -xzf "${backup.path}" -C "${tempDir}"`);

        // 查找解压后的目录
        const extractedDirs = fs.readdirSync(tempDir);
        if (extractedDirs.length === 0) {
          throw new Error('No data found in backup file');
        }

        const dataDir = path.join(tempDir, extractedDirs[0]);

        // 使用mongorestore恢复数据
        logger.info('Restoring MongoDB data...');
        const restoreCommand = `mongorestore --uri="${this.mongoUri}" --drop "${dataDir}"`;
        
        const { stdout, stderr } = await execAsync(restoreCommand);
        
        if (stderr && !stderr.includes('done')) {
          logger.warning(`MongoDB restore warnings: ${stderr}`);
        }

        logger.success('MongoDB restore completed successfully');

      } finally {
        // 清理临时目录
        if (fs.existsSync(tempDir)) {
          await execAsync(`rm -rf "${tempDir}"`);
        }
      }

    } catch (error) {
      logger.error(`MongoDB restore failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 恢复Redis数据
   */
  async restoreRedis(backupFile = null) {
    try {
      logger.info('Starting Redis restore...');

      // 选择备份文件
      const backup = backupFile || await this.selectBackupFile('redis');
      
      logger.info(`Selected backup: ${backup.name}`);

      // 确认操作
      const confirmed = await this.getUserConfirmation(
        '⚠️  This will REPLACE all data in Redis. Are you sure?'
      );
      
      if (!confirmed) {
        logger.info('Restore operation cancelled');
        return;
      }

      // 创建临时文件
      const tempFile = path.join(this.backupDir, 'temp_restore.rdb');
      
      try {
        // 解压备份文件
        logger.info('Extracting backup file...');
        await execAsync(`gunzip -c "${backup.path}" > "${tempFile}"`);

        // 停止Redis（如果需要）
        logger.warning('Note: You may need to stop Redis service manually for restore');

        // 提供恢复指令
        console.log('\nTo complete Redis restore, please:');
        console.log('1. Stop Redis service');
        console.log(`2. Copy ${tempFile} to your Redis data directory (usually /var/lib/redis/dump.rdb)`);
        console.log('3. Start Redis service');
        console.log('\nOr use redis-cli with --pipe option if Redis is running');

        logger.success('Redis backup file prepared for restore');

      } finally {
        // 清理临时文件（可选，用户可能需要手动使用）
        // if (fs.existsSync(tempFile)) {
        //   fs.unlinkSync(tempFile);
        // }
      }

    } catch (error) {
      logger.error(`Redis restore failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 恢复应用文件
   */
  async restoreApplicationFiles(backupFile = null) {
    try {
      logger.info('Starting application files restore...');

      // 选择备份文件
      const backup = backupFile || await this.selectBackupFile('app_files');
      
      logger.info(`Selected backup: ${backup.name}`);

      // 确认操作
      const confirmed = await this.getUserConfirmation(
        '⚠️  This will REPLACE existing application files. Are you sure?'
      );
      
      if (!confirmed) {
        logger.info('Restore operation cancelled');
        return;
      }

      // 解压并恢复文件
      logger.info('Extracting application files...');
      await execAsync(`tar -xzf "${backup.path}"`);

      logger.success('Application files restore completed successfully');

    } catch (error) {
      logger.error(`Application files restore failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * 验证恢复结果
   */
  async validateRestore(type) {
    try {
      logger.info(`Validating ${type} restore...`);

      switch (type) {
        case 'mongodb':
          // 简单连接测试
          const mongoose = require('mongoose');
          await mongoose.connect(this.mongoUri);
          const collections = await mongoose.connection.db.listCollections().toArray();
          logger.info(`MongoDB validation: Found ${collections.length} collections`);
          await mongoose.disconnect();
          break;

        case 'redis':
          // Redis连接测试
          const redis = require('redis');
          const client = redis.createClient({
            host: this.redisHost,
            port: this.redisPort,
            db: this.redisDb
          });
          await client.connect();
          const info = await client.info();
          logger.info('Redis validation: Connection successful');
          await client.disconnect();
          break;

        case 'application':
          // 检查关键文件
          const keyFiles = ['uploads', 'logs', '.env'];
          const existingFiles = keyFiles.filter(file => fs.existsSync(file));
          logger.info(`Application validation: Found ${existingFiles.length}/${keyFiles.length} key files`);
          break;
      }

      logger.success(`${type} restore validation passed`);

    } catch (error) {
      logger.warning(`${type} restore validation failed: ${error.message}`);
    }
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const backupFile = args[1];

  const restoreManager = new RestoreManager();

  try {
    switch (command) {
      case 'mongodb':
        await restoreManager.restoreMongoDB(backupFile);
        await restoreManager.validateRestore('mongodb');
        break;

      case 'redis':
        await restoreManager.restoreRedis(backupFile);
        await restoreManager.validateRestore('redis');
        break;

      case 'files':
        await restoreManager.restoreApplicationFiles(backupFile);
        await restoreManager.validateRestore('application');
        break;

      case 'list':
        const backups = restoreManager.listAvailableBackups();
        console.log('\nAvailable backups:');
        if (backups.length === 0) {
          console.log('  No backups found');
        } else {
          backups.forEach(backup => {
            console.log(`  ${backup.name} (${backup.type}) - ${backup.created.toISOString()}`);
          });
        }
        break;

      default:
        console.log(`
Usage: node restore.js <command> [backup_file]

Commands:
  mongodb [file]    Restore MongoDB database
  redis [file]      Restore Redis data
  files [file]      Restore application files
  list             List available backups

Examples:
  node restore.js mongodb
  node restore.js redis mongodb_backup_2023-12-01.rdb.gz
  node restore.js list
        `);
        break;
    }

  } catch (error) {
    logger.error(`Restore operation failed: ${error.message}`);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = RestoreManager;
