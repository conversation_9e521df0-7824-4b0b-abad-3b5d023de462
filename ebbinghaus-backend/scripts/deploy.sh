#!/bin/bash

# 艾宾浩斯学习系统部署脚本
# 用于生产环境部署

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
PROJECT_NAME="ebbinghaus-backend"
DEPLOY_USER="deploy"
DEPLOY_PATH="/opt/ebbinghaus"
SERVICE_NAME="ebbinghaus-backend"
NODE_VERSION="18"
PM2_APP_NAME="ebbinghaus-api"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "请不要使用root用户运行此脚本"
        exit 1
    fi
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先安装 Node.js ${NODE_VERSION}"
        exit 1
    fi
    
    local node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [[ $node_version -lt $NODE_VERSION ]]; then
        log_error "Node.js 版本过低，需要 ${NODE_VERSION} 或更高版本"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    # 检查PM2
    if ! command -v pm2 &> /dev/null; then
        log_warning "PM2 未安装，正在安装..."
        npm install -g pm2
    fi
    
    # 检查MongoDB
    if ! command -v mongod &> /dev/null; then
        log_warning "MongoDB 未检测到，请确保MongoDB服务正在运行"
    fi
    
    # 检查Redis
    if ! command -v redis-server &> /dev/null; then
        log_warning "Redis 未检测到，请确保Redis服务正在运行"
    fi
    
    log_success "系统要求检查完成"
}

# 创建部署目录
create_deploy_directory() {
    log_info "创建部署目录..."
    
    if [[ ! -d "$DEPLOY_PATH" ]]; then
        sudo mkdir -p "$DEPLOY_PATH"
        sudo chown $USER:$USER "$DEPLOY_PATH"
        log_success "部署目录创建完成: $DEPLOY_PATH"
    else
        log_info "部署目录已存在: $DEPLOY_PATH"
    fi
}

# 备份当前版本
backup_current_version() {
    if [[ -d "$DEPLOY_PATH/current" ]]; then
        log_info "备份当前版本..."
        local backup_dir="$DEPLOY_PATH/backup/$(date +%Y%m%d_%H%M%S)"
        mkdir -p "$backup_dir"
        cp -r "$DEPLOY_PATH/current"/* "$backup_dir/"
        log_success "当前版本已备份到: $backup_dir"
    fi
}

# 部署新版本
deploy_new_version() {
    log_info "部署新版本..."
    
    # 创建临时部署目录
    local temp_dir="$DEPLOY_PATH/temp_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$temp_dir"
    
    # 复制项目文件
    log_info "复制项目文件..."
    cp -r ./* "$temp_dir/"
    
    # 进入临时目录
    cd "$temp_dir"
    
    # 安装依赖
    log_info "安装生产依赖..."
    npm ci --only=production
    
    # 创建必要的目录
    mkdir -p logs uploads backups
    
    # 设置环境变量文件
    if [[ ! -f ".env" ]]; then
        if [[ -f ".env.production" ]]; then
            cp .env.production .env
            log_info "使用生产环境配置文件"
        else
            log_error "未找到环境配置文件"
            exit 1
        fi
    fi
    
    # 运行数据库迁移（如果有）
    if [[ -f "scripts/migrate.js" ]]; then
        log_info "运行数据库迁移..."
        node scripts/migrate.js
    fi
    
    # 原子性部署：移动到正式目录
    cd "$DEPLOY_PATH"
    if [[ -d "current" ]]; then
        mv current "old_$(date +%Y%m%d_%H%M%S)"
    fi
    mv "$temp_dir" current
    
    log_success "新版本部署完成"
}

# 配置PM2
configure_pm2() {
    log_info "配置PM2..."
    
    cd "$DEPLOY_PATH/current"
    
    # 创建PM2配置文件
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: '$PM2_APP_NAME',
    script: './src/app.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    time: true,
    max_memory_restart: '512M',
    node_args: '--max-old-space-size=512',
    watch: false,
    ignore_watch: ['node_modules', 'logs', 'uploads'],
    max_restarts: 10,
    min_uptime: '10s',
    kill_timeout: 5000
  }]
};
EOF
    
    log_success "PM2配置文件创建完成"
}

# 启动服务
start_service() {
    log_info "启动服务..."
    
    cd "$DEPLOY_PATH/current"
    
    # 停止旧的PM2进程
    pm2 stop $PM2_APP_NAME 2>/dev/null || true
    pm2 delete $PM2_APP_NAME 2>/dev/null || true
    
    # 启动新的PM2进程
    pm2 start ecosystem.config.js
    
    # 保存PM2配置
    pm2 save
    
    # 设置PM2开机自启
    pm2 startup
    
    log_success "服务启动完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    local health_url="http://localhost:3001/health"
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s "$health_url" > /dev/null; then
            log_success "健康检查通过"
            return 0
        fi
        
        log_info "健康检查失败，重试 $attempt/$max_attempts..."
        sleep 2
        ((attempt++))
    done
    
    log_error "健康检查失败，服务可能未正常启动"
    return 1
}

# 配置Nginx（可选）
configure_nginx() {
    if command -v nginx &> /dev/null; then
        log_info "配置Nginx反向代理..."
        
        cat > /tmp/ebbinghaus-nginx.conf << EOF
server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL配置
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
    
    # 代理配置
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 86400;
    }
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
EOF
        
        log_info "Nginx配置文件已生成: /tmp/ebbinghaus-nginx.conf"
        log_warning "请手动将配置文件移动到Nginx配置目录并重启Nginx"
    fi
}

# 清理旧版本
cleanup_old_versions() {
    log_info "清理旧版本..."
    
    cd "$DEPLOY_PATH"
    
    # 保留最近3个备份版本
    ls -dt old_* backup/* 2>/dev/null | tail -n +4 | xargs rm -rf 2>/dev/null || true
    
    log_success "旧版本清理完成"
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    echo
    echo "部署信息:"
    echo "  项目路径: $DEPLOY_PATH/current"
    echo "  服务名称: $PM2_APP_NAME"
    echo "  访问地址: http://localhost:3001"
    echo "  健康检查: http://localhost:3001/health"
    echo
    echo "常用命令:"
    echo "  查看服务状态: pm2 status"
    echo "  查看日志: pm2 logs $PM2_APP_NAME"
    echo "  重启服务: pm2 restart $PM2_APP_NAME"
    echo "  停止服务: pm2 stop $PM2_APP_NAME"
    echo
}

# 主函数
main() {
    log_info "开始部署艾宾浩斯学习系统..."
    
    check_root
    check_requirements
    create_deploy_directory
    backup_current_version
    deploy_new_version
    configure_pm2
    start_service
    
    if health_check; then
        configure_nginx
        cleanup_old_versions
        show_deployment_info
    else
        log_error "部署失败，请检查日志"
        exit 1
    fi
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
