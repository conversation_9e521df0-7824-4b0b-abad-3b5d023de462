#!/usr/bin/env node

// scripts/migrate.js
// 数据库迁移脚本

const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');

// 加载环境配置
require('dotenv').config();

const logger = {
  info: (msg) => console.log(`[INFO] ${msg}`),
  success: (msg) => console.log(`[SUCCESS] ${msg}`),
  warning: (msg) => console.log(`[WARNING] ${msg}`),
  error: (msg) => console.error(`[ERROR] ${msg}`)
};

/**
 * 数据库迁移管理器
 */
class MigrationManager {
  constructor() {
    this.migrationsPath = path.join(__dirname, '../migrations');
    this.migrationCollection = 'migrations';
  }

  /**
   * 连接数据库
   */
  async connect() {
    try {
      const mongoUri = process.env.MONGODB_URI;
      if (!mongoUri) {
        throw new Error('MONGODB_URI environment variable is required');
      }

      await mongoose.connect(mongoUri, {
        useNewUrlParser: true,
        useUnifiedTopology: true
      });
      
      logger.success('Connected to MongoDB');
    } catch (error) {
      logger.error(`Failed to connect to MongoDB: ${error.message}`);
      throw error;
    }
  }

  /**
   * 断开数据库连接
   */
  async disconnect() {
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');
  }

  /**
   * 获取已执行的迁移记录
   */
  async getExecutedMigrations() {
    const db = mongoose.connection.db;
    const collection = db.collection(this.migrationCollection);
    
    const migrations = await collection.find({}).sort({ executedAt: 1 }).toArray();
    return migrations.map(m => m.name);
  }

  /**
   * 记录迁移执行
   */
  async recordMigration(name) {
    const db = mongoose.connection.db;
    const collection = db.collection(this.migrationCollection);
    
    await collection.insertOne({
      name,
      executedAt: new Date()
    });
  }

  /**
   * 获取待执行的迁移文件
   */
  async getPendingMigrations() {
    // 确保迁移目录存在
    if (!fs.existsSync(this.migrationsPath)) {
      fs.mkdirSync(this.migrationsPath, { recursive: true });
      logger.info(`Created migrations directory: ${this.migrationsPath}`);
    }

    const migrationFiles = fs.readdirSync(this.migrationsPath)
      .filter(file => file.endsWith('.js'))
      .sort();

    const executedMigrations = await this.getExecutedMigrations();
    
    return migrationFiles.filter(file => {
      const migrationName = path.basename(file, '.js');
      return !executedMigrations.includes(migrationName);
    });
  }

  /**
   * 执行单个迁移
   */
  async executeMigration(filename) {
    const migrationPath = path.join(this.migrationsPath, filename);
    const migrationName = path.basename(filename, '.js');
    
    try {
      logger.info(`Executing migration: ${migrationName}`);
      
      const migration = require(migrationPath);
      
      if (typeof migration.up !== 'function') {
        throw new Error(`Migration ${migrationName} must export an 'up' function`);
      }
      
      await migration.up(mongoose.connection.db);
      await this.recordMigration(migrationName);
      
      logger.success(`Migration completed: ${migrationName}`);
    } catch (error) {
      logger.error(`Migration failed: ${migrationName} - ${error.message}`);
      throw error;
    }
  }

  /**
   * 执行所有待执行的迁移
   */
  async runMigrations() {
    const pendingMigrations = await this.getPendingMigrations();
    
    if (pendingMigrations.length === 0) {
      logger.info('No pending migrations found');
      return;
    }
    
    logger.info(`Found ${pendingMigrations.length} pending migrations`);
    
    for (const migration of pendingMigrations) {
      await this.executeMigration(migration);
    }
    
    logger.success('All migrations completed successfully');
  }

  /**
   * 创建新的迁移文件
   */
  createMigration(name) {
    if (!name) {
      throw new Error('Migration name is required');
    }
    
    const timestamp = new Date().toISOString().replace(/[-:]/g, '').replace(/\..+/, '');
    const filename = `${timestamp}_${name.replace(/\s+/g, '_').toLowerCase()}.js`;
    const filepath = path.join(this.migrationsPath, filename);
    
    const template = `// Migration: ${name}
// Created: ${new Date().toISOString()}

/**
 * 执行迁移
 * @param {Object} db - MongoDB数据库连接
 */
async function up(db) {
  // 在这里编写迁移逻辑
  // 例如：
  // await db.collection('users').createIndex({ email: 1 }, { unique: true });
  // await db.collection('tasks').updateMany({}, { $set: { version: 2 } });
  
  console.log('Migration ${name} executed successfully');
}

/**
 * 回滚迁移（可选）
 * @param {Object} db - MongoDB数据库连接
 */
async function down(db) {
  // 在这里编写回滚逻辑
  // 例如：
  // await db.collection('users').dropIndex({ email: 1 });
  // await db.collection('tasks').updateMany({}, { $unset: { version: 1 } });
  
  console.log('Migration ${name} rolled back successfully');
}

module.exports = { up, down };
`;
    
    // 确保迁移目录存在
    if (!fs.existsSync(this.migrationsPath)) {
      fs.mkdirSync(this.migrationsPath, { recursive: true });
    }
    
    fs.writeFileSync(filepath, template);
    logger.success(`Migration file created: ${filename}`);
    
    return filepath;
  }

  /**
   * 初始化数据库索引
   */
  async initializeIndexes() {
    logger.info('Initializing database indexes...');
    
    const db = mongoose.connection.db;
    
    try {
      // 用户集合索引
      await db.collection('users').createIndex({ email: 1 }, { unique: true });
      await db.collection('users').createIndex({ username: 1 }, { unique: true });
      await db.collection('users').createIndex({ userId: 1 }, { unique: true });
      await db.collection('users').createIndex({ createdAt: 1 });
      
      // 任务集合索引
      await db.collection('tasks').createIndex({ userId: 1 });
      await db.collection('tasks').createIndex({ userId: 1, status: 1 });
      await db.collection('tasks').createIndex({ userId: 1, type: 1 });
      await db.collection('tasks').createIndex({ userId: 1, difficulty: 1 });
      await db.collection('tasks').createIndex({ createdAt: 1 });
      await db.collection('tasks').createIndex({ updatedAt: 1 });
      
      // 复习计划集合索引
      await db.collection('reviewschedules').createIndex({ userId: 1 });
      await db.collection('reviewschedules').createIndex({ taskId: 1 });
      await db.collection('reviewschedules').createIndex({ userId: 1, scheduledTime: 1 });
      await db.collection('reviewschedules').createIndex({ userId: 1, status: 1 });
      await db.collection('reviewschedules').createIndex({ scheduledTime: 1 });
      
      // 学习记录集合索引
      await db.collection('learningrecords').createIndex({ userId: 1 });
      await db.collection('learningrecords').createIndex({ taskId: 1 });
      await db.collection('learningrecords').createIndex({ userId: 1, recordDate: 1 });
      await db.collection('learningrecords').createIndex({ recordDate: 1 });
      
      logger.success('Database indexes initialized successfully');
    } catch (error) {
      logger.error(`Failed to initialize indexes: ${error.message}`);
      throw error;
    }
  }

  /**
   * 验证数据库结构
   */
  async validateDatabase() {
    logger.info('Validating database structure...');
    
    const db = mongoose.connection.db;
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);
    
    const requiredCollections = ['users', 'tasks', 'reviewschedules', 'learningrecords'];
    const missingCollections = requiredCollections.filter(name => !collectionNames.includes(name));
    
    if (missingCollections.length > 0) {
      logger.warning(`Missing collections: ${missingCollections.join(', ')}`);
    } else {
      logger.success('All required collections exist');
    }
    
    // 验证索引
    for (const collectionName of requiredCollections) {
      if (collectionNames.includes(collectionName)) {
        const indexes = await db.collection(collectionName).indexes();
        logger.info(`Collection '${collectionName}' has ${indexes.length} indexes`);
      }
    }
  }
}

/**
 * 主函数
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  const migrationManager = new MigrationManager();
  
  try {
    await migrationManager.connect();
    
    switch (command) {
      case 'up':
      case 'migrate':
        await migrationManager.runMigrations();
        break;
        
      case 'create':
        const migrationName = args[1];
        if (!migrationName) {
          logger.error('Migration name is required');
          process.exit(1);
        }
        migrationManager.createMigration(migrationName);
        break;
        
      case 'init':
        await migrationManager.initializeIndexes();
        break;
        
      case 'validate':
        await migrationManager.validateDatabase();
        break;
        
      default:
        console.log(`
Usage: node migrate.js <command> [options]

Commands:
  up, migrate     Run all pending migrations
  create <name>   Create a new migration file
  init           Initialize database indexes
  validate       Validate database structure

Examples:
  node migrate.js migrate
  node migrate.js create "add user preferences"
  node migrate.js init
  node migrate.js validate
        `);
        break;
    }
    
  } catch (error) {
    logger.error(`Migration failed: ${error.message}`);
    process.exit(1);
  } finally {
    await migrationManager.disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}

module.exports = MigrationManager;
