#!/usr/bin/env node
// scripts/troubleshoot.js - 故障排除脚本

require('dotenv').config();
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const util = require('util');

const execAsync = util.promisify(exec);

class Troubleshooter {
  constructor() {
    this.issues = [];
    this.solutions = [];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const icons = {
      info: 'ℹ️',
      success: '✅',
      error: '❌',
      warning: '⚠️',
      fix: '🔧'
    };
    console.log(`${timestamp} ${icons[type]} ${message}`);
  }

  addIssue(issue, solution) {
    this.issues.push(issue);
    this.solutions.push(solution);
  }

  async checkSystemInfo() {
    this.log('收集系统信息...', 'info');
    
    try {
      console.log('\n📊 系统信息:');
      console.log(`操作系统: ${process.platform} ${process.arch}`);
      console.log(`Node.js版本: ${process.version}`);
      console.log(`当前目录: ${process.cwd()}`);
      
      // 检查内存使用
      const memUsage = process.memoryUsage();
      console.log(`内存使用: ${Math.round(memUsage.rss / 1024 / 1024)}MB`);
      
      // 检查磁盘空间
      if (process.platform !== 'win32') {
        const { stdout } = await execAsync('df -h .');
        console.log('磁盘空间:');
        console.log(stdout);
      }
      
    } catch (error) {
      this.log(`获取系统信息失败: ${error.message}`, 'error');
    }
  }

  async checkEnvironment() {
    this.log('检查环境配置...', 'info');
    
    // 检查环境变量文件
    const envFiles = ['.env', '.env.development', '.env.production'];
    let hasEnvFile = false;
    
    for (const file of envFiles) {
      if (fs.existsSync(file)) {
        this.log(`找到环境文件: ${file}`, 'success');
        hasEnvFile = true;
      }
    }
    
    if (!hasEnvFile) {
      this.addIssue(
        '未找到环境配置文件',
        '创建 .env 文件并配置必要的环境变量'
      );
    }
    
    // 检查关键环境变量
    const requiredVars = ['MONGODB_URI', 'JWT_SECRET', 'PORT'];
    for (const varName of requiredVars) {
      if (!process.env[varName]) {
        this.addIssue(
          `缺少环境变量: ${varName}`,
          `在 .env 文件中设置 ${varName}=your_value`
        );
      } else {
        this.log(`环境变量 ${varName}: 已设置`, 'success');
      }
    }
  }

  async checkDependencies() {
    this.log('检查项目依赖...', 'info');
    
    // 检查package.json
    if (!fs.existsSync('package.json')) {
      this.addIssue(
        'package.json 文件不存在',
        '确保在正确的项目目录中运行脚本'
      );
      return;
    }
    
    // 检查node_modules
    if (!fs.existsSync('node_modules')) {
      this.addIssue(
        'node_modules 目录不存在',
        '运行 npm install 安装依赖包'
      );
      return;
    }
    
    // 检查关键依赖
    const requiredDeps = ['express', 'mongoose', 'jsonwebtoken'];
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    for (const dep of requiredDeps) {
      if (!packageJson.dependencies[dep]) {
        this.addIssue(
          `缺少依赖包: ${dep}`,
          `运行 npm install ${dep} 安装依赖`
        );
      } else {
        this.log(`依赖包 ${dep}: 已安装`, 'success');
      }
    }
  }

  async checkPorts() {
    this.log('检查端口使用情况...', 'info');
    
    const port = process.env.PORT || 3002;
    
    try {
      const command = process.platform === 'win32' 
        ? `netstat -an | findstr :${port}`
        : `netstat -tlnp | grep :${port}`;
        
      const { stdout } = await execAsync(command);
      
      if (stdout.includes(`:${port}`)) {
        this.addIssue(
          `端口 ${port} 已被占用`,
          `使用 npm run start:force 强制启动，或更改端口号`
        );
        
        // 尝试找到占用进程
        if (process.platform === 'win32') {
          const { stdout: processInfo } = await execAsync(`netstat -ano | findstr :${port}`);
          console.log('端口占用详情:');
          console.log(processInfo);
        }
      } else {
        this.log(`端口 ${port}: 可用`, 'success');
      }
    } catch (error) {
      this.log(`检查端口时出错: ${error.message}`, 'warning');
    }
  }

  async checkDatabaseServices() {
    this.log('检查数据库服务...', 'info');
    
    // 检查MongoDB
    try {
      if (process.platform === 'win32') {
        const { stdout } = await execAsync('sc query MongoDB');
        if (stdout.includes('RUNNING')) {
          this.log('MongoDB服务: 运行中', 'success');
        } else {
          this.addIssue(
            'MongoDB服务未运行',
            '启动MongoDB服务: net start MongoDB'
          );
        }
      } else {
        const { stdout } = await execAsync('systemctl is-active mongod');
        if (stdout.trim() === 'active') {
          this.log('MongoDB服务: 运行中', 'success');
        } else {
          this.addIssue(
            'MongoDB服务未运行',
            '启动MongoDB服务: sudo systemctl start mongod'
          );
        }
      }
    } catch (error) {
      this.addIssue(
        'MongoDB服务状态未知',
        '手动检查MongoDB服务状态并启动'
      );
    }
    
    // 检查Redis（可选）
    if (process.env.REDIS_ENABLED !== 'false') {
      try {
        if (process.platform === 'win32') {
          const { stdout } = await execAsync('sc query Redis');
          if (stdout.includes('RUNNING')) {
            this.log('Redis服务: 运行中', 'success');
          } else {
            this.log('Redis服务未运行（可选）', 'warning');
          }
        } else {
          const { stdout } = await execAsync('systemctl is-active redis-server');
          if (stdout.trim() === 'active') {
            this.log('Redis服务: 运行中', 'success');
          } else {
            this.log('Redis服务未运行（可选）', 'warning');
          }
        }
      } catch (error) {
        this.log('Redis服务状态未知（可选）', 'warning');
      }
    }
  }

  async checkProjectStructure() {
    this.log('检查项目结构...', 'info');
    
    const requiredFiles = [
      'src/server.js',
      'src/app.js',
      'src/config/database.js',
      'src/config/logger.js',
      'package.json'
    ];
    
    const requiredDirs = [
      'src',
      'src/config',
      'src/controllers',
      'src/services',
      'src/models',
      'src/routes',
      'src/middleware'
    ];
    
    // 检查文件
    for (const file of requiredFiles) {
      if (fs.existsSync(file)) {
        this.log(`文件 ${file}: 存在`, 'success');
      } else {
        this.addIssue(
          `缺少文件: ${file}`,
          '确保项目结构完整，或重新克隆项目'
        );
      }
    }
    
    // 检查目录
    for (const dir of requiredDirs) {
      if (fs.existsSync(dir)) {
        this.log(`目录 ${dir}: 存在`, 'success');
      } else {
        this.addIssue(
          `缺少目录: ${dir}`,
          '确保项目结构完整，或重新克隆项目'
        );
      }
    }
  }

  async checkLogs() {
    this.log('检查日志文件...', 'info');
    
    const logDir = 'logs';
    if (fs.existsSync(logDir)) {
      const logFiles = fs.readdirSync(logDir);
      
      if (logFiles.length > 0) {
        this.log(`找到 ${logFiles.length} 个日志文件`, 'success');
        
        // 检查最新的错误日志
        const errorLogs = logFiles.filter(file => file.includes('error'));
        if (errorLogs.length > 0) {
          const latestErrorLog = errorLogs.sort().pop();
          const logPath = path.join(logDir, latestErrorLog);
          const logContent = fs.readFileSync(logPath, 'utf8');
          const recentErrors = logContent.split('\n').slice(-10).filter(line => line.trim());
          
          if (recentErrors.length > 0) {
            console.log('\n📋 最近的错误日志:');
            recentErrors.forEach(line => console.log(line));
          }
        }
      } else {
        this.log('日志目录为空', 'warning');
      }
    } else {
      this.log('日志目录不存在', 'warning');
    }
  }

  async generateReport() {
    console.log('\n🔍 开始故障排除诊断...\n');
    
    await this.checkSystemInfo();
    await this.checkEnvironment();
    await this.checkDependencies();
    await this.checkPorts();
    await this.checkDatabaseServices();
    await this.checkProjectStructure();
    await this.checkLogs();
    
    // 输出问题和解决方案
    if (this.issues.length > 0) {
      console.log('\n🚨 发现的问题:');
      this.issues.forEach((issue, index) => {
        console.log(`${index + 1}. ${issue}`);
      });
      
      console.log('\n🔧 建议的解决方案:');
      this.solutions.forEach((solution, index) => {
        console.log(`${index + 1}. ${solution}`);
      });
      
      console.log('\n💡 快速修复命令:');
      console.log('npm install                    # 安装依赖');
      console.log('npm run start:force           # 强制启动服务器');
      console.log('npm run health                # 检查服务器健康状态');
      
    } else {
      this.log('🎉 未发现明显问题，系统配置正常', 'success');
      console.log('\n💡 如果仍有问题，请尝试:');
      console.log('1. 查看详细日志: npm run health:detailed');
      console.log('2. 重新安装依赖: rm -rf node_modules && npm install');
      console.log('3. 检查防火墙和网络设置');
    }
    
    console.log('\n📞 如需更多帮助，请查看文档: docs/服务器启动指南.md');
  }
}

// 主函数
async function main() {
  const troubleshooter = new Troubleshooter();
  await troubleshooter.generateReport();
}

// 运行主函数
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 故障排除脚本异常:', error.message);
    process.exit(1);
  });
}

module.exports = Troubleshooter;
