# 数据库配置
MONGODB_URI=mongodb://localhost:27017/ebbinghaus_learning
MONGODB_TEST_URI=mongodb://localhost:27017/ebbinghaus_learning_test

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d

# 服务器配置
PORT=3000
NODE_ENV=development

# 日志配置
LOG_LEVEL=info
LOG_DIR=logs

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760

# 邮件配置（可选）
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
FROM_EMAIL=

# 其他配置
API_PREFIX=/api
CORS_ORIGIN=http://localhost:5173
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
