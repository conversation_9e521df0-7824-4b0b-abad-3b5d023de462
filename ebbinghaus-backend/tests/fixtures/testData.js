// tests/fixtures/testData.js

/**
 * 测试用户数据
 */
const testUsers = {
  validUser: {
    username: 'testuser',
    email: '<EMAIL>',
    password: 'password123',
    preferences: {
      dailyGoal: 5,
      reminderEnabled: true,
      timezone: 'Asia/Shanghai'
    }
  },
  
  adminUser: {
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    preferences: {
      dailyGoal: 10,
      reminderEnabled: true,
      timezone: 'Asia/Shanghai'
    }
  },
  
  invalidUser: {
    username: '', // 无效用户名
    email: 'invalid-email', // 无效邮箱
    password: '123' // 密码太短
  }
};

/**
 * 测试任务数据
 */
const testTasks = {
  vocabularyTask: {
    title: 'English Vocabulary',
    content: 'Learn new English words',
    type: 'vocabulary',
    difficulty: 'medium',
    tags: ['english', 'vocabulary'],
    metadata: {
      source: 'textbook',
      category: 'language'
    }
  },
  
  mathTask: {
    title: 'Algebra Formulas',
    content: 'Memorize basic algebra formulas',
    type: 'formula',
    difficulty: 'hard',
    tags: ['math', 'algebra'],
    metadata: {
      source: 'course',
      category: 'mathematics'
    }
  },
  
  easyTask: {
    title: 'Simple Facts',
    content: 'Basic facts to remember',
    type: 'fact',
    difficulty: 'easy',
    tags: ['basic'],
    metadata: {
      source: 'notes',
      category: 'general'
    }
  }
};

/**
 * 测试复习计划数据
 */
const testReviewSchedules = {
  newSchedule: {
    interval: 1,
    easeFactor: 2.5,
    repetitions: 0,
    nextReviewDate: new Date(Date.now() + 24 * 60 * 60 * 1000) // 明天
  },
  
  progressedSchedule: {
    interval: 3,
    easeFactor: 2.6,
    repetitions: 2,
    nextReviewDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000) // 3天后
  }
};

/**
 * 测试学习记录数据
 */
const testLearningRecords = {
  correctRecord: {
    quality: 4,
    responseTime: 5000,
    difficulty: 'medium',
    reviewType: 'scheduled'
  },
  
  incorrectRecord: {
    quality: 1,
    responseTime: 15000,
    difficulty: 'hard',
    reviewType: 'retry'
  }
};

/**
 * API测试数据
 */
const apiTestData = {
  // 认证相关
  auth: {
    validLogin: {
      email: '<EMAIL>',
      password: 'password123'
    },
    invalidLogin: {
      email: '<EMAIL>',
      password: 'wrongpassword'
    },
    validRegister: {
      username: 'newuser',
      email: '<EMAIL>',
      password: 'newpassword123'
    }
  },
  
  // 任务相关
  tasks: {
    createTask: {
      title: 'New Learning Task',
      content: 'Content for new task',
      type: 'vocabulary',
      difficulty: 'medium',
      tags: ['new', 'test']
    },
    updateTask: {
      title: 'Updated Task Title',
      content: 'Updated content',
      difficulty: 'hard'
    },
    invalidTask: {
      title: '', // 空标题
      content: 'Content without title'
    }
  },
  
  // 复习相关
  reviews: {
    submitReview: {
      quality: 4,
      responseTime: 3000
    },
    invalidReview: {
      quality: 6, // 超出范围
      responseTime: -1000 // 负数
    }
  }
};

/**
 * 错误消息
 */
const errorMessages = {
  auth: {
    INVALID_CREDENTIALS: 'Invalid email or password',
    TOKEN_REQUIRED: 'Access token is required',
    TOKEN_INVALID: 'Invalid or expired token',
    USER_EXISTS: 'User already exists',
    USER_NOT_FOUND: 'User not found'
  },
  
  validation: {
    REQUIRED_FIELD: 'This field is required',
    INVALID_EMAIL: 'Invalid email format',
    PASSWORD_TOO_SHORT: 'Password must be at least 6 characters',
    INVALID_DIFFICULTY: 'Invalid difficulty level'
  },
  
  tasks: {
    TASK_NOT_FOUND: 'Task not found',
    UNAUTHORIZED_ACCESS: 'Unauthorized to access this task'
  }
};

module.exports = {
  testUsers,
  testTasks,
  testReviewSchedules,
  testLearningRecords,
  apiTestData,
  errorMessages
};
