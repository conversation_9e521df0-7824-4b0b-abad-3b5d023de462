// tests/unit/ebbinghausService.simple.test.js
const ebbinghausService = require('../../src/services/ebbinghausService');

describe('EbbinghausService - Basic Tests', () => {
  describe('calculateNextInterval', () => {
    test('should calculate correct interval for first repetition', () => {
      const result = ebbinghausService.calculateNextInterval(0, 2.5, 4);
      expect(result.interval).toBe(1);
      expect(result.repetitions).toBe(1);
      expect(result.easeFactor).toBeCloseTo(2.5);
    });

    test('should calculate correct interval for second repetition', () => {
      const result = ebbinghausService.calculateNextInterval(1, 2.5, 4);
      expect(result.interval).toBe(6);
      expect(result.repetitions).toBe(2);
      expect(result.easeFactor).toBeCloseTo(2.5);
    });

    test('should increase interval based on ease factor for subsequent repetitions', () => {
      const result = ebbinghausService.calculateNextInterval(6, 2.5, 4);
      expect(result.interval).toBe(15); // 6 * 2.5 = 15
      expect(result.repetitions).toBe(3);
      expect(result.easeFactor).toBeCloseTo(2.5);
    });

    test('should adjust ease factor based on quality', () => {
      // High quality (5) should increase ease factor
      const highQuality = ebbinghausService.calculateNextInterval(1, 2.5, 5);
      expect(highQuality.easeFactor).toBeGreaterThan(2.5);

      // Low quality (2) should decrease ease factor
      const lowQuality = ebbinghausService.calculateNextInterval(1, 2.5, 2);
      expect(lowQuality.easeFactor).toBeLessThan(2.5);
    });

    test('should reset interval for very low quality', () => {
      const result = ebbinghausService.calculateNextInterval(6, 2.5, 0);
      expect(result.interval).toBe(1);
      expect(result.repetitions).toBe(0);
    });

    test('should enforce minimum ease factor', () => {
      const result = ebbinghausService.calculateNextInterval(1, 1.3, 0);
      expect(result.easeFactor).toBeGreaterThanOrEqual(1.3);
    });
  });

  describe('calculateNextReviewDate', () => {
    test('should calculate correct next review date', () => {
      const baseDate = new Date('2025-01-01T00:00:00Z');
      const originalDate = Date;
      global.Date = jest.fn(() => baseDate);
      global.Date.now = jest.fn(() => baseDate.getTime());

      const nextDate = ebbinghausService.calculateNextReviewDate(3);
      const expectedDate = new Date(baseDate.getTime() + 3 * 24 * 60 * 60 * 1000);
      
      expect(nextDate).toEqual(expectedDate);

      // 恢复原始Date
      global.Date = originalDate;
    });

    test('should handle zero interval', () => {
      const baseDate = new Date('2025-01-01T00:00:00Z');
      const originalDate = Date;
      global.Date = jest.fn(() => baseDate);
      global.Date.now = jest.fn(() => baseDate.getTime());

      const nextDate = ebbinghausService.calculateNextReviewDate(0);
      expect(nextDate).toEqual(baseDate);

      // 恢复原始Date
      global.Date = originalDate;
    });
  });

  describe('getDifficultyMultiplier', () => {
    test('should return correct multipliers for different difficulties', () => {
      expect(ebbinghausService.getDifficultyMultiplier('easy')).toBe(1.3);
      expect(ebbinghausService.getDifficultyMultiplier('medium')).toBe(1.0);
      expect(ebbinghausService.getDifficultyMultiplier('hard')).toBe(0.8);
    });

    test('should return default multiplier for unknown difficulty', () => {
      expect(ebbinghausService.getDifficultyMultiplier('unknown')).toBe(1.0);
    });
  });

  describe('adjustForDifficulty', () => {
    test('should adjust interval based on difficulty', () => {
      expect(ebbinghausService.adjustForDifficulty(10, 'easy')).toBe(13);
      expect(ebbinghausService.adjustForDifficulty(10, 'medium')).toBe(10);
      expect(ebbinghausService.adjustForDifficulty(10, 'hard')).toBe(8);
    });

    test('should round to nearest integer', () => {
      expect(ebbinghausService.adjustForDifficulty(7, 'easy')).toBe(9); // 7 * 1.3 = 9.1 -> 9
    });
  });

  describe('createInitialSchedule', () => {
    test('should create initial schedule with correct defaults', () => {
      const taskId = 'task123';
      const userId = 'user123';
      
      const schedule = ebbinghausService.createInitialSchedule(taskId, userId);
      
      expect(schedule.taskId).toBe(taskId);
      expect(schedule.userId).toBe(userId);
      expect(schedule.interval).toBe(1);
      expect(schedule.easeFactor).toBe(2.5);
      expect(schedule.repetitions).toBe(0);
      expect(schedule.nextReviewDate).toBeInstanceOf(Date);
    });
  });

  describe('updateScheduleAfterReview', () => {
    test('should update schedule correctly after review', () => {
      const currentSchedule = {
        interval: 1,
        easeFactor: 2.5,
        repetitions: 0
      };
      
      const updated = ebbinghausService.updateScheduleAfterReview(
        currentSchedule, 
        4, 
        'medium'
      );
      
      expect(updated.interval).toBe(6);
      expect(updated.repetitions).toBe(1);
      expect(updated.nextReviewDate).toBeInstanceOf(Date);
    });

    test('should apply difficulty adjustment', () => {
      const currentSchedule = {
        interval: 6,
        easeFactor: 2.5,
        repetitions: 1
      };
      
      const easyUpdate = ebbinghausService.updateScheduleAfterReview(
        currentSchedule, 
        4, 
        'easy'
      );
      
      const hardUpdate = ebbinghausService.updateScheduleAfterReview(
        currentSchedule, 
        4, 
        'hard'
      );
      
      expect(easyUpdate.interval).toBeGreaterThan(hardUpdate.interval);
    });
  });
});
