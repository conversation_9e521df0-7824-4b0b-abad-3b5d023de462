// tests/unit/taskService.test.js
const taskService = require('../../src/services/taskService');
const Task = require('../../src/models/Task');
const ReviewSchedule = require('../../src/models/ReviewSchedule');
const { createTestUser, createTestTask } = require('../helpers/testUtils');
const { testTasks } = require('../fixtures/testData');

// Mock dependencies
jest.mock('../../src/models/Task');
jest.mock('../../src/models/ReviewSchedule');
jest.mock('../../src/services/ebbinghausService');

describe('TaskService', () => {
  let mockUser;
  let mockTask;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockUser = {
      _id: 'user123',
      username: 'testuser'
    };
    
    mockTask = {
      _id: 'task123',
      userId: 'user123',
      title: 'Test Task',
      content: 'Test content',
      type: 'vocabulary',
      difficulty: 'medium',
      save: jest.fn().mockResolvedValue(true),
      toObject: jest.fn().mockReturnValue({
        _id: 'task123',
        title: 'Test Task'
      })
    };
  });

  describe('createTask', () => {
    test('should create task successfully', async () => {
      const taskData = testTasks.vocabularyTask;
      
      Task.mockImplementation(() => mockTask);
      
      const result = await taskService.createTask(mockUser._id, taskData);
      
      expect(Task).toHaveBeenCalledWith({
        userId: mockUser._id,
        ...taskData
      });
      expect(mockTask.save).toHaveBeenCalled();
      expect(result).toEqual(mockTask.toObject());
    });

    test('should handle task creation error', async () => {
      const taskData = testTasks.vocabularyTask;
      const error = new Error('Database error');
      
      Task.mockImplementation(() => ({
        save: jest.fn().mockRejectedValue(error)
      }));
      
      await expect(taskService.createTask(mockUser._id, taskData))
        .rejects.toThrow('Database error');
    });

    test('should validate required fields', async () => {
      const invalidTaskData = { content: 'No title' };
      
      Task.mockImplementation(() => ({
        save: jest.fn().mockRejectedValue(new Error('Title is required'))
      }));
      
      await expect(taskService.createTask(mockUser._id, invalidTaskData))
        .rejects.toThrow('Title is required');
    });
  });

  describe('getUserTasks', () => {
    test('should get user tasks with pagination', async () => {
      const mockTasks = [mockTask, { ...mockTask, _id: 'task456' }];
      
      Task.find = jest.fn().mockReturnValue({
        sort: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue(mockTasks)
          })
        })
      });
      
      Task.countDocuments = jest.fn().mockResolvedValue(2);
      
      const result = await taskService.getUserTasks(mockUser._id, {
        page: 1,
        limit: 10
      });
      
      expect(Task.find).toHaveBeenCalledWith({ userId: mockUser._id });
      expect(result.tasks).toEqual(mockTasks);
      expect(result.total).toBe(2);
      expect(result.page).toBe(1);
      expect(result.totalPages).toBe(1);
    });

    test('should filter tasks by type', async () => {
      Task.find = jest.fn().mockReturnValue({
        sort: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([mockTask])
          })
        })
      });
      
      Task.countDocuments = jest.fn().mockResolvedValue(1);
      
      await taskService.getUserTasks(mockUser._id, {
        type: 'vocabulary',
        page: 1,
        limit: 10
      });
      
      expect(Task.find).toHaveBeenCalledWith({
        userId: mockUser._id,
        type: 'vocabulary'
      });
    });

    test('should filter tasks by difficulty', async () => {
      Task.find = jest.fn().mockReturnValue({
        sort: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue([mockTask])
          })
        })
      });
      
      Task.countDocuments = jest.fn().mockResolvedValue(1);
      
      await taskService.getUserTasks(mockUser._id, {
        difficulty: 'medium',
        page: 1,
        limit: 10
      });
      
      expect(Task.find).toHaveBeenCalledWith({
        userId: mockUser._id,
        difficulty: 'medium'
      });
    });
  });

  describe('getTaskById', () => {
    test('should get task by id successfully', async () => {
      Task.findOne = jest.fn().mockResolvedValue(mockTask);
      
      const result = await taskService.getTaskById('task123', mockUser._id);
      
      expect(Task.findOne).toHaveBeenCalledWith({
        _id: 'task123',
        userId: mockUser._id
      });
      expect(result).toEqual(mockTask);
    });

    test('should return null for non-existent task', async () => {
      Task.findOne = jest.fn().mockResolvedValue(null);
      
      const result = await taskService.getTaskById('nonexistent', mockUser._id);
      
      expect(result).toBeNull();
    });

    test('should handle database error', async () => {
      const error = new Error('Database error');
      Task.findOne = jest.fn().mockRejectedValue(error);
      
      await expect(taskService.getTaskById('task123', mockUser._id))
        .rejects.toThrow('Database error');
    });
  });

  describe('updateTask', () => {
    test('should update task successfully', async () => {
      const updateData = { title: 'Updated Title' };
      const updatedTask = { ...mockTask, ...updateData };
      
      Task.findOneAndUpdate = jest.fn().mockResolvedValue(updatedTask);
      
      const result = await taskService.updateTask('task123', mockUser._id, updateData);
      
      expect(Task.findOneAndUpdate).toHaveBeenCalledWith(
        { _id: 'task123', userId: mockUser._id },
        updateData,
        { new: true, runValidators: true }
      );
      expect(result).toEqual(updatedTask);
    });

    test('should return null for non-existent task', async () => {
      Task.findOneAndUpdate = jest.fn().mockResolvedValue(null);
      
      const result = await taskService.updateTask('nonexistent', mockUser._id, {});
      
      expect(result).toBeNull();
    });
  });

  describe('deleteTask', () => {
    test('should delete task and related schedules successfully', async () => {
      Task.findOneAndDelete = jest.fn().mockResolvedValue(mockTask);
      ReviewSchedule.deleteMany = jest.fn().mockResolvedValue({ deletedCount: 1 });
      
      const result = await taskService.deleteTask('task123', mockUser._id);
      
      expect(Task.findOneAndDelete).toHaveBeenCalledWith({
        _id: 'task123',
        userId: mockUser._id
      });
      expect(ReviewSchedule.deleteMany).toHaveBeenCalledWith({
        taskId: 'task123'
      });
      expect(result).toEqual(mockTask);
    });

    test('should return null for non-existent task', async () => {
      Task.findOneAndDelete = jest.fn().mockResolvedValue(null);
      
      const result = await taskService.deleteTask('nonexistent', mockUser._id);
      
      expect(result).toBeNull();
      expect(ReviewSchedule.deleteMany).not.toHaveBeenCalled();
    });
  });

  describe('searchTasks', () => {
    test('should search tasks by title and content', async () => {
      const mockTasks = [mockTask];
      
      Task.find = jest.fn().mockReturnValue({
        sort: jest.fn().mockReturnValue({
          skip: jest.fn().mockReturnValue({
            limit: jest.fn().mockResolvedValue(mockTasks)
          })
        })
      });
      
      Task.countDocuments = jest.fn().mockResolvedValue(1);
      
      const result = await taskService.searchTasks(mockUser._id, 'test', {
        page: 1,
        limit: 10
      });
      
      expect(Task.find).toHaveBeenCalledWith({
        userId: mockUser._id,
        $or: [
          { title: { $regex: 'test', $options: 'i' } },
          { content: { $regex: 'test', $options: 'i' } },
          { tags: { $in: [/test/i] } }
        ]
      });
      expect(result.tasks).toEqual(mockTasks);
    });

    test('should handle empty search query', async () => {
      const result = await taskService.searchTasks(mockUser._id, '', {
        page: 1,
        limit: 10
      });
      
      expect(result.tasks).toEqual([]);
      expect(result.total).toBe(0);
    });
  });

  describe('getTaskStatistics', () => {
    test('should get task statistics', async () => {
      const mockStats = [
        { _id: 'vocabulary', count: 5 },
        { _id: 'formula', count: 3 }
      ];
      
      Task.aggregate = jest.fn().mockResolvedValue(mockStats);
      Task.countDocuments = jest.fn().mockResolvedValue(8);
      
      const result = await taskService.getTaskStatistics(mockUser._id);
      
      expect(result.total).toBe(8);
      expect(result.byType).toEqual(mockStats);
    });
  });
});
