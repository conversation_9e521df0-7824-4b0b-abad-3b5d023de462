// tests/helpers/testUtils.js
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const mongoose = require('mongoose');
const User = require('../../src/models/User');
const Task = require('../../src/models/Task');

/**
 * 创建测试用户
 */
const createTestUser = async (userData = {}) => {
  const defaultUser = {
    username: 'testuser',
    email: '<EMAIL>',
    password: 'password123',
    preferences: {
      dailyGoal: 5,
      reminderEnabled: true,
      timezone: 'Asia/Shanghai'
    }
  };

  const user = new User({ ...defaultUser, ...userData });
  await user.save();
  return user;
};

/**
 * 创建测试任务
 */
const createTestTask = async (userId, taskData = {}) => {
  const defaultTask = {
    userId,
    title: 'Test Task',
    content: 'This is a test task content',
    type: 'vocabulary',
    difficulty: 'medium',
    tags: ['test'],
    metadata: {
      source: 'test',
      category: 'general'
    }
  };

  const task = new Task({ ...defaultTask, ...taskData });
  await task.save();
  return task;
};

/**
 * 生成JWT令牌
 */
const generateToken = (userId) => {
  return jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: '1h' }
  );
};

/**
 * 生成认证头
 */
const getAuthHeader = (token) => {
  return { Authorization: `Bearer ${token}` };
};

/**
 * 创建认证用户和令牌
 */
const createAuthenticatedUser = async (userData = {}) => {
  const user = await createTestUser(userData);
  const token = generateToken(user._id);
  return {
    user: {
      ...user.toObject(),
      _id: user._id.toString()
    },
    token,
    authHeader: getAuthHeader(token)
  };
};

/**
 * 清理数据库
 */
const cleanDatabase = async () => {
  const collections = ['users', 'tasks', 'reviewschedules', 'learningrecords'];
  
  for (const collection of collections) {
    try {
      await mongoose.connection.db.collection(collection).deleteMany({});
    } catch (error) {
      // 忽略不存在的集合
    }
  }
};

/**
 * 等待指定时间
 */
const sleep = (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * 模拟日期
 */
const mockDate = (dateString) => {
  const mockDate = new Date(dateString);
  jest.spyOn(global, 'Date').mockImplementation(() => mockDate);
  Date.now = jest.fn(() => mockDate.getTime());
  return mockDate;
};

/**
 * 恢复日期模拟
 */
const restoreDate = () => {
  if (global.Date.mockRestore) {
    global.Date.mockRestore();
  }
  if (Date.now.mockRestore) {
    Date.now.mockRestore();
  }
};

/**
 * 验证错误响应格式
 */
const expectErrorResponse = (response, statusCode, errorCode = null) => {
  expect(response.status).toBe(statusCode);
  expect(response.body).toHaveProperty('success', false);
  expect(response.body).toHaveProperty('message');
  if (errorCode) {
    expect(response.body).toHaveProperty('code', errorCode);
  }
};

/**
 * 验证成功响应格式
 */
const expectSuccessResponse = (response, statusCode = 200) => {
  expect(response.status).toBe(statusCode);
  expect(response.body).toHaveProperty('success', true);
  expect(response.body).toHaveProperty('data');
};

module.exports = {
  createTestUser,
  createTestTask,
  generateToken,
  getAuthHeader,
  createAuthenticatedUser,
  cleanDatabase,
  sleep,
  mockDate,
  restoreDate,
  expectErrorResponse,
  expectSuccessResponse
};
