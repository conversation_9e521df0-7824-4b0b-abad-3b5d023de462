// tests/helpers/setup.js
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

// 全局测试设置
let mongoServer;

// 测试前设置
beforeAll(async () => {
  // 设置测试环境变量
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
  process.env.MONGODB_URI = 'mongodb://localhost:27017/ebbinghaus_learning_test';
  process.env.REDIS_URL = 'redis://localhost:6379';
  process.env.LOG_LEVEL = 'error'; // 减少测试时的日志输出
  
  // 启动内存数据库（可选，用于隔离测试）
  if (process.env.USE_MEMORY_DB === 'true') {
    mongoServer = await MongoMemoryServer.create();
    process.env.MONGODB_URI = mongoServer.getUri();
  }
});

// 测试后清理
afterAll(async () => {
  // 关闭数据库连接
  if (mongoose.connection.readyState !== 0) {
    await mongoose.connection.close();
  }
  
  // 关闭内存数据库
  if (mongoServer) {
    await mongoServer.stop();
  }
});

// 每个测试前清理
beforeEach(async () => {
  // 清理数据库（如果连接存在）
  if (mongoose.connection.readyState === 1) {
    const collections = mongoose.connection.collections;
    for (const key in collections) {
      await collections[key].deleteMany({});
    }
  }
});

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Jest 超时设置
jest.setTimeout(10000);
