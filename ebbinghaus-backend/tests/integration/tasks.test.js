// tests/integration/tasks.test.js
const request = require('supertest');
const app = require('../../src/app');
const Task = require('../../src/models/Task');
const { 
  createAuthenticatedUser, 
  createTestTask,
  expectErrorResponse, 
  expectSuccessResponse 
} = require('../helpers/testUtils');
const { apiTestData } = require('../fixtures/testData');

describe('Tasks API Integration Tests', () => {
  let authUser, authToken, authHeader;

  beforeEach(async () => {
    const auth = await createAuthenticatedUser();
    authUser = auth.user;
    authToken = auth.token;
    authHeader = auth.authHeader;
  });

  describe('POST /api/tasks', () => {
    test('should create task successfully', async () => {
      const taskData = apiTestData.tasks.createTask;
      
      const response = await request(app)
        .post('/api/tasks')
        .set(authHeader)
        .send(taskData);
      
      expectSuccessResponse(response, 201);
      expect(response.body.data.task.title).toBe(taskData.title);
      expect(response.body.data.task.userId).toBe(authUser._id.toString());
      expect(response.body.data.task.status).toBe('active');
    });

    test('should reject task creation without authentication', async () => {
      const response = await request(app)
        .post('/api/tasks')
        .send(apiTestData.tasks.createTask);
      
      expectErrorResponse(response, 401);
    });

    test('should reject task creation with invalid data', async () => {
      const response = await request(app)
        .post('/api/tasks')
        .set(authHeader)
        .send(apiTestData.tasks.invalidTask);
      
      expectErrorResponse(response, 400);
    });

    test('should create task with default values', async () => {
      const minimalTask = {
        title: 'Minimal Task',
        content: 'Basic content'
      };
      
      const response = await request(app)
        .post('/api/tasks')
        .set(authHeader)
        .send(minimalTask);
      
      expectSuccessResponse(response, 201);
      expect(response.body.data.task.type).toBe('general');
      expect(response.body.data.task.difficulty).toBe('medium');
      expect(response.body.data.task.tags).toEqual([]);
    });
  });

  describe('GET /api/tasks', () => {
    beforeEach(async () => {
      // Create test tasks
      await createTestTask(authUser._id, { title: 'Task 1', type: 'vocabulary' });
      await createTestTask(authUser._id, { title: 'Task 2', type: 'formula' });
      await createTestTask(authUser._id, { title: 'Task 3', difficulty: 'hard' });
    });

    test('should get user tasks with pagination', async () => {
      const response = await request(app)
        .get('/api/tasks')
        .set(authHeader)
        .query({ page: 1, limit: 2 });
      
      expectSuccessResponse(response);
      expect(response.body.data.tasks).toHaveLength(2);
      expect(response.body.data.pagination.total).toBe(3);
      expect(response.body.data.pagination.page).toBe(1);
      expect(response.body.data.pagination.totalPages).toBe(2);
    });

    test('should filter tasks by type', async () => {
      const response = await request(app)
        .get('/api/tasks')
        .set(authHeader)
        .query({ type: 'vocabulary' });
      
      expectSuccessResponse(response);
      expect(response.body.data.tasks).toHaveLength(1);
      expect(response.body.data.tasks[0].type).toBe('vocabulary');
    });

    test('should filter tasks by difficulty', async () => {
      const response = await request(app)
        .get('/api/tasks')
        .set(authHeader)
        .query({ difficulty: 'hard' });
      
      expectSuccessResponse(response);
      expect(response.body.data.tasks).toHaveLength(1);
      expect(response.body.data.tasks[0].difficulty).toBe('hard');
    });

    test('should sort tasks by creation date', async () => {
      const response = await request(app)
        .get('/api/tasks')
        .set(authHeader)
        .query({ sortBy: 'createdAt', sortOrder: 'desc' });
      
      expectSuccessResponse(response);
      const tasks = response.body.data.tasks;
      expect(new Date(tasks[0].createdAt)).toBeInstanceOf(Date);
    });

    test('should reject request without authentication', async () => {
      const response = await request(app)
        .get('/api/tasks');
      
      expectErrorResponse(response, 401);
    });
  });

  describe('GET /api/tasks/:id', () => {
    let testTask;

    beforeEach(async () => {
      testTask = await createTestTask(authUser._id);
    });

    test('should get task by id successfully', async () => {
      const response = await request(app)
        .get(`/api/tasks/${testTask._id}`)
        .set(authHeader);
      
      expectSuccessResponse(response);
      expect(response.body.data.task._id).toBe(testTask._id.toString());
      expect(response.body.data.task.title).toBe(testTask.title);
    });

    test('should reject request for non-existent task', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const response = await request(app)
        .get(`/api/tasks/${fakeId}`)
        .set(authHeader);
      
      expectErrorResponse(response, 404);
    });

    test('should reject request for invalid task id', async () => {
      const response = await request(app)
        .get('/api/tasks/invalid-id')
        .set(authHeader);
      
      expectErrorResponse(response, 400);
    });

    test('should reject request without authentication', async () => {
      const response = await request(app)
        .get(`/api/tasks/${testTask._id}`);
      
      expectErrorResponse(response, 401);
    });
  });

  describe('PUT /api/tasks/:id', () => {
    let testTask;

    beforeEach(async () => {
      testTask = await createTestTask(authUser._id);
    });

    test('should update task successfully', async () => {
      const updateData = apiTestData.tasks.updateTask;
      
      const response = await request(app)
        .put(`/api/tasks/${testTask._id}`)
        .set(authHeader)
        .send(updateData);
      
      expectSuccessResponse(response);
      expect(response.body.data.task.title).toBe(updateData.title);
      expect(response.body.data.task.content).toBe(updateData.content);
      expect(response.body.data.task.difficulty).toBe(updateData.difficulty);
    });

    test('should reject update for non-existent task', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const response = await request(app)
        .put(`/api/tasks/${fakeId}`)
        .set(authHeader)
        .send(apiTestData.tasks.updateTask);
      
      expectErrorResponse(response, 404);
    });

    test('should reject update with invalid data', async () => {
      const response = await request(app)
        .put(`/api/tasks/${testTask._id}`)
        .set(authHeader)
        .send({ title: '' }); // Empty title
      
      expectErrorResponse(response, 400);
    });

    test('should reject request without authentication', async () => {
      const response = await request(app)
        .put(`/api/tasks/${testTask._id}`)
        .send(apiTestData.tasks.updateTask);
      
      expectErrorResponse(response, 401);
    });
  });

  describe('DELETE /api/tasks/:id', () => {
    let testTask;

    beforeEach(async () => {
      testTask = await createTestTask(authUser._id);
    });

    test('should delete task successfully', async () => {
      const response = await request(app)
        .delete(`/api/tasks/${testTask._id}`)
        .set(authHeader);
      
      expectSuccessResponse(response);
      expect(response.body.data.task._id).toBe(testTask._id.toString());
      
      // Verify task is deleted
      const deletedTask = await Task.findById(testTask._id);
      expect(deletedTask).toBeNull();
    });

    test('should reject delete for non-existent task', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      const response = await request(app)
        .delete(`/api/tasks/${fakeId}`)
        .set(authHeader);
      
      expectErrorResponse(response, 404);
    });

    test('should reject request without authentication', async () => {
      const response = await request(app)
        .delete(`/api/tasks/${testTask._id}`);
      
      expectErrorResponse(response, 401);
    });
  });

  describe('GET /api/tasks/search', () => {
    beforeEach(async () => {
      await createTestTask(authUser._id, { 
        title: 'JavaScript Basics', 
        content: 'Learn JavaScript fundamentals',
        tags: ['programming', 'javascript']
      });
      await createTestTask(authUser._id, { 
        title: 'Python Tutorial', 
        content: 'Python programming guide',
        tags: ['programming', 'python']
      });
    });

    test('should search tasks by title', async () => {
      const response = await request(app)
        .get('/api/tasks/search')
        .set(authHeader)
        .query({ q: 'JavaScript' });
      
      expectSuccessResponse(response);
      expect(response.body.data.tasks).toHaveLength(1);
      expect(response.body.data.tasks[0].title).toContain('JavaScript');
    });

    test('should search tasks by content', async () => {
      const response = await request(app)
        .get('/api/tasks/search')
        .set(authHeader)
        .query({ q: 'programming' });
      
      expectSuccessResponse(response);
      expect(response.body.data.tasks.length).toBeGreaterThan(0);
    });

    test('should search tasks by tags', async () => {
      const response = await request(app)
        .get('/api/tasks/search')
        .set(authHeader)
        .query({ q: 'python' });
      
      expectSuccessResponse(response);
      expect(response.body.data.tasks).toHaveLength(1);
    });

    test('should return empty results for non-matching search', async () => {
      const response = await request(app)
        .get('/api/tasks/search')
        .set(authHeader)
        .query({ q: 'nonexistent' });
      
      expectSuccessResponse(response);
      expect(response.body.data.tasks).toHaveLength(0);
    });

    test('should reject search without query', async () => {
      const response = await request(app)
        .get('/api/tasks/search')
        .set(authHeader);
      
      expectErrorResponse(response, 400);
    });
  });

  describe('GET /api/tasks/statistics', () => {
    beforeEach(async () => {
      await createTestTask(authUser._id, { type: 'vocabulary' });
      await createTestTask(authUser._id, { type: 'vocabulary' });
      await createTestTask(authUser._id, { type: 'formula' });
    });

    test('should get task statistics', async () => {
      const response = await request(app)
        .get('/api/tasks/statistics')
        .set(authHeader);
      
      expectSuccessResponse(response);
      expect(response.body.data.statistics.total).toBe(3);
      expect(response.body.data.statistics.byType).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ _id: 'vocabulary', count: 2 }),
          expect.objectContaining({ _id: 'formula', count: 1 })
        ])
      );
    });

    test('should reject request without authentication', async () => {
      const response = await request(app)
        .get('/api/tasks/statistics');
      
      expectErrorResponse(response, 401);
    });
  });
});
