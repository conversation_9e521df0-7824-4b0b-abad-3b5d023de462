// tests/integration/auth.test.js
const request = require('supertest');
const app = require('../../src/app');
const User = require('../../src/models/User');
const { 
  createTestUser, 
  expectErrorResponse, 
  expectSuccessResponse 
} = require('../helpers/testUtils');
const { apiTestData, errorMessages } = require('../fixtures/testData');

describe('Auth API Integration Tests', () => {
  describe('POST /api/auth/register', () => {
    test('should register new user successfully', async () => {
      const userData = apiTestData.auth.validRegister;
      
      const response = await request(app)
        .post('/api/auth/register')
        .send(userData);
      
      expectSuccessResponse(response, 201);
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data.user.email).toBe(userData.email);
      expect(response.body.data.user).not.toHaveProperty('password');
    });

    test('should reject registration with existing email', async () => {
      // Create existing user
      await createTestUser({ email: '<EMAIL>' });
      
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          username: 'newuser',
          email: '<EMAIL>',
          password: 'password123'
        });
      
      expectErrorResponse(response, 400);
      expect(response.body.message).toContain('already exists');
    });

    test('should reject registration with invalid email', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          username: 'testuser',
          email: 'invalid-email',
          password: 'password123'
        });
      
      expectErrorResponse(response, 400);
    });

    test('should reject registration with short password', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          username: 'testuser',
          email: '<EMAIL>',
          password: '123'
        });
      
      expectErrorResponse(response, 400);
    });

    test('should reject registration with missing fields', async () => {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          email: '<EMAIL>'
          // Missing username and password
        });
      
      expectErrorResponse(response, 400);
    });
  });

  describe('POST /api/auth/login', () => {
    beforeEach(async () => {
      // Create test user for login tests
      await createTestUser({
        email: '<EMAIL>',
        password: 'password123'
      });
    });

    test('should login with valid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send(apiTestData.auth.validLogin);
      
      expectSuccessResponse(response);
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data.user.email).toBe(apiTestData.auth.validLogin.email);
    });

    test('should reject login with invalid email', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123'
        });
      
      expectErrorResponse(response, 401);
    });

    test('should reject login with invalid password', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrongpassword'
        });
      
      expectErrorResponse(response, 401);
    });

    test('should reject login with missing fields', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>'
          // Missing password
        });
      
      expectErrorResponse(response, 400);
    });
  });

  describe('POST /api/auth/logout', () => {
    test('should logout successfully with valid token', async () => {
      const { token } = await createTestUser();
      
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${token}`);
      
      expectSuccessResponse(response);
    });

    test('should reject logout without token', async () => {
      const response = await request(app)
        .post('/api/auth/logout');
      
      expectErrorResponse(response, 401);
    });

    test('should reject logout with invalid token', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', 'Bearer invalid-token');
      
      expectErrorResponse(response, 401);
    });
  });

  describe('GET /api/auth/me', () => {
    test('should get current user with valid token', async () => {
      const { user, token } = await createTestUser();
      
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${token}`);
      
      expectSuccessResponse(response);
      expect(response.body.data.user._id).toBe(user._id.toString());
      expect(response.body.data.user.email).toBe(user.email);
      expect(response.body.data.user).not.toHaveProperty('password');
    });

    test('should reject request without token', async () => {
      const response = await request(app)
        .get('/api/auth/me');
      
      expectErrorResponse(response, 401);
    });

    test('should reject request with invalid token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid-token');
      
      expectErrorResponse(response, 401);
    });
  });

  describe('PUT /api/auth/profile', () => {
    test('should update user profile successfully', async () => {
      const { token } = await createTestUser();
      
      const updateData = {
        preferences: {
          dailyGoal: 10,
          reminderEnabled: false,
          timezone: 'America/New_York'
        }
      };
      
      const response = await request(app)
        .put('/api/auth/profile')
        .set('Authorization', `Bearer ${token}`)
        .send(updateData);
      
      expectSuccessResponse(response);
      expect(response.body.data.user.preferences.dailyGoal).toBe(10);
      expect(response.body.data.user.preferences.reminderEnabled).toBe(false);
    });

    test('should reject profile update without token', async () => {
      const response = await request(app)
        .put('/api/auth/profile')
        .send({ preferences: { dailyGoal: 10 } });
      
      expectErrorResponse(response, 401);
    });
  });

  describe('POST /api/auth/change-password', () => {
    test('should change password successfully', async () => {
      const { token } = await createTestUser({ password: 'oldpassword' });
      
      const response = await request(app)
        .post('/api/auth/change-password')
        .set('Authorization', `Bearer ${token}`)
        .send({
          currentPassword: 'oldpassword',
          newPassword: 'newpassword123'
        });
      
      expectSuccessResponse(response);
    });

    test('should reject password change with wrong current password', async () => {
      const { token } = await createTestUser({ password: 'oldpassword' });
      
      const response = await request(app)
        .post('/api/auth/change-password')
        .set('Authorization', `Bearer ${token}`)
        .send({
          currentPassword: 'wrongpassword',
          newPassword: 'newpassword123'
        });
      
      expectErrorResponse(response, 400);
    });

    test('should reject password change without token', async () => {
      const response = await request(app)
        .post('/api/auth/change-password')
        .send({
          currentPassword: 'oldpassword',
          newPassword: 'newpassword123'
        });
      
      expectErrorResponse(response, 401);
    });
  });
});
