// tests/integration/health.test.js
const request = require('supertest');
const app = require('../../src/app');

describe('Health Check API', () => {
  test('should return health status', async () => {
    const response = await request(app)
      .get('/health');
    
    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty('status', 'healthy');
    expect(response.body).toHaveProperty('timestamp');
    expect(response.body).toHaveProperty('uptime');
    expect(response.body).toHaveProperty('memory');
    expect(response.body).toHaveProperty('services');
    expect(response.body).toHaveProperty('version');
    expect(response.body).toHaveProperty('environment');
  });

  test('should return API health status', async () => {
    const response = await request(app)
      .get('/api/health');
    
    expect(response.status).toBe(200);
    expect(response.body).toHaveProperty('success', true);
    expect(response.body).toHaveProperty('data');
    expect(response.body.data).toHaveProperty('status', 'healthy');
  });

  test('should handle 404 for non-existent routes', async () => {
    const response = await request(app)
      .get('/non-existent-route');
    
    expect(response.status).toBe(404);
  });

  test('should handle CORS headers', async () => {
    const response = await request(app)
      .get('/health');
    
    expect(response.headers).toHaveProperty('access-control-allow-origin');
  });

  test('should include security headers', async () => {
    const response = await request(app)
      .get('/health');
    
    // 检查一些基本的安全头
    expect(response.headers).toHaveProperty('x-content-type-options');
    expect(response.headers).toHaveProperty('x-frame-options');
  });
});
