{"name": "ebbinghaus-backend", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "build": "npm ci --only=production"}, "keywords": ["ebb<PERSON><PERSON>", "learning", "memory", "education", "api"], "author": "JYZS Team", "license": "MIT", "description": "艾宾浩斯记忆曲线学习管理系统后端API服务", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "ioredis": "^5.7.0", "js-yaml": "^4.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.17.0", "morgan": "^1.10.1", "rate-limit-redis": "^4.2.1", "redis": "^5.7.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.1.0", "@types/supertest": "^6.0.3", "eslint": "^9.32.0", "jest": "^30.0.5", "mongodb-memory-server": "^10.2.0", "nodemon": "^3.1.10", "prettier": "^3.6.2", "supertest": "^7.1.4", "typescript": "^5.9.2"}}