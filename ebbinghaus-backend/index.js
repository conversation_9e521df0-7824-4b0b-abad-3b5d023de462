// 艾宾浩斯记忆曲线学习管理系统后端API服务
// 基础服务器入口文件

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
require('dotenv').config();

// 导入配置和路由
const database = require('./src/config/database');
const authRoutes = require('./src/routes/auth');

const app = express();
const PORT = process.env.PORT || 3004;

// 基础中间件
app.use(helmet());
app.use(cors());
app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'ebbinghaus-backend',
    version: '1.0.0'
  });
});

// API路由
app.use('/api/auth', authRoutes);

// 根路径
app.get('/', (req, res) => {
  res.json({
    message: '艾宾浩斯记忆曲线学习管理系统后端API',
    status: 'running',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    endpoints: {
      auth: '/api/auth',
      health: '/health'
    }
  });
});

// 404 处理
app.use((req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `路径 ${req.originalUrl} 不存在`,
    timestamp: new Date().toISOString()
  });
});

// 错误处理
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    error: 'Internal Server Error',
    message: '服务器内部错误',
    timestamp: new Date().toISOString()
  });
});

// 启动服务器
async function startServer() {
  try {
    // 连接数据库
    await database.connect();
    console.log('✅ 数据库连接成功');

    // 启动HTTP服务器
    app.listen(PORT, () => {
      console.log(`🚀 艾宾浩斯后端服务器启动成功`);
      console.log(`📍 服务地址: http://localhost:${PORT}`);
      console.log(`🏥 健康检查: http://localhost:${PORT}/health`);
      console.log(`🔐 认证API: http://localhost:${PORT}/api/auth`);
      console.log(`⏰ 启动时间: ${new Date().toISOString()}`);
    });

  } catch (error) {
    console.error('❌ 服务器启动失败:', error.message);
    process.exit(1);
  }
}

// 优雅关闭处理
process.on('SIGINT', async () => {
  console.log('\n🛑 收到关闭信号，正在优雅关闭服务器...');
  try {
    await database.disconnect();
    console.log('✅ 数据库连接已关闭');
    process.exit(0);
  } catch (error) {
    console.error('❌ 关闭过程中发生错误:', error.message);
    process.exit(1);
  }
});

// 启动服务器
startServer();

module.exports = app;
