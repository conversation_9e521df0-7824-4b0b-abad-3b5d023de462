const jwt = require('jsonwebtoken');
require('dotenv').config();

/**
 * JWT工具类
 * 处理JWT令牌的生成、验证和刷新
 */
class JWTUtils {
  constructor() {
    this.secret = process.env.JWT_SECRET || 'ebbinghaus_jwt_secret_key_2025';
    this.expiresIn = process.env.JWT_EXPIRES_IN || '7d';
    this.refreshExpiresIn = process.env.JWT_REFRESH_EXPIRES_IN || '30d';
  }

  /**
   * 生成访问令牌
   * @param {Object} payload - 令牌载荷
   * @param {string} payload.userId - 用户ID
   * @param {string} payload.email - 用户邮箱
   * @param {string} payload.username - 用户名
   * @returns {string} JWT访问令牌
   */
  generateAccessToken(payload) {
    try {
      const tokenPayload = {
        userId: payload.userId,
        email: payload.email,
        username: payload.username,
        type: 'access',
        iat: Math.floor(Date.now() / 1000)
      };

      return jwt.sign(tokenPayload, this.secret, {
        expiresIn: this.expiresIn,
        issuer: 'ebbinghaus-backend',
        audience: 'ebbinghaus-frontend'
      });
    } catch (error) {
      throw new Error(`生成访问令牌失败: ${error.message}`);
    }
  }

  /**
   * 生成刷新令牌
   * @param {Object} payload - 令牌载荷
   * @param {string} payload.userId - 用户ID
   * @returns {string} JWT刷新令牌
   */
  generateRefreshToken(payload) {
    try {
      const tokenPayload = {
        userId: payload.userId,
        type: 'refresh',
        iat: Math.floor(Date.now() / 1000)
      };

      return jwt.sign(tokenPayload, this.secret, {
        expiresIn: this.refreshExpiresIn,
        issuer: 'ebbinghaus-backend',
        audience: 'ebbinghaus-frontend'
      });
    } catch (error) {
      throw new Error(`生成刷新令牌失败: ${error.message}`);
    }
  }

  /**
   * 生成令牌对（访问令牌 + 刷新令牌）
   * @param {Object} user - 用户对象
   * @returns {Object} 包含访问令牌和刷新令牌的对象
   */
  generateTokenPair(user) {
    try {
      const payload = {
        userId: user._id.toString(),
        email: user.email,
        username: user.username
      };

      const accessToken = this.generateAccessToken(payload);
      const refreshToken = this.generateRefreshToken({ userId: payload.userId });

      return {
        accessToken,
        refreshToken,
        expiresIn: this.expiresIn,
        tokenType: 'Bearer'
      };
    } catch (error) {
      throw new Error(`生成令牌对失败: ${error.message}`);
    }
  }

  /**
   * 验证访问令牌
   * @param {string} token - JWT令牌
   * @returns {Object} 解码后的令牌载荷
   */
  verifyAccessToken(token) {
    try {
      const decoded = jwt.verify(token, this.secret, {
        issuer: 'ebbinghaus-backend',
        audience: 'ebbinghaus-frontend'
      });

      if (decoded.type !== 'access') {
        throw new Error('无效的令牌类型');
      }

      return decoded;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new Error('访问令牌已过期');
      } else if (error.name === 'JsonWebTokenError') {
        throw new Error('无效的访问令牌');
      } else {
        throw new Error(`验证访问令牌失败: ${error.message}`);
      }
    }
  }

  /**
   * 验证刷新令牌
   * @param {string} token - JWT刷新令牌
   * @returns {Object} 解码后的令牌载荷
   */
  verifyRefreshToken(token) {
    try {
      const decoded = jwt.verify(token, this.secret, {
        issuer: 'ebbinghaus-backend',
        audience: 'ebbinghaus-frontend'
      });

      if (decoded.type !== 'refresh') {
        throw new Error('无效的令牌类型');
      }

      return decoded;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new Error('刷新令牌已过期');
      } else if (error.name === 'JsonWebTokenError') {
        throw new Error('无效的刷新令牌');
      } else {
        throw new Error(`验证刷新令牌失败: ${error.message}`);
      }
    }
  }

  /**
   * 解码令牌（不验证签名）
   * @param {string} token - JWT令牌
   * @returns {Object} 解码后的令牌载荷
   */
  decodeToken(token) {
    try {
      return jwt.decode(token, { complete: true });
    } catch (error) {
      throw new Error(`解码令牌失败: ${error.message}`);
    }
  }

  /**
   * 检查令牌是否即将过期
   * @param {string} token - JWT令牌
   * @param {number} thresholdMinutes - 阈值分钟数，默认30分钟
   * @returns {boolean} 是否即将过期
   */
  isTokenExpiringSoon(token, thresholdMinutes = 30) {
    try {
      const decoded = this.decodeToken(token);
      const now = Math.floor(Date.now() / 1000);
      const threshold = thresholdMinutes * 60;
      
      return (decoded.payload.exp - now) < threshold;
    } catch (error) {
      return true; // 如果无法解码，认为已过期
    }
  }

  /**
   * 从请求头中提取令牌
   * @param {string} authHeader - Authorization头部值
   * @returns {string|null} 提取的令牌
   */
  extractTokenFromHeader(authHeader) {
    if (!authHeader) {
      return null;
    }

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      return null;
    }

    return parts[1];
  }

  /**
   * 获取令牌剩余有效时间
   * @param {string} token - JWT令牌
   * @returns {number} 剩余秒数
   */
  getTokenRemainingTime(token) {
    try {
      const decoded = this.decodeToken(token);
      const now = Math.floor(Date.now() / 1000);
      return Math.max(0, decoded.payload.exp - now);
    } catch (error) {
      return 0;
    }
  }

  /**
   * 验证令牌格式
   * @param {string} token - JWT令牌
   * @returns {boolean} 是否为有效格式
   */
  isValidTokenFormat(token) {
    if (!token || typeof token !== 'string') {
      return false;
    }

    const parts = token.split('.');
    return parts.length === 3;
  }
}

// 创建单例实例
const jwtUtils = new JWTUtils();

module.exports = jwtUtils;
