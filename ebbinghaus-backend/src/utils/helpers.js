// src/utils/helpers.js

/**
 * 通用辅助函数
 * 提供项目中常用的工具函数
 */

const { v4: uuidv4 } = require('uuid');
const dayjs = require('dayjs');
const { REGEX_PATTERNS } = require('./constants');

/**
 * 生成UUID
 * @returns {string} UUID字符串
 */
const generateUUID = () => {
  return uuidv4();
};

/**
 * 验证邮箱格式
 * @param {string} email - 邮箱地址
 * @returns {boolean} 是否有效
 */
const isValidEmail = (email) => {
  return REGEX_PATTERNS.EMAIL.test(email);
};

/**
 * 验证密码强度
 * @param {string} password - 密码
 * @returns {boolean} 是否符合要求
 */
const isValidPassword = (password) => {
  return REGEX_PATTERNS.PASSWORD.test(password);
};

/**
 * 验证用户名格式
 * @param {string} username - 用户名
 * @returns {boolean} 是否有效
 */
const isValidUsername = (username) => {
  return REGEX_PATTERNS.USERNAME.test(username);
};

/**
 * 验证UUID格式
 * @param {string} uuid - UUID字符串
 * @returns {boolean} 是否有效
 */
const isValidUUID = (uuid) => {
  return REGEX_PATTERNS.UUID.test(uuid);
};

/**
 * 格式化日期
 * @param {Date|string} date - 日期
 * @param {string} format - 格式字符串
 * @returns {string} 格式化后的日期
 */
const formatDate = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
  return dayjs(date).format(format);
};

/**
 * 获取日期范围
 * @param {Date} startDate - 开始日期
 * @param {Date} endDate - 结束日期
 * @returns {Object} 日期范围对象
 */
const getDateRange = (startDate, endDate) => {
  return {
    start: dayjs(startDate).startOf('day').toDate(),
    end: dayjs(endDate).endOf('day').toDate()
  };
};

/**
 * 计算两个日期之间的天数差
 * @param {Date} date1 - 日期1
 * @param {Date} date2 - 日期2
 * @returns {number} 天数差
 */
const getDaysDifference = (date1, date2) => {
  return dayjs(date1).diff(dayjs(date2), 'day');
};

/**
 * 添加时间到日期
 * @param {Date} date - 基础日期
 * @param {number} amount - 数量
 * @param {string} unit - 单位 (minute, hour, day, week, month)
 * @returns {Date} 新日期
 */
const addTimeToDate = (date, amount, unit) => {
  return dayjs(date).add(amount, unit).toDate();
};

/**
 * 检查日期是否在范围内
 * @param {Date} date - 要检查的日期
 * @param {Date} startDate - 开始日期
 * @param {Date} endDate - 结束日期
 * @returns {boolean} 是否在范围内
 */
const isDateInRange = (date, startDate, endDate) => {
  const checkDate = dayjs(date);
  return checkDate.isAfter(dayjs(startDate)) && checkDate.isBefore(dayjs(endDate));
};

/**
 * 分钟转换为天数
 * @param {number} minutes - 分钟数
 * @returns {number} 天数（保留3位小数）
 */
const minutesToDays = (minutes) => {
  return Math.round((minutes / 1440) * 1000) / 1000;
};

/**
 * 天数转换为分钟
 * @param {number} days - 天数
 * @returns {number} 分钟数
 */
const daysToMinutes = (days) => {
  return Math.round(days * 1440);
};

/**
 * 生成随机字符串
 * @param {number} length - 长度
 * @param {string} charset - 字符集
 * @returns {string} 随机字符串
 */
const generateRandomString = (length = 8, charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789') => {
  let result = '';
  for (let i = 0; i < length; i++) {
    result += charset.charAt(Math.floor(Math.random() * charset.length));
  }
  return result;
};

/**
 * 深度克隆对象
 * @param {any} obj - 要克隆的对象
 * @returns {any} 克隆后的对象
 */
const deepClone = (obj) => {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime());
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item));
  }
  
  if (typeof obj === 'object') {
    const clonedObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key]);
      }
    }
    return clonedObj;
  }
};

/**
 * 移除对象中的空值
 * @param {Object} obj - 对象
 * @returns {Object} 清理后的对象
 */
const removeEmptyValues = (obj) => {
  const cleaned = {};
  for (const key in obj) {
    if (obj[key] !== null && obj[key] !== undefined && obj[key] !== '') {
      cleaned[key] = obj[key];
    }
  }
  return cleaned;
};

/**
 * 分页计算
 * @param {number} page - 页码
 * @param {number} limit - 每页数量
 * @returns {Object} 分页信息
 */
const calculatePagination = (page = 1, limit = 20) => {
  const pageNum = Math.max(1, parseInt(page));
  const limitNum = Math.max(1, Math.min(100, parseInt(limit)));
  const skip = (pageNum - 1) * limitNum;
  
  return {
    page: pageNum,
    limit: limitNum,
    skip
  };
};

/**
 * 构建分页响应
 * @param {Array} data - 数据数组
 * @param {number} totalCount - 总数量
 * @param {number} page - 当前页
 * @param {number} limit - 每页数量
 * @returns {Object} 分页响应对象
 */
const buildPaginationResponse = (data, totalCount, page, limit) => {
  const totalPages = Math.ceil(totalCount / limit);
  
  return {
    data,
    pagination: {
      page,
      limit,
      totalCount,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  };
};

/**
 * 延迟执行
 * @param {number} ms - 延迟毫秒数
 * @returns {Promise} Promise对象
 */
const delay = (ms) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * 重试执行函数
 * @param {Function} fn - 要执行的函数
 * @param {number} maxRetries - 最大重试次数
 * @param {number} delayMs - 重试间隔
 * @returns {Promise} Promise对象
 */
const retry = async (fn, maxRetries = 3, delayMs = 1000) => {
  let lastError;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      if (i < maxRetries) {
        await delay(delayMs);
      }
    }
  }
  
  throw lastError;
};

/**
 * 安全的JSON解析
 * @param {string} jsonString - JSON字符串
 * @param {any} defaultValue - 默认值
 * @returns {any} 解析结果
 */
const safeJsonParse = (jsonString, defaultValue = null) => {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    return defaultValue;
  }
};

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @returns {string} 格式化后的大小
 */
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 生成缓存键
 * @param {string} prefix - 前缀
 * @param {...any} parts - 键的组成部分
 * @returns {string} 缓存键
 */
const generateCacheKey = (prefix, ...parts) => {
  return [prefix, ...parts.map(part => String(part))].join(':');
};

module.exports = {
  generateUUID,
  isValidEmail,
  isValidPassword,
  isValidUsername,
  isValidUUID,
  formatDate,
  getDateRange,
  getDaysDifference,
  addTimeToDate,
  isDateInRange,
  minutesToDays,
  daysToMinutes,
  generateRandomString,
  deepClone,
  removeEmptyValues,
  calculatePagination,
  buildPaginationResponse,
  delay,
  retry,
  safeJsonParse,
  formatFileSize,
  generateCacheKey
};
