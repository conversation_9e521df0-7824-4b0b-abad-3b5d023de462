// src/utils/constants.js

/**
 * 系统常量定义
 * 包含应用程序中使用的所有常量值
 */

// HTTP状态码
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503
};

// 错误代码
const ERROR_CODES = {
  // 通用错误
  INVALID_INPUT: 'INVALID_INPUT',
  SERVER_ERROR: 'SERVER_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  
  // 认证相关错误
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  
  // 用户相关错误
  USER_EXISTS: 'USER_EXISTS',
  USER_NOT_FOUND: 'USER_NOT_FOUND',
  EMAIL_NOT_VERIFIED: 'EMAIL_NOT_VERIFIED',
  
  // 任务相关错误
  TASK_NOT_FOUND: 'TASK_NOT_FOUND',
  TASK_ALREADY_COMPLETED: 'TASK_ALREADY_COMPLETED',
  INVALID_TASK_STATUS: 'INVALID_TASK_STATUS',
  
  // 负载均衡错误
  LOAD_EXCEEDED: 'LOAD_EXCEEDED',
  INVALID_DATE: 'INVALID_DATE',
  
  // 复习相关错误
  REVIEW_NOT_FOUND: 'REVIEW_NOT_FOUND',
  REVIEW_ALREADY_COMPLETED: 'REVIEW_ALREADY_COMPLETED',
  INVALID_EFFECTIVENESS: 'INVALID_EFFECTIVENESS',
  
  // 数据库错误
  DATABASE_ERROR: 'DATABASE_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  DUPLICATE_KEY: 'DUPLICATE_KEY',
  
  // 文件上传错误
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  UPLOAD_FAILED: 'UPLOAD_FAILED'
};

// 学科分类
const SUBJECTS = {
  MATH: 'math',
  CHINESE: 'chinese',
  ENGLISH: 'english',
  PHYSICS: 'physics',
  CHEMISTRY: 'chemistry',
  BIOLOGY: 'biology',
  HISTORY: 'history',
  GEOGRAPHY: 'geography',
  POLITICS: 'politics'
};

// 任务状态
const TASK_STATUS = {
  PENDING: 'pending',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
};

// 复习状态
const REVIEW_STATUS = {
  SCHEDULED: 'scheduled',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  SKIPPED: 'skipped',
  OVERDUE: 'overdue'
};

// 负载等级
const LOAD_LEVELS = {
  LIGHT: 'light',
  MEDIUM: 'medium',
  HEAVY: 'heavy'
};

// 优先级等级
const PRIORITY_LEVELS = {
  VERY_LOW: 1,
  LOW: 2,
  MEDIUM: 3,
  HIGH: 4,
  VERY_HIGH: 5
};

// 难度等级
const DIFFICULTY_LEVELS = {
  VERY_EASY: 1,
  EASY: 2,
  MEDIUM: 3,
  HARD: 4,
  VERY_HARD: 5
};

// 复习效果评分
const EFFECTIVENESS_SCORES = {
  VERY_POOR: 1,
  POOR: 2,
  AVERAGE: 3,
  GOOD: 4,
  EXCELLENT: 5
};

// 艾宾浩斯记忆曲线间隔（分钟）
const EBBINGHAUS_INTERVALS = [
  5,        // 5分钟
  30,       // 30分钟
  720,      // 12小时
  1440,     // 1天
  4320,     // 3天
  10080,    // 1周
  20160,    // 2周
  43200,    // 1月
  86400     // 2月
];

// 效果调整系数
const EFFECTIVENESS_FACTORS = {
  [EFFECTIVENESS_SCORES.VERY_POOR]: 0.7,
  [EFFECTIVENESS_SCORES.POOR]: 0.85,
  [EFFECTIVENESS_SCORES.AVERAGE]: 1.0,
  [EFFECTIVENESS_SCORES.GOOD]: 1.2,
  [EFFECTIVENESS_SCORES.EXCELLENT]: 1.4
};

// 负载阈值（百分比）
const LOAD_THRESHOLDS = {
  LIGHT: 30,
  MEDIUM: 60,
  HEAVY: 100
};

// 默认配置值
const DEFAULT_CONFIG = {
  // 用户默认设置
  DEFAULT_ESTIMATED_TIME: 30, // 分钟
  DEFAULT_PRIORITY: PRIORITY_LEVELS.MEDIUM,
  DEFAULT_DIFFICULTY: DIFFICULTY_LEVELS.MEDIUM,
  DEFAULT_DAILY_STUDY_LIMIT: 120, // 分钟
  DEFAULT_MAX_DAILY_REVIEWS: 10,
  
  // 分页设置
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  
  // 文件上传限制
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif'],
  ALLOWED_AUDIO_TYPES: ['audio/mpeg', 'audio/wav', 'audio/ogg'],
  ALLOWED_ATTACHMENT_TYPES: ['application/pdf', 'text/plain', 'application/msword'],
  
  // 缓存过期时间（秒）
  CACHE_TTL: {
    USER_SESSION: 7 * 24 * 60 * 60, // 7天
    TASK_LIST: 5 * 60, // 5分钟
    REVIEW_SCHEDULE: 10 * 60, // 10分钟
    LOAD_BALANCE: 2 * 60, // 2分钟
    STATISTICS: 15 * 60 // 15分钟
  },
  
  // 限流配置
  RATE_LIMIT: {
    WINDOW_MS: 15 * 60 * 1000, // 15分钟
    MAX_REQUESTS: 100,
    CREATE_TASK_MAX: 5, // 每分钟最多创建5个任务
    LOGIN_MAX: 5, // 每15分钟最多登录5次
    REGISTER_MAX: 3 // 每小时最多注册3次
  },
  
  // JWT配置
  JWT: {
    EXPIRES_IN: '7d',
    REFRESH_EXPIRES_IN: '30d'
  },
  
  // 邮件配置
  EMAIL: {
    VERIFICATION_EXPIRES: 24 * 60 * 60 * 1000, // 24小时
    PASSWORD_RESET_EXPIRES: 60 * 60 * 1000 // 1小时
  }
};

// 正则表达式模式
const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/,
  USERNAME: /^[a-zA-Z0-9_]{3,50}$/,
  TIME_FORMAT: /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,
  REST_HOURS: /^([0-1]?[0-9]|2[0-3])-([0-1]?[0-9]|2[0-3])$/,
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
};

// API路径前缀
const API_PATHS = {
  BASE: '/api',
  AUTH: '/api/auth',
  TASKS: '/api/tasks',
  REVIEWS: '/api/reviews',
  ANALYTICS: '/api/analytics',
  USERS: '/api/users',
  HEALTH: '/health'
};

// 事件类型
const EVENT_TYPES = {
  USER_REGISTERED: 'user_registered',
  USER_LOGIN: 'user_login',
  USER_LOGOUT: 'user_logout',
  TASK_CREATED: 'task_created',
  TASK_UPDATED: 'task_updated',
  TASK_COMPLETED: 'task_completed',
  TASK_DELETED: 'task_deleted',
  REVIEW_STARTED: 'review_started',
  REVIEW_COMPLETED: 'review_completed',
  REVIEW_SKIPPED: 'review_skipped',
  LOAD_WARNING: 'load_warning',
  SYSTEM_ERROR: 'system_error'
};

// 导出所有常量
module.exports = {
  HTTP_STATUS,
  ERROR_CODES,
  SUBJECTS,
  TASK_STATUS,
  REVIEW_STATUS,
  LOAD_LEVELS,
  PRIORITY_LEVELS,
  DIFFICULTY_LEVELS,
  EFFECTIVENESS_SCORES,
  EBBINGHAUS_INTERVALS,
  EFFECTIVENESS_FACTORS,
  LOAD_THRESHOLDS,
  DEFAULT_CONFIG,
  REGEX_PATTERNS,
  API_PATHS,
  EVENT_TYPES
};
