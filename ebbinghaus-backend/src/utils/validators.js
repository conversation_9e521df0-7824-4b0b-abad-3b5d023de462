const { body, param, query } = require('express-validator');

/**
 * 验证器工具
 * 定义各种请求数据验证规则
 */

/**
 * 用户注册验证规则
 */
const registerValidation = [
  body('username')
    .trim()
    .isLength({ min: 2, max: 20 })
    .withMessage('用户名长度必须在2-20个字符之间')
    .matches(/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/)
    .withMessage('用户名只能包含字母、数字、下划线和中文'),
  
  body('email')
    .trim()
    .isEmail()
    .withMessage('请输入有效的邮箱地址')
    .normalizeEmail(),
  
  body('password')
    .isLength({ min: 6, max: 128 })
    .withMessage('密码长度必须在6-128个字符之间')
    .matches(/^(?=.*[a-zA-Z])(?=.*\d)/)
    .withMessage('密码必须包含至少一个字母和一个数字'),
  
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.password) {
        throw new Error('确认密码与密码不匹配');
      }
      return true;
    }),
  
  body('inviteCode')
    .optional()
    .trim()
    .isLength({ min: 6, max: 20 })
    .withMessage('邀请码长度必须在6-20个字符之间')
];

/**
 * 用户登录验证规则
 */
const loginValidation = [
  body('identifier')
    .trim()
    .notEmpty()
    .withMessage('请输入用户名或邮箱'),
  
  body('password')
    .notEmpty()
    .withMessage('请输入密码'),
  
  body('rememberMe')
    .optional()
    .isBoolean()
    .withMessage('记住我必须是布尔值')
];

/**
 * 刷新令牌验证规则
 */
const refreshTokenValidation = [
  body('refreshToken')
    .notEmpty()
    .withMessage('刷新令牌不能为空')
    .isJWT()
    .withMessage('无效的刷新令牌格式')
];

/**
 * 更新用户资料验证规则
 */
const updateProfileValidation = [
  body('nickname')
    .optional()
    .trim()
    .isLength({ min: 1, max: 30 })
    .withMessage('昵称长度必须在1-30个字符之间'),
  
  body('bio')
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage('个人简介最多200个字符'),
  
  body('avatar')
    .optional()
    .trim()
    .isURL()
    .withMessage('头像必须是有效的URL'),
  
  body('preferences')
    .optional()
    .isObject()
    .withMessage('用户偏好必须是对象'),
  
  body('preferences.language')
    .optional()
    .isIn(['zh-CN', 'en-US'])
    .withMessage('语言必须是zh-CN或en-US'),
  
  body('preferences.timezone')
    .optional()
    .trim()
    .notEmpty()
    .withMessage('时区不能为空'),
  
  body('preferences.theme')
    .optional()
    .isIn(['light', 'dark', 'auto'])
    .withMessage('主题必须是light、dark或auto'),
  
  body('preferences.notifications')
    .optional()
    .isObject()
    .withMessage('通知设置必须是对象'),
  
  body('preferences.notifications.email')
    .optional()
    .isBoolean()
    .withMessage('邮件通知设置必须是布尔值'),
  
  body('preferences.notifications.push')
    .optional()
    .isBoolean()
    .withMessage('推送通知设置必须是布尔值'),
  
  body('preferences.notifications.reviewReminder')
    .optional()
    .isBoolean()
    .withMessage('复习提醒设置必须是布尔值')
];

/**
 * 修改密码验证规则
 */
const changePasswordValidation = [
  body('currentPassword')
    .notEmpty()
    .withMessage('请输入当前密码'),
  
  body('newPassword')
    .isLength({ min: 6, max: 128 })
    .withMessage('新密码长度必须在6-128个字符之间')
    .matches(/^(?=.*[a-zA-Z])(?=.*\d)/)
    .withMessage('新密码必须包含至少一个字母和一个数字'),
  
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('确认密码与新密码不匹配');
      }
      return true;
    })
];

/**
 * 用户ID参数验证
 */
const userIdValidation = [
  param('userId')
    .isMongoId()
    .withMessage('无效的用户ID格式')
];

/**
 * 分页查询验证规则
 */
const paginationValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是大于0的整数'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须是1-100之间的整数'),
  
  query('sort')
    .optional()
    .trim()
    .notEmpty()
    .withMessage('排序字段不能为空'),
  
  query('order')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('排序方向必须是asc或desc')
];

/**
 * 搜索查询验证规则
 */
const searchValidation = [
  query('q')
    .optional()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('搜索关键词长度必须在1-100个字符之间'),
  
  query('fields')
    .optional()
    .trim()
    .notEmpty()
    .withMessage('搜索字段不能为空')
];

/**
 * 邮箱验证规则
 */
const emailValidation = [
  body('email')
    .trim()
    .isEmail()
    .withMessage('请输入有效的邮箱地址')
    .normalizeEmail()
];

/**
 * 密码重置验证规则
 */
const resetPasswordValidation = [
  body('token')
    .notEmpty()
    .withMessage('重置令牌不能为空'),
  
  body('newPassword')
    .isLength({ min: 6, max: 128 })
    .withMessage('新密码长度必须在6-128个字符之间')
    .matches(/^(?=.*[a-zA-Z])(?=.*\d)/)
    .withMessage('新密码必须包含至少一个字母和一个数字'),
  
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('确认密码与新密码不匹配');
      }
      return true;
    })
];

module.exports = {
  registerValidation,
  loginValidation,
  refreshTokenValidation,
  updateProfileValidation,
  changePasswordValidation,
  userIdValidation,
  paginationValidation,
  searchValidation,
  emailValidation,
  resetPasswordValidation
};
