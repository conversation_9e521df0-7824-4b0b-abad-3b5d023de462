const jwtUtils = require('../utils/jwt');
const User = require('../models/User');

/**
 * 认证中间件
 * 验证JWT令牌并设置用户信息
 */
const authenticate = async (req, res, next) => {
  try {
    // 从请求头中提取令牌
    const authHeader = req.headers.authorization;
    const token = jwtUtils.extractTokenFromHeader(authHeader);

    if (!token) {
      return res.status(401).json({
        success: false,
        message: '未提供访问令牌',
        code: 'NO_TOKEN'
      });
    }

    // 验证令牌格式
    if (!jwtUtils.isValidTokenFormat(token)) {
      return res.status(401).json({
        success: false,
        message: '无效的令牌格式',
        code: 'INVALID_TOKEN_FORMAT'
      });
    }

    // 验证访问令牌
    let decoded;
    try {
      decoded = jwtUtils.verifyAccessToken(token);
    } catch (error) {
      return res.status(401).json({
        success: false,
        message: error.message,
        code: 'TOKEN_VERIFICATION_FAILED'
      });
    }

    // 查找用户
    const user = await User.findById(decoded.userId);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户不存在',
        code: 'USER_NOT_FOUND'
      });
    }

    // 检查用户状态
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: '用户账户已被禁用',
        code: 'USER_INACTIVE'
      });
    }

    // 检查密码是否在令牌签发后被修改
    if (user.changedPasswordAfter(decoded.iat)) {
      return res.status(401).json({
        success: false,
        message: '密码已修改，请重新登录',
        code: 'PASSWORD_CHANGED'
      });
    }

    // 设置用户信息到请求对象
    req.user = user;
    req.token = token;
    req.tokenPayload = decoded;

    next();
  } catch (error) {
    console.error('认证中间件错误:', error);
    return res.status(500).json({
      success: false,
      message: '认证过程中发生错误',
      code: 'AUTH_ERROR'
    });
  }
};

/**
 * 可选认证中间件
 * 如果提供了令牌则验证，否则继续执行
 */
const optionalAuthenticate = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = jwtUtils.extractTokenFromHeader(authHeader);

    if (!token) {
      // 没有令牌，继续执行
      return next();
    }

    // 有令牌，尝试验证
    try {
      const decoded = jwtUtils.verifyAccessToken(token);
      const user = await User.findById(decoded.userId);
      
      if (user && user.isActive && !user.changedPasswordAfter(decoded.iat)) {
        req.user = user;
        req.token = token;
        req.tokenPayload = decoded;
      }
    } catch (error) {
      // 令牌验证失败，但不阻止请求继续
      console.warn('可选认证失败:', error.message);
    }

    next();
  } catch (error) {
    console.error('可选认证中间件错误:', error);
    next(); // 即使出错也继续执行
  }
};

/**
 * 权限检查中间件工厂
 * @param {string|Array} roles - 允许的角色
 */
const requireRoles = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '需要登录',
        code: 'LOGIN_REQUIRED'
      });
    }

    const userRoles = req.user.roles || ['user'];
    const allowedRoles = Array.isArray(roles) ? roles : [roles];

    const hasPermission = allowedRoles.some(role => userRoles.includes(role));

    if (!hasPermission) {
      return res.status(403).json({
        success: false,
        message: '权限不足',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: allowedRoles,
        current: userRoles
      });
    }

    next();
  };
};

/**
 * 检查用户是否为资源所有者或管理员
 */
const requireOwnershipOrAdmin = (resourceUserIdField = 'userId') => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '需要登录',
        code: 'LOGIN_REQUIRED'
      });
    }

    const userRoles = req.user.roles || ['user'];
    const isAdmin = userRoles.includes('admin') || userRoles.includes('moderator');
    
    // 管理员可以访问所有资源
    if (isAdmin) {
      return next();
    }

    // 检查资源所有权
    const resourceUserId = req.params[resourceUserIdField] || req.body[resourceUserIdField];
    const currentUserId = req.user._id.toString();

    if (resourceUserId !== currentUserId) {
      return res.status(403).json({
        success: false,
        message: '只能访问自己的资源',
        code: 'RESOURCE_ACCESS_DENIED'
      });
    }

    next();
  };
};

/**
 * 限制请求频率中间件
 */
const rateLimitByUser = (maxRequests = 100, windowMs = 15 * 60 * 1000) => {
  const userRequests = new Map();

  return (req, res, next) => {
    if (!req.user) {
      return next(); // 未登录用户不限制
    }

    const userId = req.user._id.toString();
    const now = Date.now();
    const windowStart = now - windowMs;

    // 获取用户请求记录
    let requests = userRequests.get(userId) || [];
    
    // 清理过期记录
    requests = requests.filter(timestamp => timestamp > windowStart);
    
    // 检查是否超过限制
    if (requests.length >= maxRequests) {
      return res.status(429).json({
        success: false,
        message: '请求过于频繁，请稍后再试',
        code: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil(windowMs / 1000)
      });
    }

    // 记录当前请求
    requests.push(now);
    userRequests.set(userId, requests);

    next();
  };
};

module.exports = {
  authenticate,
  optionalAuthenticate,
  requireRoles,
  requireOwnershipOrAdmin,
  rateLimitByUser
};
