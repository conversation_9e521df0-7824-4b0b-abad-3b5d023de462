const User = require('../models/User');
const jwtUtils = require('../utils/jwt');
const { validationResult } = require('express-validator');

/**
 * 认证控制器
 * 处理用户注册、登录、登出等认证相关操作
 */
class AuthController {
  /**
   * 用户注册
   */
  async register(req, res) {
    try {
      // 验证请求数据
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '请求数据验证失败',
          errors: errors.array()
        });
      }

      const { username, email, password, confirmPassword, inviteCode } = req.body;

      // 验证密码确认
      if (password !== confirmPassword) {
        return res.status(400).json({
          success: false,
          message: '密码和确认密码不匹配'
        });
      }

      // 检查用户是否已存在
      const existingUser = await User.findOne({
        $or: [{ email }, { username }]
      });

      if (existingUser) {
        const field = existingUser.email === email ? '邮箱' : '用户名';
        return res.status(409).json({
          success: false,
          message: `${field}已被使用`
        });
      }

      // 创建新用户
      const userData = {
        username,
        email,
        password,
        nickname: username // 默认昵称为用户名
      };

      // 如果有邀请码，可以在这里处理
      if (inviteCode) {
        // TODO: 验证邀请码逻辑
        userData.inviteCode = inviteCode;
      }

      const user = new User(userData);
      await user.save();

      // 生成令牌
      const tokens = jwtUtils.generateTokenPair(user);

      // 更新登录信息
      await user.updateLoginInfo(req.ip);

      // 返回成功响应
      res.status(201).json({
        success: true,
        message: '注册成功',
        data: {
          user: {
            userId: user._id,
            username: user.username,
            email: user.email,
            nickname: user.nickname,
            avatar: user.avatar,
            emailVerified: user.emailVerified,
            createdAt: user.createdAt
          },
          tokens
        }
      });

    } catch (error) {
      console.error('注册失败:', error);
      
      // 处理MongoDB重复键错误
      if (error.code === 11000) {
        const field = Object.keys(error.keyPattern)[0];
        const fieldName = field === 'email' ? '邮箱' : '用户名';
        return res.status(409).json({
          success: false,
          message: `${fieldName}已被使用`
        });
      }

      res.status(500).json({
        success: false,
        message: '注册失败，请稍后重试'
      });
    }
  }

  /**
   * 用户登录
   */
  async login(req, res) {
    try {
      // 验证请求数据
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '请求数据验证失败',
          errors: errors.array()
        });
      }

      const { identifier, password, rememberMe } = req.body;

      // 查找用户（支持邮箱或用户名登录）
      const user = await User.findByIdentifier(identifier);
      if (!user) {
        return res.status(401).json({
          success: false,
          message: '用户名或密码错误'
        });
      }

      // 检查用户状态
      if (!user.isActive) {
        return res.status(401).json({
          success: false,
          message: '账户已被禁用，请联系管理员'
        });
      }

      // 验证密码
      const isPasswordValid = await user.comparePassword(password);
      if (!isPasswordValid) {
        return res.status(401).json({
          success: false,
          message: '用户名或密码错误'
        });
      }

      // 生成令牌
      const tokens = jwtUtils.generateTokenPair(user);

      // 更新登录信息
      await user.updateLoginInfo(req.ip);

      // 返回成功响应
      res.status(200).json({
        success: true,
        message: '登录成功',
        data: {
          user: {
            userId: user._id,
            username: user.username,
            email: user.email,
            nickname: user.nickname,
            avatar: user.avatar,
            emailVerified: user.emailVerified,
            lastLoginAt: user.lastLoginAt,
            preferences: user.preferences
          },
          tokens
        }
      });

    } catch (error) {
      console.error('登录失败:', error);
      res.status(500).json({
        success: false,
        message: '登录失败，请稍后重试'
      });
    }
  }

  /**
   * 刷新令牌
   */
  async refreshToken(req, res) {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        return res.status(400).json({
          success: false,
          message: '缺少刷新令牌'
        });
      }

      // 验证刷新令牌
      let decoded;
      try {
        decoded = jwtUtils.verifyRefreshToken(refreshToken);
      } catch (error) {
        return res.status(401).json({
          success: false,
          message: error.message
        });
      }

      // 查找用户
      const user = await User.findById(decoded.userId);
      if (!user || !user.isActive) {
        return res.status(401).json({
          success: false,
          message: '用户不存在或已被禁用'
        });
      }

      // 生成新的令牌对
      const tokens = jwtUtils.generateTokenPair(user);

      res.status(200).json({
        success: true,
        message: '令牌刷新成功',
        data: { tokens }
      });

    } catch (error) {
      console.error('刷新令牌失败:', error);
      res.status(500).json({
        success: false,
        message: '刷新令牌失败'
      });
    }
  }

  /**
   * 用户登出
   */
  async logout(req, res) {
    try {
      // 这里可以实现令牌黑名单逻辑
      // 目前简单返回成功响应
      res.status(200).json({
        success: true,
        message: '登出成功'
      });

    } catch (error) {
      console.error('登出失败:', error);
      res.status(500).json({
        success: false,
        message: '登出失败'
      });
    }
  }

  /**
   * 获取当前用户信息
   */
  async getCurrentUser(req, res) {
    try {
      const user = req.user;

      res.status(200).json({
        success: true,
        message: '获取用户信息成功',
        data: {
          userId: user._id,
          username: user.username,
          email: user.email,
          nickname: user.nickname,
          avatar: user.avatar,
          bio: user.bio,
          emailVerified: user.emailVerified,
          lastLoginAt: user.lastLoginAt,
          preferences: user.preferences,
          stats: user.stats,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        }
      });

    } catch (error) {
      console.error('获取用户信息失败:', error);
      res.status(500).json({
        success: false,
        message: '获取用户信息失败'
      });
    }
  }

  /**
   * 更新用户资料
   */
  async updateProfile(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '请求数据验证失败',
          errors: errors.array()
        });
      }

      const { nickname, bio, avatar, preferences } = req.body;
      const user = req.user;

      // 更新用户信息
      if (nickname !== undefined) user.nickname = nickname;
      if (bio !== undefined) user.bio = bio;
      if (avatar !== undefined) user.avatar = avatar;
      if (preferences !== undefined) {
        user.preferences = { ...user.preferences, ...preferences };
      }

      await user.save();

      res.status(200).json({
        success: true,
        message: '资料更新成功',
        data: {
          userId: user._id,
          username: user.username,
          email: user.email,
          nickname: user.nickname,
          avatar: user.avatar,
          bio: user.bio,
          preferences: user.preferences,
          updatedAt: user.updatedAt
        }
      });

    } catch (error) {
      console.error('更新资料失败:', error);
      res.status(500).json({
        success: false,
        message: '更新资料失败'
      });
    }
  }

  /**
   * 修改密码
   */
  async changePassword(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: '请求数据验证失败',
          errors: errors.array()
        });
      }

      const { currentPassword, newPassword, confirmPassword } = req.body;
      const user = await User.findById(req.user._id).select('+password');

      // 验证当前密码
      const isCurrentPasswordValid = await user.comparePassword(currentPassword);
      if (!isCurrentPasswordValid) {
        return res.status(400).json({
          success: false,
          message: '当前密码错误'
        });
      }

      // 验证新密码确认
      if (newPassword !== confirmPassword) {
        return res.status(400).json({
          success: false,
          message: '新密码和确认密码不匹配'
        });
      }

      // 更新密码
      user.password = newPassword;
      await user.save();

      res.status(200).json({
        success: true,
        message: '密码修改成功，请重新登录'
      });

    } catch (error) {
      console.error('修改密码失败:', error);
      res.status(500).json({
        success: false,
        message: '修改密码失败'
      });
    }
  }
}

module.exports = new AuthController();
