const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
require('dotenv').config();

/**
 * 数据库配置和连接管理
 * 支持本地MongoDB Memory Server和外部MongoDB
 */
class Database {
  constructor() {
    this.connection = null;
    this.isConnected = false;
    this.mongoServer = null;
  }

  /**
   * 启动本地MongoDB Memory Server
   */
  async startLocalServer() {
    try {
      console.log('🚀 启动本地MongoDB服务器...');

      this.mongoServer = await MongoMemoryServer.create({
        instance: {
          dbName: 'ebbinghaus_local',
          port: 27018, // 使用不同端口避免冲突
          storageEngine: 'wiredTiger'
        },
        binary: {
          version: '6.0.0'
        }
      });

      const uri = this.mongoServer.getUri();
      console.log('✅ 本地MongoDB服务器启动成功');
      console.log('📍 服务器地址:', uri);
      console.log('🔌 端口:', this.mongoServer.instanceInfo?.port);
      console.log('💾 数据库名称:', this.mongoServer.instanceInfo?.dbName);

      return uri;
    } catch (error) {
      console.error('❌ 启动本地MongoDB服务器失败:', error.message);
      throw error;
    }
  }

  /**
   * 连接到MongoDB数据库
   */
  async connect() {
    try {
      let mongoUri;

      // 检查是否使用本地服务器
      const useLocalServer = process.env.USE_LOCAL_DB === 'true' || !process.env.MONGODB_URI;

      if (useLocalServer) {
        // 启动本地MongoDB Memory Server
        mongoUri = await this.startLocalServer();
        console.log('🏠 使用本地MongoDB Memory Server');
      } else {
        // 使用外部MongoDB
        mongoUri = process.env.MONGODB_URI;
        console.log('🌐 使用外部MongoDB服务器');
      }

      console.log('🔗 正在连接数据库...');
      console.log('📍 数据库地址:', mongoUri);

      const options = {
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000
      };

      this.connection = await mongoose.connect(mongoUri, options);
      this.isConnected = true;

      console.log('✅ 数据库连接成功');
      console.log('📊 数据库名称:', this.connection.connection.name);
      console.log('🏠 主机地址:', this.connection.connection.host);
      console.log('🔌 端口:', this.connection.connection.port);

      // 监听连接事件
      mongoose.connection.on('connected', () => {
        console.log('📡 Mongoose 连接已建立');
      });

      mongoose.connection.on('error', (err) => {
        console.error('❌ Mongoose 连接错误:', err);
        this.isConnected = false;
      });

      mongoose.connection.on('disconnected', () => {
        console.log('📴 Mongoose 连接已断开');
        this.isConnected = false;
      });

      return this.connection;
    } catch (error) {
      console.error('❌ 数据库连接失败:', error.message);
      this.isConnected = false;
      throw error;
    }
  }

  /**
   * 断开数据库连接
   */
  async disconnect() {
    try {
      if (this.connection) {
        await mongoose.disconnect();
        this.isConnected = false;
        console.log('📴 数据库连接已断开');
      }
    } catch (error) {
      console.error('❌ 断开数据库连接失败:', error.message);
      throw error;
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      readyState: mongoose.connection.readyState,
      host: mongoose.connection.host,
      port: mongoose.connection.port,
      name: mongoose.connection.name
    };
  }

  /**
   * 健康检查
   */
  async healthCheck() {
    try {
      if (!this.isConnected) {
        return { status: 'disconnected', message: '数据库未连接' };
      }

      // 执行简单的数据库操作来验证连接
      await mongoose.connection.db.admin().ping();
      
      return {
        status: 'healthy',
        message: '数据库连接正常',
        details: this.getConnectionStatus()
      };
    } catch (error) {
      return {
        status: 'error',
        message: '数据库连接异常',
        error: error.message
      };
    }
  }
}

// 创建单例实例
const database = new Database();

module.exports = database;
