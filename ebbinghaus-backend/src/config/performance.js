// src/config/performance.js
const compression = require('compression');
const helmet = require('helmet');
const environmentConfig = require('./environment');

/**
 * 性能优化配置
 */
class PerformanceConfig {
  constructor() {
    this.config = environmentConfig;
  }

  /**
   * 配置压缩中间件
   */
  getCompressionMiddleware() {
    const compressionConfig = this.config.getServerConfig().compression;
    
    if (!compressionConfig.enabled) {
      return (req, res, next) => next();
    }

    return compression({
      level: compressionConfig.level,
      threshold: 1024, // 只压缩大于1KB的响应
      filter: (req, res) => {
        // 不压缩已经压缩的内容
        if (req.headers['x-no-compression']) {
          return false;
        }
        
        // 使用默认过滤器
        return compression.filter(req, res);
      }
    });
  }

  /**
   * 配置安全头中间件
   */
  getSecurityMiddleware() {
    const securityConfig = this.config.getSecurityConfig();
    
    if (!securityConfig.helmet.enabled) {
      return (req, res, next) => next();
    }

    const helmetOptions = {
      contentSecurityPolicy: securityConfig.helmet.contentSecurityPolicy ? {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'"],
          fontSrc: ["'self'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"]
        }
      } : false,
      
      hsts: securityConfig.helmet.hsts.enabled ? {
        maxAge: securityConfig.helmet.hsts.maxAge,
        includeSubDomains: true,
        preload: true
      } : false,
      
      crossOriginEmbedderPolicy: false, // 避免与某些API冲突
      crossOriginResourcePolicy: { policy: "cross-origin" }
    };

    return helmet(helmetOptions);
  }

  /**
   * 配置CORS中间件
   */
  getCorsOptions() {
    const securityConfig = this.config.getSecurityConfig();
    
    return {
      origin: (origin, callback) => {
        // 允许没有origin的请求（如移动应用）
        if (!origin) return callback(null, true);
        
        // 检查origin是否在允许列表中
        const allowedOrigins = securityConfig.corsOrigin;
        if (allowedOrigins.includes('*') || allowedOrigins.includes(origin)) {
          return callback(null, true);
        }
        
        return callback(new Error('Not allowed by CORS'));
      },
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-API-Key'
      ],
      exposedHeaders: ['X-Total-Count', 'X-Page-Count'],
      maxAge: 86400 // 24小时
    };
  }

  /**
   * 配置请求解析中间件
   */
  getParsingMiddleware() {
    return {
      json: {
        limit: '10mb',
        strict: true,
        type: 'application/json'
      },
      urlencoded: {
        limit: '10mb',
        extended: true,
        parameterLimit: 1000
      }
    };
  }

  /**
   * 配置静态文件服务
   */
  getStaticFileOptions() {
    return {
      maxAge: this.config.isProduction() ? '1y' : '0',
      etag: true,
      lastModified: true,
      setHeaders: (res, path) => {
        // 为不同类型的文件设置不同的缓存策略
        if (path.endsWith('.html')) {
          res.setHeader('Cache-Control', 'no-cache');
        } else if (path.match(/\.(js|css|png|jpg|jpeg|gif|ico|svg)$/)) {
          res.setHeader('Cache-Control', 'public, max-age=31536000'); // 1年
        }
      }
    };
  }

  /**
   * 配置会话选项
   */
  getSessionOptions() {
    const securityConfig = this.config.getSecurityConfig();
    
    return {
      secret: securityConfig.sessionSecret,
      resave: false,
      saveUninitialized: false,
      rolling: true,
      cookie: {
        secure: this.config.isProduction(), // 生产环境使用HTTPS
        httpOnly: true,
        maxAge: 24 * 60 * 60 * 1000, // 24小时
        sameSite: 'strict'
      },
      name: 'ebbinghaus.sid' // 自定义会话名称
    };
  }

  /**
   * 配置MongoDB连接选项
   */
  getMongooseOptions() {
    const dbConfig = this.config.getDatabaseConfig();
    
    return {
      ...dbConfig.options,
      useNewUrlParser: true,
      useUnifiedTopology: true,
      bufferCommands: false,
      bufferMaxEntries: 0,
      connectTimeoutMS: 10000,
      socketTimeoutMS: 45000,
      family: 4, // 使用IPv4
      keepAlive: true,
      keepAliveInitialDelay: 300000
    };
  }

  /**
   * 配置Redis连接选项
   */
  getRedisOptions() {
    const redisConfig = this.config.getRedisConfig();
    
    return {
      ...redisConfig,
      connectTimeout: 10000,
      commandTimeout: 5000,
      retryDelayOnFailover: 100,
      enableReadyCheck: true,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      keepAlive: 30000
    };
  }

  /**
   * 获取集群配置
   */
  getClusterConfig() {
    const serverConfig = this.config.getServerConfig();
    
    return {
      enabled: serverConfig.cluster.enabled,
      workers: serverConfig.cluster.workers || require('os').cpus().length,
      respawn: true,
      silent: false
    };
  }

  /**
   * 配置进程优化
   */
  configureProcess() {
    // 设置进程标题
    process.title = 'ebbinghaus-backend';
    
    // 配置内存限制警告
    if (this.config.isProduction()) {
      const memoryThreshold = 512 * 1024 * 1024; // 512MB
      
      setInterval(() => {
        const memUsage = process.memoryUsage();
        if (memUsage.heapUsed > memoryThreshold) {
          console.warn('High memory usage detected:', {
            heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + 'MB',
            heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024) + 'MB',
            external: Math.round(memUsage.external / 1024 / 1024) + 'MB'
          });
        }
      }, 60000); // 每分钟检查一次
    }
    
    // 配置未捕获异常处理
    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      if (this.config.isProduction()) {
        process.exit(1);
      }
    });
    
    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      if (this.config.isProduction()) {
        process.exit(1);
      }
    });
    
    // 优雅关闭处理
    const gracefulShutdown = (signal) => {
      console.log(`Received ${signal}, shutting down gracefully...`);
      
      // 这里可以添加清理逻辑
      setTimeout(() => {
        console.log('Forced shutdown');
        process.exit(1);
      }, 10000); // 10秒后强制退出
    };
    
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  }

  /**
   * 获取性能监控配置
   */
  getMonitoringConfig() {
    return {
      enabled: this.config.get('PERFORMANCE_MONITORING', false, 'boolean'),
      metricsEnabled: this.config.get('METRICS_ENABLED', false, 'boolean'),
      healthCheckEnabled: this.config.get('HEALTH_CHECK_ENABLED', true, 'boolean'),
      responseTimeThreshold: 1000, // 1秒
      memoryThreshold: 512 * 1024 * 1024, // 512MB
      cpuThreshold: 80 // 80%
    };
  }
}

// 创建单例实例
const performanceConfig = new PerformanceConfig();

module.exports = performanceConfig;
