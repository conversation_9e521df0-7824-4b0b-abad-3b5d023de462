const express = require('express');
const authController = require('../controllers/authController');
const { authenticate } = require('../middleware/auth');
const {
  registerValidation,
  loginValidation,
  refreshTokenValidation,
  updateProfileValidation,
  changePasswordValidation
} = require('../utils/validators');

const router = express.Router();

/**
 * 认证路由
 * 处理用户注册、登录、登出等认证相关请求
 */

/**
 * @route   POST /auth/register
 * @desc    用户注册
 * @access  Public
 * @body    { username, email, password, confirmPassword, inviteCode? }
 */
router.post('/register', registerValidation, authController.register);

/**
 * @route   POST /auth/login
 * @desc    用户登录
 * @access  Public
 * @body    { identifier, password, rememberMe? }
 */
router.post('/login', loginValidation, authController.login);

/**
 * @route   POST /auth/refresh
 * @desc    刷新访问令牌
 * @access  Public
 * @body    { refreshToken }
 */
router.post('/refresh', refreshTokenValidation, authController.refreshToken);

/**
 * @route   POST /auth/logout
 * @desc    用户登出
 * @access  Private
 * @headers Authorization: Bearer <token>
 */
router.post('/logout', authenticate, authController.logout);

/**
 * @route   GET /auth/me
 * @desc    获取当前用户信息
 * @access  Private
 * @headers Authorization: Bearer <token>
 */
router.get('/me', authenticate, authController.getCurrentUser);

/**
 * @route   PUT /auth/profile
 * @desc    更新用户资料
 * @access  Private
 * @headers Authorization: Bearer <token>
 * @body    { nickname?, bio?, avatar?, preferences? }
 */
router.put('/profile', authenticate, updateProfileValidation, authController.updateProfile);

/**
 * @route   POST /auth/change-password
 * @desc    修改密码
 * @access  Private
 * @headers Authorization: Bearer <token>
 * @body    { currentPassword, newPassword, confirmPassword }
 */
router.post('/change-password', authenticate, changePasswordValidation, authController.changePassword);

/**
 * @route   GET /auth/status
 * @desc    检查认证状态
 * @access  Private
 * @headers Authorization: Bearer <token>
 */
router.get('/status', authenticate, (req, res) => {
  res.status(200).json({
    success: true,
    message: '认证状态正常',
    data: {
      authenticated: true,
      userId: req.user._id,
      username: req.user.username,
      email: req.user.email,
      tokenExpiry: req.tokenPayload.exp
    }
  });
});

/**
 * @route   GET /auth/health
 * @desc    认证服务健康检查
 * @access  Public
 */
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: '认证服务运行正常',
    data: {
      service: 'auth',
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    }
  });
});

module.exports = router;
