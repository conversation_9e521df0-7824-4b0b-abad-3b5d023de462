// src/routes/auth.js - 简化版本
const express = require('express');
const authController = require('../controllers/authController');

const router = express.Router();

// 用户注册
router.post('/register', authController.register);

// 用户登录
router.post('/login', authController.login);

// 刷新令牌
router.post('/refresh', authController.refreshToken);

// 用户登出
router.post('/logout', authController.logout);

// 获取当前用户信息
router.get('/me', authController.getCurrentUser);

// 更新用户资料
router.put('/profile', authController.updateProfile);

// 修改密码
router.post('/change-password', authController.changePassword);

module.exports = router;