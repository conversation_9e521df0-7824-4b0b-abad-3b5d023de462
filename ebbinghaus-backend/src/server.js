// src/server.js - 服务器启动文件 (最小核心版本)
const app = require('./app');

const PORT = process.env.PORT || 3004;

/**
 * 启动服务器
 */
async function startServer() {
  try {
    console.log('🎯 艾宾浩斯学习系统后端服务');
    console.log('📅 启动时间:', new Date().toLocaleString());
    console.log('🔧 Node.js版本:', process.version);
    console.log('💾 内存使用:', Math.round(process.memoryUsage().heapUsed / 1024 / 1024), 'MB');
    console.log('');
    // 启动服务器
    const server = app.listen(PORT, () => {
      console.log(`🚀 艾宾浩斯学习系统后端服务启动成功`);
      console.log(`📍 服务地址: http://localhost:${PORT}`);
      console.log(`💚 健康检查: http://localhost:${PORT}/health`);
      console.log(`🔧 环境: ${process.env.NODE_ENV || 'development'}`);
    });

    // 处理服务器错误
    server.on('error', (error) => {
      if (error.code === 'EADDRINUSE') {
        console.error(`❌ 端口 ${PORT} 已被占用，请检查是否有其他服务在运行`);
      } else {
        console.error('❌ 服务器错误:', error.message);
      }
      process.exit(1);
    });

  } catch (error) {
    console.error('❌ 应用启动失败:', error.message);
    process.exit(1);
  }
}

// 启动应用
if (require.main === module) {
  startServer();
}

module.exports = app;
