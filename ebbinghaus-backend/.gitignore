# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Upload directories
uploads/
public/uploads/

# Build directories
build/
dist/

# ===== JYZS 后端特定规则 =====
# FileScopeMCP 分析文件
FileScopeMCP-tree-*.json

# 数据库文件
*.db
*.sqlite
*.sqlite3
data/
db/

# 测试文件（保留主要的）
test-*.js
!test-minimal-server.js

# 备份和临时文件
*.backup
*.bak
*.old
*.tmp
*.temp
temp-*
scratch.*

# 性能分析
.clinic/
.0x/
flamegraph.html
perf.data*

# 安全文件
*.pem
*.key
*.cert
*.crt
secrets/
.secrets/

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z
