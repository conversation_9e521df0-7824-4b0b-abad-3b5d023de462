# 艾宾浩斯学习系统后端API

<div align="center">

![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)
![Express](https://img.shields.io/badge/Express-5.x-blue.svg)
![MongoDB](https://img.shields.io/badge/MongoDB-6.0+-green.svg)
![Redis](https://img.shields.io/badge/Redis-7.0+-red.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)

基于艾宾浩斯记忆曲线的智能学习管理系统后端服务

[快速开始](#快速开始) • [API文档](#api文档) • [部署指南](#部署指南) • [贡献指南](#贡献指南)

</div>

## 📋 目录

- [项目概述](#项目概述)
- [核心特性](#核心特性)
- [技术架构](#技术架构)
- [快速开始](#快速开始)
- [环境配置](#环境配置)
- [API文档](#api文档)
- [项目结构](#项目结构)
- [开发指南](#开发指南)
- [测试](#测试)
- [部署](#部署)
- [监控与日志](#监控与日志)
- [故障排除](#故障排除)
- [贡献指南](#贡献指南)
- [许可证](#许可证)

## 🎯 项目概述

艾宾浩斯学习系统后端是一个基于科学记忆曲线理论的智能学习管理平台，旨在通过算法优化学习效率，提供个性化的学习体验。

### 核心理念

- **科学记忆**: 基于艾宾浩斯遗忘曲线，智能安排复习时间
- **个性化学习**: 根据用户学习表现动态调整学习计划
- **数据驱动**: 通过学习数据分析，持续优化学习效果
- **用户体验**: 简洁易用的API设计，支持多端应用

## ✨ 核心特性

### 🧠 智能学习算法
- **艾宾浩斯记忆曲线**: 科学的复习时间计算
- **动态难度调整**: 根据学习效果自动调整任务难度
- **个性化推荐**: 基于用户偏好和学习历史的智能推荐
- **负载均衡**: 智能分配学习任务，避免学习过载

### 📚 学习管理
- **任务管理**: 完整的学习任务CRUD操作
- **复习计划**: 自动生成和管理复习计划
- **学习记录**: 详细的学习过程记录和分析
- **进度跟踪**: 实时学习进度监控和统计

### 🔐 安全与认证
- **JWT认证**: 安全的用户认证机制
- **权限控制**: 细粒度的API访问控制
- **数据加密**: 敏感数据加密存储
- **安全头**: 完整的HTTP安全头配置

### 📊 数据分析
- **学习统计**: 多维度学习数据统计
- **效果分析**: 学习效果评估和趋势分析
- **可视化数据**: 支持图表展示的数据格式
- **导出功能**: 学习数据导出和备份

### 🚀 性能与扩展
- **高性能**: 优化的数据库查询和缓存策略
- **可扩展**: 模块化架构，支持水平扩展
- **监控**: 完整的性能监控和日志系统
- **容错**: 健壮的错误处理和恢复机制

## 🏗️ 技术架构

### 核心技术栈

| 技术 | 版本 | 用途 |
|------|------|------|
| **Node.js** | 18+ LTS | 运行时环境 |
| **Express.js** | 5.x | Web应用框架 |
| **MongoDB** | 6.0+ | 主数据库 |
| **Redis** | 7.0+ | 缓存和会话存储 |
| **Mongoose** | 8.x | MongoDB ODM |
| **JWT** | - | 用户认证 |
| **Winston** | 3.x | 日志管理 |
| **Jest** | 29.x | 测试框架 |

### 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   移动端应用    │    │   第三方集成    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      API Gateway         │
                    │   (Express + 中间件)     │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │     业务逻辑层           │
                    │  (Controllers + Services) │
                    └─────────────┬─────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      数据访问层          │
                    │    (Models + ODM)        │
                    └─────────────┬─────────────┘
                                 │
          ┌──────────────────────┼──────────────────────┐
          │                      │                      │
    ┌─────┴─────┐         ┌─────┴─────┐         ┌─────┴─────┐
    │ MongoDB   │         │   Redis   │         │   文件    │
    │ (主数据)  │         │  (缓存)   │         │  (日志)   │
    └───────────┘         └───────────┘         └───────────┘
```

### 模块架构

- **配置层** (`config/`): 数据库、Redis、日志等配置
- **路由层** (`routes/`): API路由定义和参数验证
- **控制器层** (`controllers/`): 请求处理和响应格式化
- **服务层** (`services/`): 核心业务逻辑实现
- **数据层** (`models/`): 数据模型和数据库操作
- **中间件层** (`middleware/`): 认证、验证、错误处理等
- **工具层** (`utils/`): 通用工具函数和常量

## 🚀 快速开始

### 环境要求

在开始之前，请确保您的开发环境满足以下要求：

| 软件 | 版本要求 | 说明 |
|------|----------|------|
| **Node.js** | 18.0+ LTS | 推荐使用最新LTS版本 |
| **npm** | 9.0+ | 或使用 yarn 1.22+ |
| **MongoDB** | 6.0+ | 主数据库 |
| **Redis** | 7.0+ | 缓存服务（可选，但推荐） |
| **Git** | 2.0+ | 版本控制 |

### 安装步骤

#### 1. 克隆项目

```bash
git clone https://github.com/your-org/ebbinghaus-backend.git
cd ebbinghaus-backend
```

#### 2. 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

#### 3. 环境配置

```bash
# 复制环境配置文件
cp .env.development .env

# 编辑配置文件
nano .env  # 或使用您喜欢的编辑器
```

#### 4. 数据库设置

**MongoDB 设置：**

```bash
# 启动 MongoDB 服务
# macOS (使用 Homebrew)
brew services start mongodb-community

# Ubuntu/Debian
sudo systemctl start mongod

# Windows
net start MongoDB
```

**Redis 设置（可选）：**

```bash
# 启动 Redis 服务
# macOS (使用 Homebrew)
brew services start redis

# Ubuntu/Debian
sudo systemctl start redis-server

# Windows
redis-server
```

#### 5. 数据库初始化

```bash
# 运行数据库迁移
npm run migrate:init

# 创建初始数据（可选）
npm run seed
```

#### 6. 启动服务

```bash
# 智能启动（推荐）- 包含前置检查和自动健康验证
npm start

# 或者使用开发模式（相同功能）
npm run dev

# 生产模式启动
npm start -- --production
```

**启动功能特性**：
- ✅ 自动检查Node.js版本和项目文件
- ✅ 验证依赖包和环境配置
- ✅ 检测端口冲突并自动处理
- ✅ 验证MongoDB和Redis连接
- ✅ 自动执行健康检查
- ✅ Windows/Linux/macOS兼容

### 验证安装

服务启动后，您可以通过以下方式验证安装：

#### 1. 健康检查

```bash
curl http://localhost:3002/health
```

预期响应：
```json
{
  "status": "healthy",
  "timestamp": "2025-08-03T10:30:00.000Z",
  "uptime": 120.5,
  "services": {
    "database": {
      "connected": true,
      "responseTime": 15
    },
    "redis": {
      "connected": true,
      "responseTime": 2
    }
  }
}
```

#### 2. API文档

访问 Swagger API 文档：
```
http://localhost:3002/api/docs
```

#### 3. 基础API测试

```bash
# 获取API信息
curl http://localhost:3002/

# 用户注册测试
curl -X POST http://localhost:3002/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "Test123456"
  }'
```

### 常见问题

**Q: 端口冲突怎么办？**
A: 智能启动脚本会自动检测端口冲突。您也可以修改 `.env` 文件中的 `PORT` 配置：
```bash
# 修改 .env 文件中的端口
PORT=3003

# 然后重新启动
npm start
```

**Q: MongoDB 连接失败？**
A: 检查 MongoDB 服务是否启动，确认连接字符串配置正确：
```bash
# 检查 MongoDB 状态
mongosh --eval "db.adminCommand('ismaster')"
```

**Q: Redis 连接失败？**
A: Redis 是可选的，如果不需要可以在 `.env` 中禁用：

```bash
REDIS_ENABLED=false
```

## ⚙️ 环境配置

### 配置文件说明

项目使用 `.env` 文件进行环境配置，支持多环境配置：

- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置
- `.env.test` - 测试环境配置

### 核心配置项

#### 服务器配置

```bash
# 服务器配置
NODE_ENV=development          # 运行环境: development/production/test
PORT=3002                     # 服务端口
HOST=localhost                # 服务主机

# API配置
API_VERSION=v1                # API版本
API_PREFIX=/api               # API路径前缀
```

#### 数据库配置

```bash
# MongoDB 配置
MONGODB_URI=mongodb://localhost:27017/ebbinghaus_learning_dev
MONGODB_TEST_URI=mongodb://localhost:27017/ebbinghaus_learning_test

# MongoDB 连接池配置
MONGODB_MAX_POOL_SIZE=10      # 最大连接数
MONGODB_MIN_POOL_SIZE=2       # 最小连接数
MONGODB_MAX_IDLE_TIME=30000   # 最大空闲时间(ms)
MONGODB_SERVER_SELECTION_TIMEOUT=5000  # 服务器选择超时(ms)

# Redis 配置
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=1                    # Redis 数据库编号
REDIS_PASSWORD=               # Redis 密码（如果有）
REDIS_MAX_RETRIES=3           # 最大重试次数
```

#### 安全配置

```bash
# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=24h            # 访问令牌过期时间
JWT_REFRESH_EXPIRES_IN=7d     # 刷新令牌过期时间

# 密码加密
BCRYPT_ROUNDS=12              # bcrypt 加密轮数

# CORS 配置
CORS_ORIGIN=http://localhost:3000,http://localhost:3001
TRUSTED_PROXIES=127.0.0.1,::1
```

#### 功能开关

```bash
# 功能特性开关
FEATURE_USER_REGISTRATION=true     # 用户注册功能
FEATURE_EMAIL_VERIFICATION=false   # 邮箱验证功能
FEATURE_PASSWORD_RESET=true        # 密码重置功能
FEATURE_ANALYTICS=true             # 数据分析功能
FEATURE_NOTIFICATIONS=false        # 通知功能

# API 功能
API_DOCS_ENABLED=true              # API文档功能
API_RATE_LIMIT_ENABLED=true        # API限流功能
```

#### 日志配置

```bash
# 日志配置
LOG_LEVEL=debug               # 日志级别: error/warn/info/debug
LOG_FILE=true                 # 是否写入文件
LOG_DIR=./logs                # 日志目录
LOG_MAX_SIZE=20m              # 单个日志文件最大大小
LOG_MAX_FILES=7d              # 日志文件保留时间
```

### 环境变量优先级

配置加载优先级（从高到低）：

1. 系统环境变量
2. `.env.local` 文件（不提交到版本控制）
3. `.env.{NODE_ENV}` 文件
4. `.env` 文件
5. 默认配置

## 📚 API文档

### 在线文档

启动服务后，可以通过以下地址访问完整的API文档：

- **Swagger UI**: `http://localhost:3002/api/docs`
- **OpenAPI JSON**: `http://localhost:3002/api/docs/json`
- **OpenAPI YAML**: `http://localhost:3002/api/docs/yaml`

### API概览

#### 认证接口

| 方法 | 路径 | 描述 |
|------|------|------|
| POST | `/api/auth/register` | 用户注册 |
| POST | `/api/auth/login` | 用户登录 |
| POST | `/api/auth/logout` | 用户登出 |
| POST | `/api/auth/refresh` | 刷新令牌 |
| GET | `/api/auth/profile` | 获取用户信息 |
| PUT | `/api/auth/profile` | 更新用户信息 |

#### 任务管理接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/tasks` | 获取任务列表 |
| POST | `/api/tasks` | 创建新任务 |
| GET | `/api/tasks/:id` | 获取任务详情 |
| PUT | `/api/tasks/:id` | 更新任务 |
| DELETE | `/api/tasks/:id` | 删除任务 |
| GET | `/api/tasks/search` | 搜索任务 |
| GET | `/api/tasks/stats` | 获取任务统计 |

#### 复习管理接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/reviews` | 获取复习计划 |
| GET | `/api/reviews/today` | 获取今日复习 |
| GET | `/api/reviews/upcoming` | 获取即将到期的复习 |
| POST | `/api/reviews/:id/complete` | 完成复习 |
| POST | `/api/reviews/:id/skip` | 跳过复习 |
| GET | `/api/reviews/history` | 获取复习历史 |

#### 数据分析接口

| 方法 | 路径 | 描述 |
|------|------|------|
| GET | `/api/analytics/overview` | 获取学习概览 |
| GET | `/api/analytics/progress` | 获取学习进度 |
| GET | `/api/analytics/performance` | 获取学习表现 |
| GET | `/api/analytics/trends` | 获取学习趋势 |
| GET | `/api/analytics/heatmap` | 获取学习热力图 |

### 认证方式

API 使用 JWT (JSON Web Token) 进行认证：

```bash
# 请求头格式
Authorization: Bearer <your-jwt-token>
```

### 响应格式

#### 成功响应

```json
{
  "success": true,
  "data": {
    // 实际数据内容
  },
  "message": "操作成功",
  "timestamp": "2025-08-03T10:30:00.000Z"
}
```

#### 分页响应

```json
{
  "success": true,
  "data": [
    // 数据数组
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "pages": 10
  }
}
```

#### 错误响应

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": [
      "邮箱格式不正确",
      "密码长度至少6位"
    ]
  },
  "timestamp": "2025-08-03T10:30:00.000Z"
}
```

### 错误码说明

| 错误码 | HTTP状态 | 描述 |
|--------|----------|------|
| `VALIDATION_ERROR` | 400 | 请求参数验证失败 |
| `AUTHENTICATION_ERROR` | 401 | 认证失败 |
| `AUTHORIZATION_ERROR` | 403 | 权限不足 |
| `RESOURCE_NOT_FOUND` | 404 | 资源不存在 |
| `RESOURCE_CONFLICT` | 409 | 资源冲突 |
| `RATE_LIMIT_EXCEEDED` | 429 | 请求频率超限 |
| `INTERNAL_SERVER_ERROR` | 500 | 服务器内部错误 |

## 📁 项目结构

```
ebbinghaus-backend/
├── src/                          # 源代码目录
│   ├── config/                   # 配置文件
│   │   ├── database.js           # 数据库配置
│   │   ├── redis.js              # Redis配置
│   │   ├── logger.js             # 日志配置
│   │   ├── environment.js        # 环境配置管理
│   │   ├── performance.js        # 性能优化配置
│   │   └── swagger.js            # API文档配置
│   ├── controllers/              # API控制器
│   │   ├── authController.js     # 认证控制器
│   │   ├── taskController.js     # 任务控制器
│   │   ├── reviewController.js   # 复习控制器
│   │   └── analyticsController.js # 分析控制器
│   ├── services/                 # 业务服务层
│   │   ├── authService.js        # 认证服务
│   │   ├── taskService.js        # 任务服务
│   │   ├── ebbinghausService.js  # 艾宾浩斯算法服务
│   │   └── loadBalanceService.js # 负载均衡服务
│   ├── models/                   # 数据模型
│   │   ├── User.js               # 用户模型
│   │   ├── Task.js               # 任务模型
│   │   ├── ReviewSchedule.js     # 复习计划模型
│   │   └── LearningRecord.js     # 学习记录模型
│   ├── routes/                   # 路由定义
│   │   ├── auth.js               # 认证路由
│   │   ├── tasks.js              # 任务路由
│   │   ├── reviews.js            # 复习路由
│   │   ├── analytics.js          # 分析路由
│   │   └── docs.js               # 文档路由
│   ├── middleware/               # 中间件
│   │   ├── auth.js               # 认证中间件
│   │   ├── validation.js         # 数据验证中间件
│   │   ├── errorHandler.js       # 错误处理中间件
│   │   └── rateLimit.js          # 限流中间件
│   ├── utils/                    # 工具函数
│   │   ├── constants.js          # 常量定义
│   │   └── helpers.js            # 辅助函数
│   ├── app.js                    # Express应用配置
│   └── server.js                 # 服务器启动文件
├── tests/                        # 测试目录
│   ├── unit/                     # 单元测试
│   │   ├── services/             # 服务层测试
│   │   ├── models/               # 模型测试
│   │   └── utils/                # 工具函数测试
│   ├── integration/              # 集成测试
│   │   ├── auth.test.js          # 认证接口测试
│   │   ├── tasks.test.js         # 任务接口测试
│   │   └── reviews.test.js       # 复习接口测试
│   ├── fixtures/                 # 测试数据
│   └── helpers/                  # 测试工具
├── scripts/                      # 脚本目录
│   ├── deploy.sh                 # 部署脚本
│   ├── start-production.sh       # 生产启动脚本
│   ├── migrate.js                # 数据库迁移
│   ├── backup.js                 # 备份脚本
│   ├── restore.js                # 恢复脚本
│   └── validate-swagger.js       # 文档验证脚本
├── docs/                         # 文档目录
│   ├── API.md                    # API详细文档
│   ├── 后端接口.md               # 前端对接文档
│   ├── API快速参考.md            # API快速参考
│   ├── SWAGGER_SETUP.md          # Swagger配置指南
│   └── PRODUCTION.md             # 生产环境指南
├── logs/                         # 日志目录
├── uploads/                      # 上传文件目录
├── backups/                      # 备份目录
├── coverage/                     # 测试覆盖率报告
├── .env.development              # 开发环境变量
├── .env.production               # 生产环境变量
├── .env.test                     # 测试环境变量
├── .gitignore                    # Git忽略文件
├── .eslintrc.js                  # ESLint配置
├── .prettierrc                   # Prettier配置
├── jest.config.js                # Jest测试配置
├── ecosystem.config.js           # PM2配置
├── package.json                  # 项目配置
├── package-lock.json             # 依赖锁定文件
├── tsconfig.json                 # TypeScript配置
└── README.md                     # 项目说明文档
```

### 核心目录说明

#### `src/` 源代码目录

- **`config/`**: 应用配置文件，包括数据库、缓存、日志等配置
- **`controllers/`**: API控制器，处理HTTP请求和响应
- **`services/`**: 业务逻辑层，包含核心算法和业务处理
- **`models/`**: 数据模型定义，使用Mongoose ODM
- **`routes/`**: 路由定义，API端点配置
- **`middleware/`**: 中间件函数，处理认证、验证、错误等
- **`utils/`**: 通用工具函数和常量定义

#### `tests/` 测试目录

- **`unit/`**: 单元测试，测试单个函数或模块
- **`integration/`**: 集成测试，测试API端点和业务流程
- **`fixtures/`**: 测试数据和模拟数据
- **`helpers/`**: 测试辅助函数和工具

#### `scripts/` 脚本目录

- **`deploy.sh`**: 自动化部署脚本
- **`migrate.js`**: 数据库迁移脚本
- **`backup.js`**: 数据备份脚本
- **`restore.js`**: 数据恢复脚本

#### `docs/` 文档目录

- **`API.md`**: 详细的API文档
- **`后端接口.md`**: 前端开发者对接指南
- **`PRODUCTION.md`**: 生产环境部署指南

## 🛠️ 开发指南

### 开发环境设置

#### 1. 代码编辑器配置

推荐使用 VS Code，并安装以下扩展：

```json
{
  "recommendations": [
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-json",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next"
  ]
}
```

#### 2. Git Hooks 设置

```bash
# 安装 husky
npm install --save-dev husky

# 设置 pre-commit hook
npx husky add .husky/pre-commit "npm run lint && npm run test"

# 设置 commit-msg hook
npx husky add .husky/commit-msg "npx commitlint --edit $1"
```

### 编码规范

#### 1. 代码风格

项目使用 ESLint + Prettier 进行代码格式化：

```bash
# 检查代码风格
npm run lint

# 自动修复代码风格
npm run lint:fix

# 格式化代码
npm run format
```

#### 2. 提交规范

使用 Conventional Commits 规范：

```bash
# 功能添加
git commit -m "feat: 添加用户认证功能"

# Bug修复
git commit -m "fix: 修复登录状态检查问题"

# 文档更新
git commit -m "docs: 更新API文档"

# 代码重构
git commit -m "refactor: 重构任务服务层代码"

# 性能优化
git commit -m "perf: 优化数据库查询性能"

# 测试添加
git commit -m "test: 添加用户服务单元测试"
```

#### 3. 命名规范

```javascript
// 文件命名：camelCase
userService.js
authController.js

// 类命名：PascalCase
class UserService {}
class TaskModel {}

// 函数命名：camelCase
function getUserById() {}
const createTask = () => {}

// 常量命名：UPPER_SNAKE_CASE
const API_BASE_URL = '/api';
const MAX_RETRY_COUNT = 3;

// 变量命名：camelCase
const userName = 'john';
const taskList = [];
```

### API开发流程

#### 1. 创建新的API端点

```bash
# 1. 定义数据模型 (如果需要)
# src/models/NewModel.js

# 2. 创建服务层
# src/services/newService.js

# 3. 创建控制器
# src/controllers/newController.js

# 4. 定义路由
# src/routes/new.js

# 5. 添加Swagger文档注释

# 6. 编写测试
# tests/unit/services/newService.test.js
# tests/integration/new.test.js
```

#### 2. 数据验证

使用 express-validator 进行数据验证：

```javascript
// src/middleware/validation.js
const { body, validationResult } = require('express-validator');

const validateCreateTask = [
  body('title')
    .notEmpty()
    .withMessage('任务标题不能为空')
    .isLength({ max: 100 })
    .withMessage('任务标题不能超过100个字符'),

  body('content')
    .notEmpty()
    .withMessage('任务内容不能为空'),

  body('difficulty')
    .isIn(['easy', 'medium', 'hard'])
    .withMessage('难度必须是 easy、medium 或 hard'),

  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '请求参数验证失败',
          details: errors.array().map(err => err.msg)
        }
      });
    }
    next();
  }
];
```

#### 3. 错误处理

统一的错误处理机制：

```javascript
// src/utils/errors.js
class AppError extends Error {
  constructor(message, statusCode, code = null) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

// 使用示例
throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
```

## 🧪 测试

### 测试策略

项目采用多层次的测试策略：

- **单元测试**: 测试单个函数和模块
- **集成测试**: 测试API端点和业务流程
- **端到端测试**: 测试完整的用户场景

### 运行测试

```bash
# 运行所有测试
npm test

# 运行单元测试
npm run test:unit

# 运行集成测试
npm run test:integration

# 运行测试并生成覆盖率报告
npm run test:coverage

# 监听模式运行测试
npm run test:watch

# 运行特定测试文件
npm test -- --testPathPattern=auth

# 运行特定测试用例
npm test -- --testNamePattern="should create user"
```

### 测试配置

Jest 配置文件 `jest.config.js`：

```javascript
module.exports = {
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/tests/helpers/setup.js'],
  testMatch: [
    '<rootDir>/tests/**/*.test.js'
  ],
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/server.js',
    '!src/config/**',
    '!**/node_modules/**'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  coverageReporters: ['text', 'lcov', 'html']
};
```

### 编写测试

#### 单元测试示例

```javascript
// tests/unit/services/userService.test.js
const UserService = require('../../../src/services/userService');
const User = require('../../../src/models/User');

jest.mock('../../../src/models/User');

describe('UserService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createUser', () => {
    it('should create user successfully', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
      };

      const mockUser = { _id: 'user123', ...userData };
      User.create.mockResolvedValue(mockUser);

      const result = await UserService.createUser(userData);

      expect(User.create).toHaveBeenCalledWith(userData);
      expect(result).toEqual(mockUser);
    });

    it('should throw error for duplicate email', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
      };

      User.create.mockRejectedValue(new Error('Email already exists'));

      await expect(UserService.createUser(userData))
        .rejects.toThrow('Email already exists');
    });
  });
});
```

#### 集成测试示例

```javascript
// tests/integration/auth.test.js
const request = require('supertest');
const app = require('../../src/app');
const User = require('../../src/models/User');

describe('Auth API', () => {
  beforeEach(async () => {
    await User.deleteMany({});
  });

  describe('POST /api/auth/register', () => {
    it('should register new user successfully', async () => {
      const userData = {
        username: 'testuser',
        email: '<EMAIL>',
        password: 'password123'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.user.email).toBe(userData.email);
      expect(response.body.data.token).toBeDefined();
    });

    it('should reject registration with invalid email', async () => {
      const userData = {
        username: 'testuser',
        email: 'invalid-email',
        password: 'password123'
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });
  });
});
```

### 测试数据管理

```javascript
// tests/fixtures/users.js
module.exports = {
  validUser: {
    username: 'testuser',
    email: '<EMAIL>',
    password: 'password123'
  },

  adminUser: {
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin'
  },

  invalidUser: {
    username: '',
    email: 'invalid-email',
    password: '123'
  }
};
```

## 🚀 部署

### 部署选项

#### 1. 传统服务器部署

```bash
# 1. 克隆代码到服务器
git clone https://github.com/your-org/ebbinghaus-backend.git
cd ebbinghaus-backend

# 2. 安装依赖
npm ci --only=production

# 3. 配置环境变量
cp .env.production .env
# 编辑 .env 文件

# 4. 启动服务
npm start

# 生产模式启动
npm start -- --production
```



#### 3. 云平台部署

**Heroku 部署：**

```bash
# 1. 安装 Heroku CLI
# 2. 登录 Heroku
heroku login

# 3. 创建应用
heroku create ebbinghaus-backend

# 4. 配置环境变量
heroku config:set NODE_ENV=production
heroku config:set JWT_SECRET=your-secret-key
heroku config:set MONGODB_URI=your-mongodb-uri

# 5. 部署
git push heroku main
```

**AWS ECS 部署：**

```json
{
  "family": "ebbinghaus-backend",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "256",
  "memory": "512",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "containerDefinitions": [
    {
      "name": "ebbinghaus-backend",
      "image": "your-account.dkr.ecr.region.amazonaws.com/ebbinghaus-backend:latest",
      "portMappings": [
        {
          "containerPort": 3002,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/ebbinghaus-backend",
          "awslogs-region": "us-west-2",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

### 部署脚本

```bash
#!/bin/bash
# scripts/deploy.sh

set -e

echo "🚀 开始部署艾宾浩斯学习系统后端..."

# 检查环境
if [ -z "$NODE_ENV" ]; then
  echo "❌ NODE_ENV 环境变量未设置"
  exit 1
fi

# 备份当前版本
echo "📦 备份当前版本..."
npm run backup

# 拉取最新代码
echo "📥 拉取最新代码..."
git pull origin main

# 安装依赖
echo "📦 安装依赖..."
npm ci --only=production

# 运行数据库迁移
echo "🗄️ 运行数据库迁移..."
npm run migrate

# 运行测试
echo "🧪 运行测试..."
npm run test:production

# 重启服务
echo "🔄 重启服务..."
npm run restart:cluster

# 健康检查
echo "🏥 健康检查..."
sleep 10
curl -f http://localhost:3002/health || exit 1

echo "✅ 部署完成！"
```

### 环境配置

#### 生产环境配置清单

- [ ] 设置强密码的 JWT_SECRET
- [ ] 配置生产数据库连接
- [ ] 启用 HTTPS
- [ ] 配置反向代理 (Nginx)
- [ ] 设置防火墙规则
- [ ] 配置日志轮转
- [ ] 设置监控和告警
- [ ] 配置备份策略
- [ ] 设置 SSL 证书
- [ ] 配置 CDN (如果需要)

#### Nginx 配置示例

```nginx
# /etc/nginx/sites-available/ebbinghaus-backend
server {
    listen 80;
    server_name api.ebbinghaus-learning.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.ebbinghaus-learning.com;

    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;

    # SSL 配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";

    location / {
        proxy_pass http://localhost:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 📊 监控与日志

### 应用监控

#### 1. 健康检查

系统提供多层次的健康检查：

```javascript
// 基础健康检查
GET /health

// 详细健康检查
GET /health/detailed

// 就绪检查
GET /health/ready

// 存活检查
GET /health/live
```

#### 2. 性能监控

```javascript
// src/middleware/monitoring.js
const responseTime = require('response-time');
const prometheus = require('prom-client');

// 创建指标
const httpRequestDuration = new prometheus.Histogram({
  name: 'http_request_duration_seconds',
  help: 'HTTP请求持续时间',
  labelNames: ['method', 'route', 'status_code']
});

const httpRequestTotal = new prometheus.Counter({
  name: 'http_requests_total',
  help: 'HTTP请求总数',
  labelNames: ['method', 'route', 'status_code']
});

// 监控中间件
const monitoringMiddleware = (req, res, next) => {
  const start = Date.now();

  res.on('finish', () => {
    const duration = (Date.now() - start) / 1000;
    const route = req.route ? req.route.path : req.path;

    httpRequestDuration
      .labels(req.method, route, res.statusCode)
      .observe(duration);

    httpRequestTotal
      .labels(req.method, route, res.statusCode)
      .inc();
  });

  next();
};
```

#### 3. 错误监控

```javascript
// src/utils/errorReporting.js
const Sentry = require('@sentry/node');

// 初始化 Sentry
Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 1.0
});

// 错误报告函数
const reportError = (error, context = {}) => {
  console.error('Error occurred:', error);

  if (process.env.NODE_ENV === 'production') {
    Sentry.captureException(error, {
      tags: context.tags,
      extra: context.extra,
      user: context.user
    });
  }
};

module.exports = { reportError };
```

### 日志管理

#### 1. 日志配置

```javascript
// src/config/logger.js
const winston = require('winston');
const path = require('path');

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: {
    service: 'ebbinghaus-backend',
    version: process.env.npm_package_version
  },
  transports: [
    // 错误日志
    new winston.transports.File({
      filename: path.join(process.env.LOG_DIR || './logs', 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),

    // 组合日志
    new winston.transports.File({
      filename: path.join(process.env.LOG_DIR || './logs', 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5
    })
  ]
});

// 开发环境添加控制台输出
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

module.exports = logger;
```

#### 2. 日志轮转

```bash
# /etc/logrotate.d/ebbinghaus-backend
/var/log/ebbinghaus-backend/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 0644 nodejs nodejs
    postrotate
        systemctl reload ebbinghaus-backend
    endscript
}
```

### 监控指标

#### 1. 系统指标

- **CPU 使用率**: 应用和系统CPU使用情况
- **内存使用**: 堆内存和系统内存使用
- **磁盘I/O**: 读写操作和磁盘使用率
- **网络I/O**: 网络流量和连接数

#### 2. 应用指标

- **请求量**: QPS、TPS等请求统计
- **响应时间**: 平均响应时间、P95、P99
- **错误率**: 4xx、5xx错误统计
- **数据库性能**: 查询时间、连接池状态

#### 3. 业务指标

- **用户活跃度**: 日活、月活用户数
- **学习数据**: 任务完成率、复习完成率
- **系统使用**: API调用频率、功能使用统计

## 🔧 故障排除

### 常见问题

#### 1. 服务启动失败

**问题**: 服务无法启动

**可能原因**:
- 端口被占用
- 数据库连接失败
- 环境变量配置错误
- 依赖包缺失

**解决方案**:

```bash
# 检查端口占用
lsof -i :3002
netstat -tulpn | grep :3002

# 检查数据库连接
mongosh $MONGODB_URI --eval "db.adminCommand('ismaster')"

# 检查环境变量
node -e "console.log(process.env)"

# 重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

#### 2. 数据库连接问题

**问题**: MongoDB 连接超时或失败

**解决方案**:

```bash
# 检查 MongoDB 服务状态
systemctl status mongod

# 检查网络连接
telnet mongodb-host 27017

# 检查认证
mongosh $MONGODB_URI

# 检查连接池配置
# 调整 .env 中的连接池参数
MONGODB_MAX_POOL_SIZE=10
MONGODB_SERVER_SELECTION_TIMEOUT=5000
```

#### 3. 内存泄漏

**问题**: 应用内存使用持续增长

**诊断方法**:

```bash
# 使用 Node.js 内置工具
node --inspect src/server.js

# 生成堆快照
kill -USR2 <pid>

# 使用 clinic.js 分析
npm install -g clinic
clinic doctor -- node src/server.js
```

**解决方案**:
- 检查事件监听器是否正确移除
- 确认定时器和间隔器被清理
- 检查数据库连接是否正确关闭
- 使用弱引用处理缓存

#### 4. 性能问题

**问题**: API响应时间过长

**诊断步骤**:

```bash
# 启用性能分析
NODE_ENV=production node --prof src/server.js

# 分析性能日志
node --prof-process isolate-*.log > processed.txt

# 使用 APM 工具
npm install newrelic
# 配置 newrelic.js
```

**优化建议**:
- 添加数据库索引
- 实现查询缓存
- 优化数据库查询
- 使用连接池
- 启用 gzip 压缩

### 调试技巧

#### 1. 开发环境调试

```bash
# 启用调试模式
DEBUG=* npm run dev

# 使用 VS Code 调试
# 创建 .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Debug Server",
      "program": "${workspaceFolder}/src/server.js",
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal"
    }
  ]
}
```

#### 2. 生产环境调试

```bash
# 远程调试（谨慎使用）
node --inspect=0.0.0.0:9229 src/server.js

# 使用日志调试
logger.debug('Debug info', { data: someData });

# 临时增加日志级别
LOG_LEVEL=debug npm start
```

### 监控告警

#### 1. 告警规则

```yaml
# prometheus/alerts.yml
groups:
  - name: ebbinghaus-backend
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status_code=~"5.."}[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "高错误率告警"
          description: "5xx错误率超过10%"

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "响应时间过长"
          description: "95%请求响应时间超过1秒"

      - alert: DatabaseConnectionFailed
        expr: up{job="mongodb"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "数据库连接失败"
          description: "MongoDB连接不可用"
```

#### 2. 告警通知

```javascript
// src/utils/alerting.js
const axios = require('axios');

const sendSlackAlert = async (message, severity = 'info') => {
  if (!process.env.SLACK_WEBHOOK_URL) return;

  const color = {
    info: '#36a64f',
    warning: '#ff9500',
    critical: '#ff0000'
  }[severity] || '#36a64f';

  await axios.post(process.env.SLACK_WEBHOOK_URL, {
    attachments: [{
      color,
      title: '艾宾浩斯学习系统告警',
      text: message,
      timestamp: Math.floor(Date.now() / 1000)
    }]
  });
};

const sendEmailAlert = async (subject, message) => {
  // 实现邮件告警
};

module.exports = { sendSlackAlert, sendEmailAlert };
```

## 🤝 贡献指南

### 贡献流程

1. **Fork 项目**
2. **创建功能分支** (`git checkout -b feature/amazing-feature`)
3. **提交更改** (`git commit -m 'feat: add amazing feature'`)
4. **推送分支** (`git push origin feature/amazing-feature`)
5. **创建 Pull Request**

### 开发规范

#### 1. 代码审查清单

- [ ] 代码符合项目编码规范
- [ ] 添加了适当的测试用例
- [ ] 测试覆盖率不低于80%
- [ ] 更新了相关文档
- [ ] 提交信息符合规范
- [ ] 没有引入安全漏洞
- [ ] 性能没有明显下降

#### 2. Pull Request 模板

```markdown
## 变更描述

简要描述此次变更的内容和目的。

## 变更类型

- [ ] Bug 修复
- [ ] 新功能
- [ ] 性能优化
- [ ] 代码重构
- [ ] 文档更新
- [ ] 测试添加

## 测试

描述测试方法和结果：

- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 手动测试完成

## 检查清单

- [ ] 代码符合项目规范
- [ ] 添加了必要的测试
- [ ] 更新了文档
- [ ] 没有破坏现有功能

## 相关 Issue

关联的 Issue 编号：#123

## 截图（如果适用）

添加相关截图或 GIF。
```

### 发布流程

#### 1. 版本管理

项目使用语义化版本控制 (SemVer)：

- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

#### 2. 发布步骤

```bash
# 1. 更新版本号
npm version patch  # 或 minor/major

# 2. 更新 CHANGELOG
npm run changelog

# 3. 提交变更
git add .
git commit -m "chore: release v1.0.1"

# 4. 创建标签
git tag v1.0.1

# 5. 推送到远程
git push origin main --tags

# 6. 创建 GitHub Release
gh release create v1.0.1 --generate-notes
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持与联系

- **项目主页**: https://github.com/your-org/ebbinghaus-backend
- **问题反馈**: https://github.com/your-org/ebbinghaus-backend/issues
- **文档**: https://docs.ebbinghaus-learning.com
- **邮箱**: <EMAIL>

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户。

---

**最后更新**: 2025-08-03
**文档版本**: v1.0.0
**项目版本**: v1.0.0
