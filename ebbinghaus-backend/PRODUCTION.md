# 艾宾浩斯学习系统 - 生产环境配置指南

## 📋 概述

本文档详细说明了艾宾浩斯学习系统后端服务的生产环境配置、部署和维护流程。

## 🚀 快速部署

### 1. 环境准备

**系统要求：**
- Node.js 18+
- MongoDB 6.0+
- Redis 7.0+

**安装依赖：**
```bash
# 安装Node.js依赖
npm ci --only=production
```

### 2. 环境配置

**复制并配置环境变量：**
```bash
cp .env.production .env
```

**必须配置的环境变量：**
```bash
# 数据库配置
MONGODB_URI=mongodb://localhost:27017/ebbinghaus_learning
REDIS_URL=redis://localhost:6379

# 安全配置
JWT_SECRET=your-super-secure-jwt-secret-key
SESSION_SECRET=your-super-secure-session-secret

# 服务器配置
PORT=3001
NODE_ENV=production
```

### 3. 数据库初始化

```bash
# 运行数据库迁移
npm run migrate:init
npm run migrate

# 验证数据库结构
npm run migrate:validate
```

### 4. 启动服务

**智能启动（推荐）：**
```bash
# 生产模式启动
npm start -- --production

# 或者标准启动
npm start
```

**启动功能特性：**
- ✅ 自动环境检查和依赖验证
- ✅ 数据库连接测试
- ✅ 端口冲突检测
- ✅ 自动健康检查
- ✅ 错误恢复机制

## 📁 文件结构

```
ebbinghaus-backend/
├── .env.production          # 生产环境变量
├── .env.development         # 开发环境变量

├── src/
│   ├── config/
│   │   ├── environment.js   # 环境配置管理
│   │   └── performance.js   # 性能优化配置
│   └── ...
├── scripts/
│   ├── deploy.sh           # 部署脚本
│   ├── start-server.js     # 智能启动脚本
│   ├── migrate.js          # 数据库迁移
│   ├── backup.js           # 备份脚本
│   └── restore.js          # 恢复脚本
├── logs/                   # 日志目录
├── uploads/                # 上传文件目录
├── backups/                # 备份目录
└── pids/                   # 进程ID文件
```

## ⚙️ 配置详解

### 环境变量配置

**数据库配置：**
```bash
# MongoDB
MONGODB_URI=mongodb://localhost:27017/ebbinghaus_learning
MONGODB_MAX_POOL_SIZE=10
MONGODB_MIN_POOL_SIZE=5

# Redis
REDIS_URL=redis://localhost:6379
REDIS_DB=0
REDIS_MAX_RETRIES=3
```

**安全配置：**
```bash
# JWT配置
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=24h

# 密码加密
BCRYPT_ROUNDS=12

# CORS配置
CORS_ORIGIN=https://yourdomain.com
```

**性能配置：**
```bash
# 压缩
COMPRESSION_ENABLED=true
COMPRESSION_LEVEL=6

# 限流
RATE_LIMIT_ENABLED=true
RATE_LIMIT_MAX_REQUESTS=100

```

## 🔧 运维管理

### 日志管理

**日志位置：**
- 应用日志：`./logs/`
- 系统日志：通过智能启动脚本自动管理

**日志管理：**
```bash
# 查看应用日志
npm run health

# 查看详细健康状态
npm run health -- --detailed
```

### 备份与恢复

**自动备份：**
```bash
# 完整备份
npm run backup

# 仅备份MongoDB
node scripts/backup.js mongodb

# 仅备份Redis
node scripts/backup.js redis
```

**数据恢复：**
```bash
# 恢复MongoDB
node scripts/restore.js mongodb

# 恢复Redis
node scripts/restore.js redis

# 列出可用备份
node scripts/restore.js list
```

## 🔍 监控与健康检查

### 健康检查端点

```bash
# 基础健康检查
curl http://localhost:3001/health

# API健康检查
curl http://localhost:3001/api/health
```

### 性能监控

**系统监控：**
- 智能启动脚本自动监控服务健康状态
- 内置错误恢复和重启机制
- 可通过健康检查脚本查看系统状态

### 错误处理

**常见问题排查：**

1. **服务无法启动**
   ```bash
   # 使用故障排除脚本
   npm run troubleshoot

   # 检查端口占用
   lsof -i :3003

   # 查看详细健康状态
   npm run health -- --detailed
   ```

2. **数据库连接失败**
   ```bash
   # 检查MongoDB状态
   systemctl status mongod

   # 检查Redis状态
   systemctl status redis

   # 验证数据库连接
   npm run migrate:validate
   ```

3. **服务异常**
   ```bash
   # 重启服务
   npm start

   # 查看系统状态
   npm run health
   ```

## 🔒 安全配置

### SSL/TLS 配置

**Nginx反向代理配置：**
```nginx
server {
    listen 443 ssl http2;
    server_name yourdomain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 防火墙配置

```bash
# 仅允许必要端口
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw deny 3001   # 应用端口（通过Nginx代理）
```

## 📈 性能优化

### 数据库优化

**MongoDB索引：**
```bash
# 运行索引初始化
npm run migrate:init
```

**Redis配置：**
```bash
# 启用持久化
save 900 1
save 300 10
save 60 10000
```

### 应用优化

**Node.js优化：**
```bash
# 设置内存限制
node --max-old-space-size=512 src/app.js

# 启用压缩
COMPRESSION_ENABLED=true
```

## 🚨 故障恢复

### 快速恢复步骤

1. **检查服务状态**
   ```bash
   npm run health
   systemctl status mongod
   systemctl status redis
   ```

2. **重启服务**
   ```bash
   npm start
   ```

3. **恢复数据（如需要）**
   ```bash
   node scripts/restore.js list
   node scripts/restore.js mongodb
   ```

4. **验证服务**
   ```bash
   curl http://localhost:3001/health
   ```

## 📞 技术支持

如遇到问题，请按以下顺序排查：

1. 运行故障排除：`npm run troubleshoot`
2. 查看健康状态：`npm run health -- --detailed`
3. 验证配置文件：`npm run test:config`
4. 检查数据库连接：`npm run migrate:validate`

---

**最后更新：** 2025-08-03
**版本：** 1.0.0
