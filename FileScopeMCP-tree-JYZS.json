{"config": {"filename": "FileScopeMCP-tree-JYZS.json", "baseDirectory": "e:/JYZS", "projectRoot": "e:/JYZS", "lastUpdated": "2025-08-05T01:05:36.829Z"}, "fileTree": {"path": "e:\\JYZS", "name": "JYZS", "isDirectory": true, "children": [{"path": "e:\\JYZS\\backend-architecture-analysis.mmd", "name": "backend-architecture-analysis.mmd", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\backend-dependency-analysis.mmd", "name": "backend-dependency-analysis.mmd", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\doc", "name": "doc", "isDirectory": true, "children": [{"path": "e:\\JYZS\\doc\\backend-dependencies.mmd", "name": "backend-dependencies.mmd", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\doc\\backend-structure.mmd", "name": "backend-structure.mmd", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\doc\\项目依赖关系图.mmd", "name": "项目依赖关系图.mmd", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "e:\\JYZS\\docs", "name": "docs", "isDirectory": true, "children": [{"path": "e:\\JYZS\\docs\\Git配置说明.md", "name": "Git配置说明.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\docs\\updated-project-structure.mmd", "name": "updated-project-structure.mmd", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\docs\\项目基本架构图.mmd", "name": "项目基本架构图.mmd", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "e:\\JYZS\\ebbinghaus-backend", "name": "ebbinghaus-backend", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-backend\\docs", "name": "docs", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-backend\\docs\\后端依赖关系图.mmd", "name": "后端依赖关系图.mmd", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "e:\\JYZS\\ebbinghaus-backend\\jest.config.js", "name": "jest.config.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\package-lock.json", "name": "package-lock.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\package.json", "name": "package.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\PRODUCTION.md", "name": "PRODUCTION.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\README.md", "name": "README.md", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\scripts", "name": "scripts", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-backend\\scripts\\backup.js", "name": "backup.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [{"name": "child_process", "path": "e:\\JYZS\\node_modules\\child_process"}, {"name": "fs", "path": "e:\\JYZS\\node_modules\\fs"}, {"name": "path", "path": "e:\\JYZS\\node_modules\\path"}, {"name": "util", "path": "e:\\JYZS\\node_modules\\util"}, {"name": "dotenv", "path": "e:\\JYZS\\node_modules\\dotenv"}], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\scripts\\deploy.sh", "name": "deploy.sh", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\scripts\\health-check.js", "name": "health-check.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [{"name": "dotenv", "path": "e:\\JYZS\\node_modules\\dotenv"}, {"name": "http", "path": "e:\\JYZS\\node_modules\\http"}, {"name": "child_process", "path": "e:\\JYZS\\node_modules\\child_process"}, {"name": "util", "path": "e:\\JYZS\\node_modules\\util"}], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\scripts\\migrate.js", "name": "migrate.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [{"name": "mongoose", "path": "e:\\JYZS\\node_modules\\mongoose"}, {"name": "path", "path": "e:\\JYZS\\node_modules\\path"}, {"name": "fs", "path": "e:\\JYZS\\node_modules\\fs"}, {"name": "dotenv", "path": "e:\\JYZS\\node_modules\\dotenv"}], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\scripts\\restore.js", "name": "restore.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [{"name": "child_process", "path": "e:\\JYZS\\node_modules\\child_process"}, {"name": "fs", "path": "e:\\JYZS\\node_modules\\fs"}, {"name": "path", "path": "e:\\JYZS\\node_modules\\path"}, {"name": "util", "path": "e:\\JYZS\\node_modules\\util"}, {"name": "readline", "path": "e:\\JYZS\\node_modules\\readline"}, {"name": "dotenv", "path": "e:\\JYZS\\node_modules\\dotenv"}, {"name": "mongoose", "path": "e:\\JYZS\\node_modules\\mongoose"}, {"name": "redis", "path": "e:\\JYZS\\node_modules\\redis"}], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\scripts\\troubleshoot.js", "name": "troubleshoot.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [{"name": "dotenv", "path": "e:\\JYZS\\node_modules\\dotenv"}, {"name": "child_process", "path": "e:\\JYZS\\node_modules\\child_process"}, {"name": "fs", "path": "e:\\JYZS\\node_modules\\fs"}, {"name": "path", "path": "e:\\JYZS\\node_modules\\path"}, {"name": "util", "path": "e:\\JYZS\\node_modules\\util"}], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\scripts\\validate-swagger.js", "name": "validate-swagger.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [{"name": "fs", "path": "e:\\JYZS\\node_modules\\fs"}, {"name": "path", "path": "e:\\JYZS\\node_modules\\path"}], "dependents": []}]}, {"path": "e:\\JYZS\\ebbinghaus-backend\\src", "name": "src", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-backend\\src\\app.js", "name": "app.js", "isDirectory": false, "importance": 10, "dependencies": ["e:\\JYZS\\ebbinghaus-backend\\src\\routes\\auth.js"], "packageDependencies": [{"name": "dotenv", "path": "e:\\JYZS\\node_modules\\dotenv"}, {"name": "express", "path": "e:\\JYZS\\node_modules\\express"}, {"name": "cors", "path": "e:\\JYZS\\node_modules\\cors"}], "dependents": ["e:\\JYZS\\ebbinghaus-backend\\src\\server.js", "e:\\JYZS\\ebbinghaus-backend\\tests\\integration\\auth.test.js", "e:\\JYZS\\ebbinghaus-backend\\tests\\integration\\health.test.js", "e:\\JYZS\\ebbinghaus-backend\\tests\\integration\\tasks.test.js"]}, {"path": "e:\\JYZS\\ebbinghaus-backend\\src\\config", "name": "config", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-backend\\src\\config\\performance.js", "name": "performance.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [{"name": "compression", "path": "e:\\JYZS\\node_modules\\compression"}, {"name": "helmet", "path": "e:\\JYZS\\node_modules\\helmet"}, {"name": "os", "path": "e:\\JYZS\\node_modules\\os"}], "dependents": []}]}, {"path": "e:\\JYZS\\ebbinghaus-backend\\src\\controllers", "name": "controllers", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-backend\\src\\controllers\\authController.js", "name": "authController.js", "isDirectory": false, "importance": 6, "dependencies": [], "packageDependencies": [{"name": "uuid", "path": "e:\\JYZS\\node_modules\\uuid"}], "dependents": ["e:\\JYZS\\ebbinghaus-backend\\src\\routes\\auth.js"]}]}, {"path": "e:\\JYZS\\ebbinghaus-backend\\src\\middleware", "name": "middleware", "isDirectory": true, "children": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\src\\models", "name": "models", "isDirectory": true, "children": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\src\\routes", "name": "routes", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-backend\\src\\routes\\auth.js", "name": "auth.js", "isDirectory": false, "importance": 8, "dependencies": ["e:\\JYZS\\ebbinghaus-backend\\src\\controllers\\authController.js"], "packageDependencies": [{"name": "express", "path": "e:\\JYZS\\node_modules\\express"}], "dependents": ["e:\\JYZS\\ebbinghaus-backend\\src\\app.js"]}]}, {"path": "e:\\JYZS\\ebbinghaus-backend\\src\\server.js", "name": "server.js", "isDirectory": false, "importance": 6, "dependencies": ["e:\\JYZS\\ebbinghaus-backend\\src\\app.js"], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\src\\services", "name": "services", "isDirectory": true, "children": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\src\\utils", "name": "utils", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js", "name": "constants.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [], "dependents": ["e:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js"]}, {"path": "e:\\JYZS\\ebbinghaus-backend\\src\\utils\\helpers.js", "name": "helpers.js", "isDirectory": false, "importance": 6, "dependencies": ["e:\\JYZS\\ebbinghaus-backend\\src\\utils\\constants.js"], "packageDependencies": [{"name": "uuid", "path": "e:\\JYZS\\node_modules\\uuid"}, {"name": "dayjs", "path": "e:\\JYZS\\node_modules\\dayjs"}], "dependents": []}]}]}, {"path": "e:\\JYZS\\ebbinghaus-backend\\test-minimal-server.js", "name": "test-minimal-server.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [{"name": "dotenv", "path": "e:\\JYZS\\node_modules\\dotenv"}, {"name": "express", "path": "e:\\JYZS\\node_modules\\express"}, {"name": "cors", "path": "e:\\JYZS\\node_modules\\cors"}, {"name": "uuid", "path": "e:\\JYZS\\node_modules\\uuid"}, {"name": "uuid", "path": "e:\\JYZS\\node_modules\\uuid"}, {"name": "uuid", "path": "e:\\JYZS\\node_modules\\uuid"}], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\test-server.js", "name": "test-server.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [{"name": "express", "path": "e:\\JYZS\\node_modules\\express"}, {"name": "cors", "path": "e:\\JYZS\\node_modules\\cors"}, {"name": "uuid", "path": "e:\\JYZS\\node_modules\\uuid"}], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\tests", "name": "tests", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-backend\\tests\\fixtures", "name": "fixtures", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-backend\\tests\\fixtures\\testData.js", "name": "testData.js", "isDirectory": false, "importance": 8, "dependencies": [], "packageDependencies": [], "dependents": ["e:\\JYZS\\ebbinghaus-backend\\tests\\integration\\auth.test.js", "e:\\JYZS\\ebbinghaus-backend\\tests\\integration\\tasks.test.js", "e:\\JYZS\\ebbinghaus-backend\\tests\\unit\\taskService.test.js"]}]}, {"path": "e:\\JYZS\\ebbinghaus-backend\\tests\\helpers", "name": "helpers", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\setup.js", "name": "setup.js", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [{"name": "mongoose", "path": "e:\\JYZS\\node_modules\\mongoose"}, {"name": "mongodb-memory-server", "path": "e:\\JYZS\\node_modules\\mongodb-memory-server"}], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\testUtils.js", "name": "testUtils.js", "isDirectory": false, "importance": 10, "dependencies": [], "packageDependencies": [{"name": "jsonwebtoken", "path": "e:\\JYZS\\node_modules\\jsonwebtoken"}, {"name": "bcryptjs", "path": "e:\\JYZS\\node_modules\\bcryptjs"}, {"name": "mongoose", "path": "e:\\JYZS\\node_modules\\mongoose"}], "dependents": ["e:\\JYZS\\ebbinghaus-backend\\tests\\integration\\auth.test.js", "e:\\JYZS\\ebbinghaus-backend\\tests\\integration\\tasks.test.js", "e:\\JYZS\\ebbinghaus-backend\\tests\\unit\\ebbinghausService.test.js", "e:\\JYZS\\ebbinghaus-backend\\tests\\unit\\taskService.test.js"]}]}, {"path": "e:\\JYZS\\ebbinghaus-backend\\tests\\integration", "name": "integration", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-backend\\tests\\integration\\auth.test.js", "name": "auth.test.js", "isDirectory": false, "importance": 8, "dependencies": ["e:\\JYZS\\ebbinghaus-backend\\src\\app.js", "e:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\testUtils.js", "e:\\JYZS\\ebbinghaus-backend\\tests\\fixtures\\testData.js"], "packageDependencies": [{"name": "supertest", "path": "e:\\JYZS\\node_modules\\supertest"}], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\tests\\integration\\health.test.js", "name": "health.test.js", "isDirectory": false, "importance": 6, "dependencies": ["e:\\JYZS\\ebbinghaus-backend\\src\\app.js"], "packageDependencies": [{"name": "supertest", "path": "e:\\JYZS\\node_modules\\supertest"}], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\tests\\integration\\tasks.test.js", "name": "tasks.test.js", "isDirectory": false, "importance": 8, "dependencies": ["e:\\JYZS\\ebbinghaus-backend\\src\\app.js", "e:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\testUtils.js", "e:\\JYZS\\ebbinghaus-backend\\tests\\fixtures\\testData.js"], "packageDependencies": [{"name": "supertest", "path": "e:\\JYZS\\node_modules\\supertest"}], "dependents": []}]}, {"path": "e:\\JYZS\\ebbinghaus-backend\\tests\\unit", "name": "unit", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-backend\\tests\\unit\\ebbinghausService.simple.test.js", "name": "ebbinghausService.simple.test.js", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\tests\\unit\\ebbinghausService.test.js", "name": "ebbinghausService.test.js", "isDirectory": false, "importance": 4, "dependencies": ["e:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\testUtils.js"], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-backend\\tests\\unit\\taskService.test.js", "name": "taskService.test.js", "isDirectory": false, "importance": 6, "dependencies": ["e:\\JYZS\\ebbinghaus-backend\\tests\\helpers\\testUtils.js", "e:\\JYZS\\ebbinghaus-backend\\tests\\fixtures\\testData.js"], "packageDependencies": [], "dependents": []}]}]}, {"path": "e:\\JYZS\\ebbinghaus-backend\\tsconfig.json", "name": "tsconfig.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system", "name": "ebbinghaus-learning-system", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-learning-system\\docs", "name": "docs", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-learning-system\\docs\\前端依赖关系图.mmd", "name": "前端依赖关系图.mmd", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\docs\\前端包依赖关系图.mmd", "name": "前端包依赖关系图.mmd", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\docs\\前端混合架构图.mmd", "name": "前端混合架构图.mmd", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\docs\\前端目录结构图.mmd", "name": "前端目录结构图.mmd", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\env.d.ts", "name": "env.d.ts", "isDirectory": false, "importance": 5, "dependencies": [], "packageDependencies": [{"name": "vue", "path": "e:\\JYZS\\node_modules\\vue"}], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\index.html", "name": "index.html", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\package-lock.json", "name": "package-lock.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\package.json", "name": "package.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src", "name": "src", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\App.vue", "name": "App.vue", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\main.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\components", "name": "components", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business", "name": "business", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\MindMapCard.vue", "name": "MindMapCard.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\MindMapEdge.vue", "name": "MindMapEdge.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\MindMapNode.vue", "name": "MindMapNode.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\MindMapViewer.vue", "name": "MindMapViewer.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\ReviewCard.vue", "name": "ReviewCard.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\TaskCard.vue", "name": "TaskCard.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\components\\business\\TaskList.vue", "name": "TaskList.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\components\\common", "name": "common", "isDirectory": true, "children": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\components\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 5, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\components\\layout", "name": "layout", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\components\\layout\\AppHeader.vue", "name": "AppHeader.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\components\\layout\\AppLayout.vue", "name": "AppLayout.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\components\\layout\\AppNotifications.vue", "name": "AppNotifications.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\components\\layout\\AppSidebar.vue", "name": "AppSidebar.vue", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\components\\mindmap", "name": "mindmap", "isDirectory": true, "children": []}]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\constants", "name": "constants", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\constants\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 5, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\main.ts", "name": "main.ts", "isDirectory": false, "importance": 10, "dependencies": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\App.vue", "e:\\JYZS\\ebbinghaus-learning-system\\src\\router", "e:\\JYZS\\ebbinghaus-learning-system\\src\\stores", "e:\\JYZS\\ebbinghaus-learning-system\\src\\styles\\index.css"], "packageDependencies": [{"name": "vue", "path": "e:\\JYZS\\node_modules\\vue"}, {"name": "pinia", "path": "e:\\JYZS\\node_modules\\pinia"}, {"name": "element-plus", "path": "e:\\JYZS\\node_modules\\element-plus"}, {"name": "element-plus", "path": "e:\\JYZS\\node_modules\\element-plus\\dist\\index.css"}, {"name": "@element-plus/icons-vue", "path": "e:\\JYZS\\node_modules\\@element-plus\\icons-vue", "scope": "@element-plus"}], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\router", "name": "router", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 10, "dependencies": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\HomeView.vue", "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\LoginView.vue", "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\RegisterView.vue", "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\TestView.vue", "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ApiTestView.vue", "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\StoreTestView.vue", "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ComponentTestView.vue", "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\DashboardView.vue", "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\TasksView.vue", "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\TaskCreateView.vue", "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\TaskDetailView.vue", "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ReviewsView.vue", "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ReviewSessionView.vue", "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\MindMapsView.vue", "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\MindMapDetailView.vue"], "packageDependencies": [{"name": "vue-router", "path": "e:\\JYZS\\node_modules\\vue-router"}], "dependents": []}]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\services", "name": "services", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api", "name": "api", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\analytics.ts", "name": "analytics.ts", "isDirectory": false, "importance": 7, "dependencies": [], "packageDependencies": [{"name": "@/utils", "path": "e:\\JYZS\\node_modules\\@\\utils\\http", "scope": "@"}, {"name": "@/types", "path": "e:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 9, "dependencies": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\task.ts", "e:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\review.ts", "e:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\mindmap.ts", "e:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\user.ts", "e:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\notification.ts", "e:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\analytics.ts"], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\mindmap.ts", "name": "mindmap.ts", "isDirectory": false, "importance": 7, "dependencies": [], "packageDependencies": [{"name": "@/utils", "path": "e:\\JYZS\\node_modules\\@\\utils\\http", "scope": "@"}, {"name": "@/types", "path": "e:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\notification.ts", "name": "notification.ts", "isDirectory": false, "importance": 7, "dependencies": [], "packageDependencies": [{"name": "@/utils", "path": "e:\\JYZS\\node_modules\\@\\utils\\http", "scope": "@"}, {"name": "@/types", "path": "e:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\review.ts", "name": "review.ts", "isDirectory": false, "importance": 7, "dependencies": [], "packageDependencies": [{"name": "@/utils", "path": "e:\\JYZS\\node_modules\\@\\utils\\http", "scope": "@"}, {"name": "@/types", "path": "e:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\task.ts", "name": "task.ts", "isDirectory": false, "importance": 7, "dependencies": [], "packageDependencies": [{"name": "@/utils", "path": "e:\\JYZS\\node_modules\\@\\utils\\http", "scope": "@"}, {"name": "@/types", "path": "e:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\user.ts", "name": "user.ts", "isDirectory": false, "importance": 7, "dependencies": [], "packageDependencies": [{"name": "@/utils", "path": "e:\\JYZS\\node_modules\\@\\utils\\http", "scope": "@"}, {"name": "@/types", "path": "e:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\services\\api\\index.ts"]}]}]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\stores", "name": "stores", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\app.ts", "name": "app.ts", "isDirectory": false, "importance": 10, "dependencies": [], "packageDependencies": [{"name": "pinia", "path": "e:\\JYZS\\node_modules\\pinia"}, {"name": "vue", "path": "e:\\JYZS\\node_modules\\vue"}, {"name": "@/types", "path": "e:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\index.ts", "e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\mindmap.ts", "e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\review.ts", "e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\task.ts", "e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\user.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 9, "dependencies": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\app.ts", "e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\user.ts", "e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\task.ts", "e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\review.ts", "e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\mindmap.ts"], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\mindmap.ts", "name": "mindmap.ts", "isDirectory": false, "importance": 9, "dependencies": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\app.ts"], "packageDependencies": [{"name": "pinia", "path": "e:\\JYZS\\node_modules\\pinia"}, {"name": "vue", "path": "e:\\JYZS\\node_modules\\vue"}, {"name": "@/services", "path": "e:\\JYZS\\node_modules\\@\\services\\api", "scope": "@"}, {"name": "@/types", "path": "e:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\review.ts", "name": "review.ts", "isDirectory": false, "importance": 9, "dependencies": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\app.ts"], "packageDependencies": [{"name": "pinia", "path": "e:\\JYZS\\node_modules\\pinia"}, {"name": "vue", "path": "e:\\JYZS\\node_modules\\vue"}, {"name": "@/services", "path": "e:\\JYZS\\node_modules\\@\\services\\api", "scope": "@"}, {"name": "@/types", "path": "e:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\task.ts", "name": "task.ts", "isDirectory": false, "importance": 9, "dependencies": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\app.ts"], "packageDependencies": [{"name": "pinia", "path": "e:\\JYZS\\node_modules\\pinia"}, {"name": "vue", "path": "e:\\JYZS\\node_modules\\vue"}, {"name": "@/services", "path": "e:\\JYZS\\node_modules\\@\\services\\api", "scope": "@"}, {"name": "@/types", "path": "e:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\user.ts", "name": "user.ts", "isDirectory": false, "importance": 9, "dependencies": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\app.ts"], "packageDependencies": [{"name": "pinia", "path": "e:\\JYZS\\node_modules\\pinia"}, {"name": "vue", "path": "e:\\JYZS\\node_modules\\vue"}, {"name": "@/services", "path": "e:\\JYZS\\node_modules\\@\\services\\api", "scope": "@"}, {"name": "@/types", "path": "e:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\stores\\index.ts"]}]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\styles", "name": "styles", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\styles\\index.css", "name": "index.css", "isDirectory": false, "importance": 4, "dependencies": [], "packageDependencies": [], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\main.ts"]}]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\types", "name": "types", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\types\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 5, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\utils", "name": "utils", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\utils\\http.ts", "name": "http.ts", "isDirectory": false, "importance": 5, "dependencies": [], "packageDependencies": [{"name": "axios", "path": "e:\\JYZS\\node_modules\\axios", "version": "^1.11.0"}, {"name": "element-plus", "path": "e:\\JYZS\\node_modules\\element-plus"}, {"name": "@/types", "path": "e:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\utils\\index.ts", "name": "index.ts", "isDirectory": false, "importance": 7, "dependencies": [], "packageDependencies": [{"name": "dayjs", "path": "e:\\JYZS\\node_modules\\dayjs"}, {"name": "dayjs", "path": "e:\\JYZS\\node_modules\\dayjs\\locale\\zh-cn"}, {"name": "dayjs", "path": "e:\\JYZS\\node_modules\\dayjs\\plugin\\relativeTime"}, {"name": "dayjs", "path": "e:\\JYZS\\node_modules\\dayjs\\plugin\\duration"}, {"name": "@/types", "path": "e:\\JYZS\\node_modules\\@\\types", "scope": "@"}], "dependents": []}]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views", "name": "views", "isDirectory": true, "children": [{"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\analytics", "name": "analytics", "isDirectory": true, "children": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ApiTestView.vue", "name": "ApiTestView.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ComponentTestView.vue", "name": "ComponentTestView.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\dashboard", "name": "dashboard", "isDirectory": true, "children": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\DashboardView.vue", "name": "DashboardView.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\HomeView.vue", "name": "HomeView.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\LoginView.vue", "name": "LoginView.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\mindmap", "name": "mindmap", "isDirectory": true, "children": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\MindMapDetailView.vue", "name": "MindMapDetailView.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\MindMapsView.vue", "name": "MindMapsView.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\RegisterView.vue", "name": "RegisterView.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\review", "name": "review", "isDirectory": true, "children": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ReviewSessionView.vue", "name": "ReviewSessionView.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\ReviewsView.vue", "name": "ReviewsView.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\StoreTestView.vue", "name": "StoreTestView.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\task", "name": "task", "isDirectory": true, "children": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\TaskCreateView.vue", "name": "TaskCreateView.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\TaskDetailView.vue", "name": "TaskDetailView.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\TasksView.vue", "name": "TasksView.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\src\\views\\TestView.vue", "name": "TestView.vue", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": ["e:\\JYZS\\ebbinghaus-learning-system\\src\\router\\index.ts"]}]}]}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\tsconfig.app.json", "name": "tsconfig.app.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\tsconfig.json", "name": "tsconfig.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\tsconfig.node.json", "name": "tsconfig.node.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\tsconfig.vitest.json", "name": "tsconfig.vitest.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\ebbinghaus-learning-system\\vite.config.ts", "name": "vite.config.ts", "isDirectory": false, "importance": 5, "dependencies": [], "packageDependencies": [{"name": "vite", "path": "e:\\JYZS\\node_modules\\vite"}, {"name": "@vitejs/plugin-vue", "path": "e:\\JYZS\\node_modules\\@vitejs\\plugin-vue", "scope": "@vitejs"}, {"name": "node:url", "path": "e:\\JYZS\\node_modules\\node:url"}], "dependents": []}]}, {"path": "e:\\JYZS\\FileScopeMCP-excludes.json", "name": "FileScopeMCP-excludes.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\final-architecture-analysis.mmd", "name": "final-architecture-analysis.mmd", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\final-core-architecture.mmd", "name": "final-core-architecture.mmd", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\package-lock.json", "name": "package-lock.json", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\package.json", "name": "package.json", "isDirectory": false, "importance": 3, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\scripts", "name": "scripts", "isDirectory": true, "children": [{"path": "e:\\JYZS\\scripts\\git-quick.bat", "name": "git-quick.bat", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\scripts\\git-setup.ps1", "name": "git-setup.ps1", "isDirectory": false, "importance": 0, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "e:\\JYZS\\tests", "name": "tests", "isDirectory": true, "children": []}, {"path": "e:\\JYZS\\设计开发标准doc", "name": "设计开发标准doc", "isDirectory": true, "children": [{"path": "e:\\JYZS\\设计开发标准doc\\00-术语表.md", "name": "00-术语表.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\设计开发标准doc\\01-需求分析", "name": "01-需求分析", "isDirectory": true, "children": [{"path": "e:\\JYZS\\设计开发标准doc\\01-需求分析\\01-项目背景与目标.md", "name": "01-项目背景与目标.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\设计开发标准doc\\01-需求分析\\02-功能需求规格.md", "name": "02-功能需求规格.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\设计开发标准doc\\01-需求分析\\03-用户场景与流程.md", "name": "03-用户场景与流程.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\设计开发标准doc\\01-需求分析\\04-非功能性需求.md", "name": "04-非功能性需求.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\设计开发标准doc\\01-需求分析\\05-需求优先级与验收标准.md", "name": "05-需求优先级与验收标准.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\设计开发标准doc\\01-需求分析\\06-业务规则索引.md", "name": "06-业务规则索引.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\设计开发标准doc\\01-需求分析\\README.md", "name": "README.md", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "e:\\JYZS\\设计开发标准doc\\02-系统设计", "name": "02-系统设计", "isDirectory": true, "children": [{"path": "e:\\JYZS\\设计开发标准doc\\02-系统设计\\01-系统整体架构设计.md", "name": "01-系统整体架构设计.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\设计开发标准doc\\02-系统设计\\02-任务管理核心模块设计.md", "name": "02-任务管理核心模块设计.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\设计开发标准doc\\02-系统设计\\03-智能时间管理模块设计.md", "name": "03-智能时间管理模块设计.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\设计开发标准doc\\02-系统设计\\04-思维导图功能模块设计.md", "name": "04-思维导图功能模块设计.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\设计开发标准doc\\02-系统设计\\05-用户界面设计规范.md", "name": "05-用户界面设计规范.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\设计开发标准doc\\02-系统设计\\06-数据存储与同步设计.md", "name": "06-数据存储与同步设计.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\设计开发标准doc\\02-系统设计\\07-模块协作与通信规范.md", "name": "07-模块协作与通信规范.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\设计开发标准doc\\02-系统设计\\08-API接口设计.md", "name": "08-API接口设计.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\设计开发标准doc\\02-系统设计\\09-数据模型设计.md", "name": "09-数据模型设计.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\设计开发标准doc\\02-系统设计\\README.md", "name": "README.md", "isDirectory": false, "importance": 2, "dependencies": [], "packageDependencies": [], "dependents": []}]}, {"path": "e:\\JYZS\\设计开发标准doc\\03-开发实现", "name": "03-开发实现", "isDirectory": true, "children": [{"path": "e:\\JYZS\\设计开发标准doc\\03-开发实现\\01-前端核心功能开发指南.md", "name": "01-前端核心功能开发指南.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}, {"path": "e:\\JYZS\\设计开发标准doc\\03-开发实现\\02-后端核心功能开发指南.md", "name": "02-后端核心功能开发指南.md", "isDirectory": false, "importance": 1, "dependencies": [], "packageDependencies": [], "dependents": []}]}]}]}}