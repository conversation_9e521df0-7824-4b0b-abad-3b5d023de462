# [DES-MODULE-001] 任务管理核心模块设计

## 📋 概述

本文档定义了任务管理核心模块的详细技术设计，包括 [TERM-001] 艾宾浩斯记忆曲线算法实现、[TERM-004] 学习任务管理、复习提醒机制等核心功能的技术实现方案。

## 🎯 模块职责

### 核心功能
- **[TERM-004] 学习任务管理**：任务的创建、查询、更新、删除操作
- **[TERM-001] 艾宾浩斯记忆曲线**：基于记忆曲线的复习计划生成和管理
- **复习提醒机制**：智能提醒系统，支持多种提醒方式
- **任务状态管理**：任务生命周期管理和状态转换
- **数据持久化**：本地存储和云端同步的数据管理

### 模块边界
- **输入**：用户操作、时间触发、外部模块调用
- **输出**：任务数据、复习计划、提醒通知、状态更新
- **依赖**：数据存储模块、通知服务模块
- **被依赖**：时间管理模块、思维导图模块、用户界面模块

## 🔧 核心算法设计

### [DES-ALGO-001] 艾宾浩斯记忆曲线算法
**算法ID**：DES-ALGO-001  
**算法名称**：艾宾浩斯复习计划生成算法  
**实现需求**：[REQ-FUNC-002] 艾宾浩斯复习计划生成

**算法描述**：
基于 [TERM-001] 艾宾浩斯记忆曲线理论，为每个 [TERM-004] 学习任务生成9个复习时间点，实现科学的记忆强化。

**算法流程**：
```typescript
interface EbbinghausAlgorithm {
  // 标准时间间隔（分钟）
  readonly STANDARD_INTERVALS: number[] = [
    5,        // 5分钟
    30,       // 30分钟
    720,      // 12小时
    1440,     // 1天
    4320,     // 3天
    10080,    // 1周
    20160,    // 2周
    43200,    // 1月
    86400     // 2月
  ];

  /**
   * 生成复习计划
   * @param taskId 任务ID
   * @param createdAt 任务创建时间
   * @param userPreferences 用户偏好设置
   * @returns 复习计划数组
   */
  generateReviewSchedule(
    taskId: string,
    createdAt: Date,
    userPreferences?: UserPreferences
  ): ReviewSchedule[];
}
```

**算法实现**：
```typescript
class EbbinghausService {
  generateReviewSchedule(
    taskId: string,
    createdAt: Date,
    userPreferences?: UserPreferences
  ): ReviewSchedule[] {
    const schedule: ReviewSchedule[] = [];
    
    for (let i = 0; i < this.STANDARD_INTERVALS.length; i++) {
      const intervalMinutes = this.STANDARD_INTERVALS[i];
      let reviewTime = new Date(createdAt.getTime() + intervalMinutes * 60000);
      
      // 应用用户偏好调整
      if (userPreferences) {
        reviewTime = this.adjustForUserPreferences(reviewTime, userPreferences);
      }
      
      // 避开休息时间
      reviewTime = this.avoidRestTime(reviewTime, userPreferences?.restHours);
      
      schedule.push({
        id: generateUUID(),
        taskId,
        reviewIndex: i + 1,
        scheduledTime: reviewTime,
        status: ReviewStatus.SCHEDULED,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }
    
    return schedule;
  }

  /**
   * 根据复习效果调整后续复习间隔
   */
  adjustScheduleByEffectiveness(
    schedule: ReviewSchedule[],
    completedReviewIndex: number,
    effectiveness: number // 1-5分
  ): ReviewSchedule[] {
    const adjustmentFactor = this.calculateAdjustmentFactor(effectiveness);
    
    // 调整后续复习时间
    for (let i = completedReviewIndex; i < schedule.length; i++) {
      const currentInterval = schedule[i].scheduledTime.getTime() - 
                             schedule[i-1].scheduledTime.getTime();
      const adjustedInterval = currentInterval * adjustmentFactor;
      
      schedule[i].scheduledTime = new Date(
        schedule[i-1].scheduledTime.getTime() + adjustedInterval
      );
      schedule[i].updatedAt = new Date();
    }
    
    return schedule;
  }

  private calculateAdjustmentFactor(effectiveness: number): number {
    // 效果好(4-5分)：延长间隔，效果差(1-2分)：缩短间隔
    const factorMap = {
      1: 0.7,  // 效果很差，大幅缩短间隔
      2: 0.85, // 效果较差，适度缩短间隔
      3: 1.0,  // 效果一般，保持原间隔
      4: 1.2,  // 效果较好，适度延长间隔
      5: 1.4   // 效果很好，大幅延长间隔
    };
    return factorMap[effectiveness] || 1.0;
  }
}
```

**算法参数**：
- **标准间隔**：[5min, 30min, 12h, 1d, 3d, 1w, 2w, 1m, 2m]
- **调整范围**：±3天（避免时间冲突）
- **效果系数**：1-5分对应0.7-1.4的调整系数
- **休息时间**：用户设置的免打扰时间段

**性能要求**：
- 计算时间：≤ 100ms
- 内存使用：≤ 1MB
- 并发支持：1000个任务同时计算

**实现需求**：[REQ-FUNC-002] 艾宾浩斯复习计划生成  
**相关设计**：[DES-MODEL-002] 复习计划数据模型

## 📊 数据模型设计

### [DES-MODEL-001] 学习任务数据模型
**模型ID**：DES-MODEL-001  
**模型名称**：Task 学习任务模型  
**实现需求**：[REQ-FUNC-001] 学习任务创建功能

**数据结构**：
```typescript
interface Task {
  // 基础信息 [必填字段]
  id: string;              // [FIELD-001] UUID格式
  title: string;           // [FIELD-002] 1-100字符
  content: string;         // [FIELD-003] 1-5000字符
  subject: SubjectEnum;    // [FIELD-004] 预定义枚举
  
  // 学习参数 [可选字段]
  estimatedTime?: number;  // [FIELD-005] 1-300分钟
  actualTime?: number;     // [FIELD-006] 实际耗时
  priority?: number;       // [FIELD-007] 1-5优先级
  difficulty?: number;     // [FIELD-008] 1-5难度级别
  tags?: string[];         // [FIELD-009] 标签数组
  
  // 多媒体内容 [可选字段]
  images?: MediaFile[];    // [FIELD-010] 图片文件
  audio?: MediaFile[];     // [FIELD-011] 音频文件
  attachments?: MediaFile[]; // [FIELD-012] 附件文件
  
  // 状态信息 [系统字段]
  status: TaskStatus;      // [FIELD-013] 任务状态
  progress: number;        // [FIELD-014] 完成进度 0-100
  completedReviews: number; // [FIELD-015] 已完成复习次数
  
  // 时间信息 [系统字段]
  createdAt: Date;         // [FIELD-016] 创建时间
  updatedAt: Date;         // [FIELD-017] 更新时间
  completedAt?: Date;      // [FIELD-018] 完成时间
  
  // 关联信息 [外键字段]
  userId: string;          // [FIELD-019] 用户ID
  mindMapNodeId?: string;  // [FIELD-020] 关联的思维导图节点ID
  reviewScheduleIds: string[]; // [FIELD-021] 复习计划ID数组
  
  // 统计信息 [计算字段]
  totalStudyTime: number;  // [FIELD-022] 总学习时间
  averageReviewScore: number; // [FIELD-023] 平均复习评分
  memoryRetention: number; // [FIELD-024] 记忆保持率
}

// 任务状态枚举
enum TaskStatus {
  PENDING = 'pending',      // 待处理
  ACTIVE = 'active',        // 进行中
  COMPLETED = 'completed',  // 已完成
  CANCELLED = 'cancelled'   // 已取消
}

// 学科枚举
enum SubjectEnum {
  MATH = 'math',           // 数学
  CHINESE = 'chinese',     // 语文
  ENGLISH = 'english',     // 英语
  PHYSICS = 'physics',     // 物理
  CHEMISTRY = 'chemistry', // 化学
  BIOLOGY = 'biology',     // 生物
  HISTORY = 'history',     // 历史
  GEOGRAPHY = 'geography', // 地理
  POLITICS = 'politics'    // 政治
}

// 多媒体文件模型
interface MediaFile {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  url: string;
  createdAt: Date;
}
```

**数据约束**：
- **主键约束**：id字段唯一
- **外键约束**：userId必须存在于用户表
- **检查约束**：priority和difficulty范围1-5
- **长度约束**：title(1-100)，content(1-5000)
- **枚举约束**：status和subject必须在预定义枚举中

**索引设计**：
- **主键索引**：id
- **复合索引**：userId + createdAt（用户任务列表查询）
- **复合索引**：userId + subject（按学科筛选）
- **复合索引**：userId + status（按状态筛选）
- **全文索引**：title + content（任务搜索）

**实现需求**：[REQ-FUNC-001] 学习任务创建功能  
**相关约束**：[REQ-CONST-001] ~ [REQ-CONST-005]

### [DES-MODEL-002] 复习计划数据模型
**模型ID**：DES-MODEL-002  
**模型名称**：ReviewSchedule 复习计划模型  
**实现需求**：[REQ-FUNC-002] 艾宾浩斯复习计划生成

**数据结构**：
```typescript
interface ReviewSchedule {
  // 基础信息
  id: string;              // 复习计划ID
  taskId: string;          // 关联的任务ID
  reviewIndex: number;     // 复习序号 1-9
  
  // 时间信息
  scheduledTime: Date;     // 计划复习时间
  actualStartTime?: Date;  // 实际开始时间
  actualEndTime?: Date;    // 实际结束时间
  duration?: number;       // 实际复习时长（分钟）
  
  // 状态信息
  status: ReviewStatus;    // 复习状态
  effectiveness?: number;  // 复习效果评分 1-5
  notes?: string;          // 复习笔记
  
  // 提醒信息
  reminderSent: boolean;   // 是否已发送提醒
  reminderTime?: Date;     // 提醒发送时间
  
  // 系统信息
  createdAt: Date;         // 创建时间
  updatedAt: Date;         // 更新时间
  
  // 算法信息
  originalInterval: number; // 原始间隔（分钟）
  adjustedInterval?: number; // 调整后间隔（分钟）
  adjustmentReason?: string; // 调整原因
}

// 复习状态枚举
enum ReviewStatus {
  SCHEDULED = 'scheduled',    // 已安排
  REMINDED = 'reminded',      // 已提醒
  IN_PROGRESS = 'in_progress', // 进行中
  COMPLETED = 'completed',    // 已完成
  SKIPPED = 'skipped',       // 已跳过
  OVERDUE = 'overdue'        // 已逾期
}
```

**数据约束**：
- **主键约束**：id字段唯一
- **外键约束**：taskId必须存在于任务表
- **检查约束**：reviewIndex范围1-9
- **检查约束**：effectiveness范围1-5
- **唯一约束**：taskId + reviewIndex组合唯一

**索引设计**：
- **主键索引**：id
- **复合索引**：taskId + reviewIndex（任务复习计划查询）
- **复合索引**：scheduledTime + status（提醒查询）
- **复合索引**：userId + scheduledTime（用户复习日程）

**实现需求**：[REQ-FUNC-002] 艾宾浩斯复习计划生成  
**相关算法**：[DES-ALGO-001] 艾宾浩斯记忆曲线算法

## 🔌 API接口设计

### [DES-API-001] 任务管理API接口
**接口ID**：DES-API-001  
**接口类型**：RESTful API  
**实现需求**：[REQ-FUNC-001] 学习任务创建功能

#### 创建任务接口
**接口路径**：POST /api/tasks  
**请求格式**：
```json
{
  "title": "string(1-100)",
  "content": "string(1-5000)",
  "subject": "enum[math|chinese|english...]",
  "estimatedTime": "number(1-300)",
  "priority": "number(1-5)",
  "difficulty": "number(1-5)",
  "tags": "array[string]",
  "images": "array[file]",
  "audio": "array[file]"
}
```

**响应格式**：
```json
{
  "success": true,
  "data": {
    "taskId": "string(uuid)",
    "reviewSchedule": [
      {
        "id": "string(uuid)",
        "reviewIndex": 1,
        "scheduledTime": "2025-01-31T10:05:00Z"
      }
    ],
    "loadWarning": {
      "level": "medium",
      "message": "当日学习负载较重，建议调整时间安排",
      "suggestions": ["调整到明天", "降低优先级"]
    }
  },
  "error": null
}
```

**错误码定义**：
- **[ERR-001]** 参数验证失败
- **[ERR-002]** 负载超限警告
- **[ERR-003]** 系统内部错误
- **[ERR-004]** 用户权限不足
- **[ERR-005]** 文件上传失败

#### 查询任务列表接口
**接口路径**：GET /api/tasks  
**查询参数**：
```typescript
interface TaskQueryParams {
  page?: number;           // 页码，默认1
  pageSize?: number;       // 页大小，默认20
  subject?: SubjectEnum;   // 学科筛选
  status?: TaskStatus;     // 状态筛选
  priority?: number;       // 优先级筛选
  keyword?: string;        // 关键词搜索
  sortBy?: string;         // 排序字段
  sortOrder?: 'asc' | 'desc'; // 排序方向
  dateFrom?: string;       // 开始日期
  dateTo?: string;         // 结束日期
}
```

**响应格式**：
```json
{
  "success": true,
  "data": {
    "tasks": [
      {
        "id": "uuid",
        "title": "任务标题",
        "subject": "math",
        "status": "pending",
        "priority": 3,
        "estimatedTime": 30,
        "createdAt": "2025-01-31T08:00:00Z",
        "progress": 0,
        "nextReviewTime": "2025-01-31T08:05:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 156,
      "totalPages": 8
    }
  },
  "error": null
}
```

#### 更新任务接口
**接口路径**：PUT /api/tasks/:taskId  
**请求格式**：支持部分更新，只传递需要更新的字段

#### 删除任务接口
**接口路径**：DELETE /api/tasks/:taskId  
**响应格式**：
```json
{
  "success": true,
  "data": {
    "deletedTaskId": "uuid",
    "affectedReviews": 5,
    "message": "任务及相关复习计划已删除"
  },
  "error": null
}
```

**实现需求**：[REQ-FUNC-001] 学习任务创建功能  
**相关模型**：[DES-MODEL-001] 学习任务数据模型

### [DES-API-002] 复习管理API接口
**接口ID**：DES-API-002  
**接口类型**：RESTful API  
**实现需求**：[REQ-FUNC-006] 复习执行功能

#### 开始复习接口
**接口路径**：POST /api/reviews/:reviewId/start  
**响应格式**：
```json
{
  "success": true,
  "data": {
    "reviewId": "uuid",
    "taskId": "uuid",
    "taskContent": {
      "title": "任务标题",
      "content": "任务内容",
      "images": ["url1", "url2"],
      "audio": ["url1"]
    },
    "reviewIndex": 3,
    "startTime": "2025-01-31T10:00:00Z"
  },
  "error": null
}
```

#### 完成复习接口
**接口路径**：POST /api/reviews/:reviewId/complete  
**请求格式**：
```json
{
  "effectiveness": 4,
  "notes": "复习效果不错，基本掌握",
  "actualDuration": 25
}
```

**响应格式**：
```json
{
  "success": true,
  "data": {
    "reviewId": "uuid",
    "completedAt": "2025-01-31T10:25:00Z",
    "nextReviewTime": "2025-02-03T10:00:00Z",
    "adjustedSchedule": [
      {
        "reviewIndex": 4,
        "originalTime": "2025-02-01T10:00:00Z",
        "adjustedTime": "2025-02-03T10:00:00Z",
        "reason": "效果良好，延长间隔"
      }
    ]
  },
  "error": null
}
```

**实现需求**：[REQ-FUNC-006] 复习执行功能  
**相关算法**：[DES-ALGO-001] 艾宾浩斯记忆曲线算法

## 📝 变更记录

| 版本 | 日期 | 变更内容 | 变更人 | 批准人 |
|------|------|----------|--------|--------|
| v1.0 | 2025-01-31 | 初始任务管理核心模块设计创建 | 后端架构师 | 技术负责人 |
| v1.1 | 2025-02-01 | 逻辑冲突检查和版本管理优化 | 后端架构师 | 技术负责人 |

---

**文档版本**：v1.1
**创建时间**：2025-01-31
**最后更新**：2025-02-01
**负责人**：后端架构师
**审核人**：技术负责人
**状态**：已发布
