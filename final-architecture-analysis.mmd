graph TB
classDef package-node fill:#a29bfe,stroke:#2d3436,shape:ellipse
classDef package-scope-node fill:#ffeaa7,stroke:#2d3436,shape:stadium

  %% Package Scopes

  %% Node Definitions & Styles
  node0["JYZS"];
  style node0 fill:#74b9ff,stroke:#333,stroke-width:1px
  node1["cunzhi-memory"];
  style node1 fill:#74b9ff,stroke:#333,stroke-width:1px
  node2["idea"];
  style node2 fill:#74b9ff,stroke:#333,stroke-width:1px
  node3["ebbinghaus-backend"];
  style node3 fill:#74b9ff,stroke:#333,stroke-width:1px
  node4["ebbinghaus-learning-system"];
  style node4 fill:#74b9ff,stroke:#333,stroke-width:1px
  node5["______doc"];
  style node5 fill:#74b9ff,stroke:#333,stroke-width:1px
  node6["coverage"];
  style node6 fill:#74b9ff,stroke:#333,stroke-width:1px
  node7["docs"];
  style node7 fill:#74b9ff,stroke:#333,stroke-width:1px
  node8["logs"];
  style node8 fill:#74b9ff,stroke:#333,stroke-width:1px
  node9["scripts"];
  style node9 fill:#74b9ff,stroke:#333,stroke-width:1px
  node10["src"];
  style node10 fill:#74b9ff,stroke:#333,stroke-width:1px
  node11["test-server.js"];
  style node11 fill:#ff7675,stroke:#333,stroke-width:1px
  node12["tests"];
  style node12 fill:#74b9ff,stroke:#333,stroke-width:1px
  node13["dist"];
  style node13 fill:#74b9ff,stroke:#333,stroke-width:1px
  node14["docs"];
  style node14 fill:#74b9ff,stroke:#333,stroke-width:1px
  node15["src"];
  style node15 fill:#74b9ff,stroke:#333,stroke-width:1px
  node16["01-____"];
  style node16 fill:#74b9ff,stroke:#333,stroke-width:1px
  node17["02-____"];
  style node17 fill:#74b9ff,stroke:#333,stroke-width:1px
  node18["03-____"];
  style node18 fill:#74b9ff,stroke:#333,stroke-width:1px

  %% Edge Definitions
  node0 --> node1
  linkStyle 0 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node2
  linkStyle 1 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node3
  linkStyle 2 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node4
  linkStyle 3 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node5
  linkStyle 4 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node3 --> node6
  linkStyle 5 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node3 --> node7
  linkStyle 6 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node3 --> node8
  linkStyle 7 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node3 --> node9
  linkStyle 8 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node3 --> node10
  linkStyle 9 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node3 --> node11
  linkStyle 10 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node3 --> node12
  linkStyle 11 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node4 --> node13
  linkStyle 12 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node4 --> node14
  linkStyle 13 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node4 --> node15
  linkStyle 14 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node16
  linkStyle 15 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node17
  linkStyle 16 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node18
  linkStyle 17 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5