# ===================================
# JYZS 项目 Git 属性配置
# ===================================

# ===== 行尾处理 =====
# 自动转换行尾符
* text=auto

# Windows 特定文件使用 CRLF
*.bat text eol=crlf
*.cmd text eol=crlf
*.ps1 text eol=crlf

# Unix 特定文件使用 LF
*.sh text eol=lf
Dockerfile text eol=lf

# ===== 代码文件 =====
# JavaScript/TypeScript
*.js text
*.jsx text
*.ts text
*.tsx text
*.vue text
*.json text
*.mjs text
*.cjs text

# 样式文件
*.css text
*.scss text
*.sass text
*.less text
*.styl text

# 标记语言
*.html text
*.xml text
*.svg text
*.md text
*.mdx text
*.yml text
*.yaml text
*.toml text

# 配置文件
*.ini text
*.cfg text
*.conf text
*.config text
.gitignore text
.gitattributes text
.editorconfig text
.eslintrc* text
.prettierrc* text
tsconfig*.json text
package*.json text
*.lock text

# ===== 二进制文件 =====
# 图片
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.webp binary
*.svg binary
*.bmp binary
*.tiff binary

# 字体
*.woff binary
*.woff2 binary
*.eot binary
*.ttf binary
*.otf binary

# 音视频
*.mp3 binary
*.mp4 binary
*.avi binary
*.mov binary
*.wav binary
*.flac binary

# 压缩文件
*.zip binary
*.tar binary
*.gz binary
*.rar binary
*.7z binary

# 可执行文件
*.exe binary
*.dll binary
*.so binary
*.dylib binary

# 文档
*.pdf binary
*.doc binary
*.docx binary
*.xls binary
*.xlsx binary
*.ppt binary
*.pptx binary

# ===== 特殊处理 =====
# 大文件使用 LFS
*.zip filter=lfs diff=lfs merge=lfs -text
*.tar.gz filter=lfs diff=lfs merge=lfs -text
*.mp4 filter=lfs diff=lfs merge=lfs -text
*.mov filter=lfs diff=lfs merge=lfs -text

# 忽略差异比较的文件
package-lock.json -diff
yarn.lock -diff
pnpm-lock.yaml -diff

# ===== 语言检测 =====
# 确保正确的语言检测
*.vue linguist-language=Vue
*.ts linguist-language=TypeScript
*.js linguist-language=JavaScript
*.mmd linguist-language=Mermaid

# 排除某些文件的语言统计
docs/ linguist-documentation
*.md linguist-documentation
*.mmd linguist-documentation
node_modules/ linguist-vendored
dist/ linguist-generated
build/ linguist-generated
coverage/ linguist-generated

# ===== 合并策略 =====
# 特定文件的合并策略
package.json merge=ours
package-lock.json merge=ours
yarn.lock merge=ours
.env.example merge=union
