# ===================================
# JYZS 艾宾浩斯学习系统 Git 过滤规则
# ===================================

# ===== 依赖和包管理 =====
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
# 注意：package-lock.json 应该被提交到版本控制
# yarn.lock 和 pnpm-lock.yaml 根据项目使用的包管理器决定

# ===== 环境变量和配置 =====
.env
.env.*
!.env.example
config.json
*.local

# ===== 构建输出 =====
dist/
build/
out/
.next/
.nuxt/
.output/
.vercel/
.netlify/

# ===== 日志文件 =====
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# ===== 缓存目录 =====
.cache/
.parcel-cache/
.npm/
.yarn/
.pnpm/
.eslintcache
.stylelintcache
*.tsbuildinfo
.turbo/

# ===== 测试覆盖率 =====
coverage/
*.lcov
.nyc_output/
.c8_output/

# ===== 临时文件 =====
tmp/
temp/
*.tmp
*.temp
*.pid
*.seed
*.pid.lock

# ===== 编辑器和IDE =====
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/

# ===== AI工具和记忆文件 =====
.cunzhi-memory/
.augment/
# AI分析临时文件
frontend-analysis.json
backend-analysis.json
*-analysis.json

# ===== 操作系统文件 =====
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# ===== FileScopeMCP 分析文件 =====
# 保留主配置文件，排除子模块重复配置
FileScopeMCP-tree-*.json
!FileScopeMCP-tree-JYZS.json
!FileScopeMCP-excludes.json
# 排除子目录中的重复配置
ebbinghaus-backend/FileScopeMCP-tree-*.json
ebbinghaus-learning-system/FileScopeMCP-tree-*.json

# ===== 文档和图表 =====
# 保留重要文档，排除自动生成的临时文件
*.mmd.backup
*.md.backup
docs/temp/
docs/cache/

# ===== 上传和媒体文件 =====
uploads/
public/uploads/
static/uploads/
media/
assets/uploads/

# ===== 数据库文件 =====
*.db
*.sqlite
*.sqlite3
data/
db/

# ===== 安全和密钥 =====
*.pem
*.key
*.cert
*.crt
secrets/
.secrets/

# ===== 运行时文件 =====
pids/
*.pid
*.seed
*.pid.lock
.lock-wscript

# ===== 特定框架文件 =====
# Vue.js
.vite/
.rollup.cache/

# TypeScript
*.tsbuildinfo

# ESLint
.eslintcache

# Prettier
.prettierignore

# Stylelint
.stylelintcache

# ===== 部署相关 =====
.vercel/
.netlify/
.firebase/
.surge/

# ===== 备份文件 =====
*.bak
*.backup
*.old
*.orig

# ===== 压缩文件 =====
*.zip
*.tar.gz
*.rar
*.7z

# ===== 特定项目文件 =====
# 测试服务器文件（保留主要的）
test-*.js
!test-minimal-server.js

# 临时脚本
temp-*.js
temp-*.ts
scratch.*

# 设计文档目录（根据需要决定是否排除）
# 注意：当前包含重要的设计文档，建议保留
# 设计开发标准doc/

# ===== 性能分析 =====
.clinic/
.0x/
flamegraph.html
perf.data*

# ===== 其他工具 =====
.history/
.vite-inspect/
.turbo/
.rush/
