# ===================================
# JYZS 项目 Git 配置脚本
# ===================================

Write-Host "🔧 JYZS 项目 Git 环境配置" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# 检查是否在项目根目录
if (-not (Test-Path "package.json")) {
    Write-Host "❌ 请在项目根目录运行此脚本" -ForegroundColor Red
    exit 1
}

# 1. 设置 Git 配置
Write-Host "📝 配置 Git 设置..." -ForegroundColor Yellow

# 设置行尾处理
git config core.autocrlf true
git config core.safecrlf warn

# 设置文件权限
git config core.filemode false

# 设置默认分支名
git config init.defaultBranch main

# 设置推送策略
git config push.default simple

# 设置合并策略
git config merge.tool vscode
git config mergetool.vscode.cmd 'code --wait $MERGED'

# 设置差异工具
git config diff.tool vscode
git config difftool.vscode.cmd 'code --wait --diff $LOCAL $REMOTE'

# 2. 设置 Git 钩子
Write-Host "🪝 设置 Git 钩子..." -ForegroundColor Yellow

$hooksDir = ".git/hooks"
if (Test-Path $hooksDir) {
    # 创建 pre-commit 钩子
    $preCommitHook = @"
#!/bin/sh
# JYZS 项目 pre-commit 钩子

echo "🔍 执行提交前检查..."

# 检查是否有大文件
echo "📏 检查文件大小..."
git diff --cached --name-only | while read file; do
    if [ -f "$file" ]; then
        size=$(wc -c < "$file")
        if [ $size -gt 10485760 ]; then  # 10MB
            echo "❌ 文件 $file 超过 10MB，请使用 Git LFS"
            exit 1
        fi
    fi
done

# 检查敏感信息
echo "🔒 检查敏感信息..."
if git diff --cached --name-only | grep -E "\.(env|key|pem|cert)$"; then
    echo "⚠️  检测到可能的敏感文件，请确认是否应该提交"
fi

# 检查代码格式（如果有 prettier）
if command -v npx >/dev/null 2>&1 && [ -f "package.json" ]; then
    echo "🎨 检查代码格式..."
    npx prettier --check . || {
        echo "❌ 代码格式检查失败，请运行 npm run format"
        exit 1
    }
fi

echo "✅ 提交前检查通过"
"@
    
    $preCommitPath = "$hooksDir/pre-commit"
    $preCommitHook | Out-File -FilePath $preCommitPath -Encoding UTF8
    
    # 设置执行权限（Windows 下通过 Git 设置）
    git update-index --chmod=+x $preCommitPath
}

# 3. 清理和优化
Write-Host "🧹 清理 Git 状态..." -ForegroundColor Yellow

# 添加新的配置文件
git add .gitignore .gitattributes

# 移除已删除的文件
git add -u

# 4. 显示当前状态
Write-Host "📊 当前 Git 状态:" -ForegroundColor Green
git status --short

# 5. 提供建议
Write-Host ""
Write-Host "💡 建议的下一步操作:" -ForegroundColor Cyan
Write-Host "1. 检查暂存的文件: git status" -ForegroundColor White
Write-Host "2. 提交初始版本: git commit -m 'feat: 初始化 JYZS 艾宾浩斯学习系统'" -ForegroundColor White
Write-Host "3. 创建远程仓库并推送: git remote add origin <URL> && git push -u origin main" -ForegroundColor White
Write-Host ""
Write-Host "🔧 Git 配置完成！" -ForegroundColor Green
