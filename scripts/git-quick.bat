@echo off
chcp 65001 >nul
echo.
echo 🚀 JYZS 项目 Git 快速操作
echo ===========================

if "%1"=="" (
    echo 📋 可用命令:
    echo   git-quick status    - 查看状态
    echo   git-quick add       - 添加所有更改
    echo   git-quick commit    - 提交更改
    echo   git-quick push      - 推送到远程
    echo   git-quick pull      - 拉取远程更改
    echo   git-quick clean     - 清理未跟踪文件
    echo   git-quick setup     - 初始化设置
    goto :end
)

if "%1"=="status" (
    echo 📊 Git 状态:
    git status --short
    echo.
    git log --oneline -5
    goto :end
)

if "%1"=="add" (
    echo 📝 添加所有更改...
    git add .
    echo ✅ 文件已添加到暂存区
    git status --short
    goto :end
)

if "%1"=="commit" (
    if "%2"=="" (
        echo ❌ 请提供提交信息: git-quick commit "提交信息"
        goto :end
    )
    echo 💾 提交更改...
    git commit -m "%~2"
    echo ✅ 提交完成
    goto :end
)

if "%1"=="push" (
    echo 🚀 推送到远程仓库...
    git push
    echo ✅ 推送完成
    goto :end
)

if "%1"=="pull" (
    echo 📥 拉取远程更改...
    git pull
    echo ✅ 拉取完成
    goto :end
)

if "%1"=="clean" (
    echo 🧹 清理未跟踪文件...
    git clean -fd
    echo ✅ 清理完成
    goto :end
)

if "%1"=="setup" (
    echo 🔧 执行 Git 初始化设置...
    powershell -ExecutionPolicy Bypass -File "scripts/git-setup.ps1"
    goto :end
)

echo ❌ 未知命令: %1

:end
echo.
