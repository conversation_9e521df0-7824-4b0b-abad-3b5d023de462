# JYZS 艾宾浩斯记忆曲线学习管理系统 - 技术栈总结

## 📋 项目概览

**项目名称**: JYZS (艾宾浩斯记忆曲线学习管理系统)  
**架构模式**: Monorepo + 前后端分离  
**项目管理**: npm workspaces  
**开发模式**: 本地开发 + API代理  

---

## 🎨 前端技术栈 (ebbinghaus-learning-system)

### 核心框架
| 技术 | 版本 | 用途 |
|------|------|------|
| Vue 3 | ^3.4.0 | 渐进式JavaScript框架 |
| TypeScript | ~5.3.0 | 类型安全的JavaScript超集 |
| Vite | ^5.0.10 | 现代化构建工具 |

### UI组件库
| 技术 | 版本 | 用途 |
|------|------|------|
| Element Plus | ^2.4.4 | Vue 3企业级UI组件库 |
| @element-plus/icons-vue | ^2.3.1 | Element Plus图标库 |
| TailwindCSS | ^3.3.6 | 原子化CSS框架 |
| @tailwindcss/typography | ^0.5.10 | 排版插件 |

### 状态管理
| 技术 | 版本 | 用途 |
|------|------|------|
| Pinia | ^2.1.7 | Vue 3官方状态管理库 |
| Vue Router | ^4.2.5 | Vue官方路由管理 |

### 数据可视化
| 技术 | 版本 | 用途 |
|------|------|------|
| Cytoscape.js | ^3.28.1 | 图形可视化库 |
| cytoscape-dagre | ^2.5.0 | 有向图布局算法 |
| cytoscape-cose-bilkent | ^4.1.0 | 复合图布局算法 |

### 工具库
| 技术 | 版本 | 用途 |
|------|------|------|
| Axios | ^1.6.2 | HTTP客户端 |
| Day.js | ^1.11.10 | 轻量级日期处理 |
| Lodash-es | ^4.17.21 | 实用工具库 |
| vuedraggable | ^4.1.0 | 拖拽组件 |

### 开发工具
| 技术 | 版本 | 用途 |
|------|------|------|
| ESLint | ^8.49.0 | 代码质量检查 |
| Prettier | ^3.0.3 | 代码格式化 |
| Vitest | ^1.0.4 | 单元测试框架 |
| Playwright | ^1.40.0 | E2E测试框架 |
| Vue TSC | ^1.8.25 | Vue TypeScript编译器 |

---

## ⚙️ 后端技术栈 (ebbinghaus-backend)

### 核心框架
| 技术 | 版本 | 用途 |
|------|------|------|
| Node.js | >=18.0.0 | JavaScript运行时 |
| Express | ^5.1.0 | Web应用框架 |
| TypeScript | ^5.9.2 | 类型安全支持 |

### 数据库
| 技术 | 版本 | 用途 |
|------|------|------|
| MongoDB | - | NoSQL文档数据库 |
| Mongoose | ^8.17.0 | MongoDB对象建模工具 |
| Redis | ^5.7.0 | 内存数据库/缓存 |
| IORedis | ^5.7.0 | Redis客户端 |

### 安全认证
| 技术 | 版本 | 用途 |
|------|------|------|
| jsonwebtoken | ^9.0.2 | JWT认证 |
| bcryptjs | ^3.0.2 | 密码加密 |
| Helmet | ^8.1.0 | 安全头部设置 |
| CORS | ^2.8.5 | 跨域资源共享 |

### API文档
| 技术 | 版本 | 用途 |
|------|------|------|
| swagger-jsdoc | ^6.2.8 | JSDoc注释转Swagger |
| swagger-ui-express | ^5.0.1 | Swagger UI界面 |

### 性能优化
| 技术 | 版本 | 用途 |
|------|------|------|
| express-rate-limit | ^8.0.1 | 请求频率限制 |
| rate-limit-redis | ^4.2.1 | Redis存储限流数据 |
| Morgan | ^1.10.1 | HTTP请求日志 |

### 日志系统
| 技术 | 版本 | 用途 |
|------|------|------|
| Winston | ^3.17.0 | 日志记录库 |
| winston-daily-rotate-file | ^5.0.0 | 日志文件轮转 |

### 开发工具
| 技术 | 版本 | 用途 |
|------|------|------|
| Jest | ^30.0.5 | 测试框架 |
| Supertest | ^7.1.4 | HTTP断言测试 |
| ESLint | ^9.32.0 | 代码质量检查 |
| Prettier | ^3.6.2 | 代码格式化 |
| Nodemon | ^3.1.10 | 开发热重载 |

---

## 🔧 开发环境配置

### 构建工具
- **前端**: Vite + Vue SFC + TypeScript
- **后端**: Node.js原生 + TypeScript编译

### 端口配置
- **前端开发服务器**: 3000
- **后端API服务器**: 3004
- **API代理**: Vite proxy转发 `/api` 到后端

### 环境管理
- **dotenv**: 环境变量管理
- **多环境配置**: 开发/生产环境分离

---

## 📦 项目特色功能

1. **艾宾浩斯记忆曲线算法实现**
2. **思维导图可视化系统**
3. **智能复习提醒机制**
4. **用户学习数据分析**
5. **响应式设计适配**

---

## 🚀 启动命令

### 一键启动全部服务
```bash
npm run dev  # 同时启动前后端
```

### 分别启动
```bash
npm run dev:frontend  # 仅启动前端 (端口3000)
npm run dev:backend   # 仅启动后端 (端口3004)
```

### 构建部署
```bash
npm run build  # 构建前后端
```

---

## 📁 项目结构

```
E:/JYZS/
├── ebbinghaus-learning-system/     # 前端项目
│   ├── src/
│   │   ├── components/             # 组件库
│   │   ├── stores/                 # Pinia状态管理
│   │   ├── views/                  # 页面视图
│   │   └── utils/                  # 工具函数
│   └── package.json
├── ebbinghaus-backend/             # 后端项目
│   ├── src/
│   │   ├── controllers/            # 控制器
│   │   ├── models/                 # 数据模型
│   │   ├── routes/                 # 路由定义
│   │   └── services/               # 业务服务
│   └── package.json
└── package.json                    # Monorepo主配置
```

---

**文档版本**: v1.0  
**更新日期**: 2025-01-05  
**维护团队**: JYZS Team
