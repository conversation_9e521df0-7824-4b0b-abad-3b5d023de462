["# ===== 依赖和包管理 =====", "**/node_modules", "**/*-debug.log*", "**/*.log", "# ===== 环境变量和配置 =====", "**/.env", "**/.env.*", "!**/.env.example", "**/config.json", "**/*.local", "# ===== 构建输出 =====", "**/dist", "**/build", "**/out", "**/.next", "**/.nuxt", "**/.output", "**/.vercel", "**/.netlify", "# ===== 日志文件 =====", "**/logs", "**/*.log", "# ===== 缓存目录 =====", "**/.cache", "**/.parcel-cache", "**/.npm", "**/.yarn", "**/.pnpm", "**/.eslintcache", "**/.<PERSON><PERSON><PERSON><PERSON>", "**/*.tsbuildinfo", "**/.turbo", "# ===== 测试覆盖率 =====", "**/coverage", "**/*.lcov", "**/.nyc_output", "**/.c8_output", "# ===== 临时文件 =====", "**/tmp", "**/temp", "**/*.tmp", "**/*.temp", "**/*.pid", "**/*.seed", "**/*.pid.lock", "# ===== 编辑器和IDE =====", "**/.vscode", "**/.idea", "**/*.swp", "**/*.swo", "**/*~", "**/.project", "**/.classpath", "**/.settings", "# ===== AI工具和记忆文件 =====", "**/.cunzhi-memory", "**/.augment", "**/*-analysis.json", "# ===== 操作系统文件 =====", "**/.DS_Store", "**/.DS_Store?", "**/._*", "**/.Spotlight-V100", "**/.<PERSON><PERSON><PERSON>", "**/ehthumbs.db", "**/Thumbs.db", "**/desktop.ini", "# ===== FileScopeMCP 分析文件 =====", "**/FileScopeMCP-tree-*.json", "!**/FileScopeMCP-tree-JYZS.json", "!**/FileScopeMCP-excludes.json", "# ===== 框架特定文件 =====", "**/.vite", "**/.rollup.cache", "# ===== 备份和压缩文件 =====", "**/*.bak", "**/*.backup", "**/*.old", "**/*.orig", "**/*.zip", "**/*.tar.gz", "**/*.rar", "**/*.7z", "# ===== 临时脚本 =====", "**/temp-*.js", "**/temp-*.ts", "**/scratch.*", "# ===== 性能分析 =====", "**/.clinic", "**/.0x", "**/flamegraph.html", "**/perf.data*", "# ===== 其他工具 =====", "**/.history", "**/.vite-inspect", "**/.rush"]