graph TB
classDef package-node fill:#a29bfe,stroke:#2d3436,shape:ellipse
classDef package-scope-node fill:#ffeaa7,stroke:#2d3436,shape:stadium

  %% Package Scopes

  %% Node Definitions & Styles
  node0["JYZS"];
  style node0 fill:#74b9ff,stroke:#333,stroke-width:1px
  node1["cunzhi-memory"];
  style node1 fill:#74b9ff,stroke:#333,stroke-width:1px
  node2["idea"];
  style node2 fill:#74b9ff,stroke:#333,stroke-width:1px
  node3["doc"];
  style node3 fill:#74b9ff,stroke:#333,stroke-width:1px
  node4["ebbinghaus-backend"];
  style node4 fill:#74b9ff,stroke:#333,stroke-width:1px
  node5["ebbinghaus-learning-system"];
  style node5 fill:#74b9ff,stroke:#333,stroke-width:1px
  node6["______doc"];
  style node6 fill:#74b9ff,stroke:#333,stroke-width:1px
  node7["inspectionProfiles"];
  style node7 fill:#74b9ff,stroke:#333,stroke-width:1px
  node8["coverage"];
  style node8 fill:#74b9ff,stroke:#333,stroke-width:1px
  node9["docs"];
  style node9 fill:#74b9ff,stroke:#333,stroke-width:1px
  node10["ebbinghaus-learning-system"];
  style node10 fill:#74b9ff,stroke:#333,stroke-width:1px
  node11["logs"];
  style node11 fill:#74b9ff,stroke:#333,stroke-width:1px
  node12["package.json"];
  style node12 fill:#81ecec,stroke:#333,stroke-width:1px
  node13["scripts"];
  style node13 fill:#74b9ff,stroke:#333,stroke-width:1px
  node14["src"];
  style node14 fill:#74b9ff,stroke:#333,stroke-width:1px
  node15["tests"];
  style node15 fill:#74b9ff,stroke:#333,stroke-width:1px
  node16["tsconfig.json"];
  style node16 fill:#81ecec,stroke:#333,stroke-width:1px
  node17["dist"];
  style node17 fill:#74b9ff,stroke:#333,stroke-width:1px
  node18["docs"];
  style node18 fill:#74b9ff,stroke:#333,stroke-width:1px
  node19["env.d.ts"];
  style node19 fill:#74b9ff,stroke:#333,stroke-width:1px
  node20["package.json"];
  style node20 fill:#81ecec,stroke:#333,stroke-width:1px
  node21["src"];
  style node21 fill:#74b9ff,stroke:#333,stroke-width:1px
  node22["tsconfig.json"];
  style node22 fill:#81ecec,stroke:#333,stroke-width:1px
  node23["vite.config.ts"];
  style node23 fill:#74b9ff,stroke:#333,stroke-width:1px
  node24["01-____"];
  style node24 fill:#74b9ff,stroke:#333,stroke-width:1px
  node25["02-____"];
  style node25 fill:#74b9ff,stroke:#333,stroke-width:1px
  node26["03-____"];
  style node26 fill:#74b9ff,stroke:#333,stroke-width:1px

  %% Edge Definitions
  node0 --> node1
  linkStyle 0 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node2
  linkStyle 1 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node3
  linkStyle 2 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node4
  linkStyle 3 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node5
  linkStyle 4 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node6
  linkStyle 5 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node2 --> node7
  linkStyle 6 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node4 --> node8
  linkStyle 7 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node4 --> node9
  linkStyle 8 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node4 --> node10
  linkStyle 9 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node4 --> node11
  linkStyle 10 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node4 --> node12
  linkStyle 11 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node4 --> node13
  linkStyle 12 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node4 --> node14
  linkStyle 13 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node4 --> node15
  linkStyle 14 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node4 --> node16
  linkStyle 15 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node17
  linkStyle 16 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node18
  linkStyle 17 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node19
  linkStyle 18 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node20
  linkStyle 19 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node21
  linkStyle 20 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node22
  linkStyle 21 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node5 --> node23
  linkStyle 22 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node24
  linkStyle 23 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node25
  linkStyle 24 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node6 --> node26
  linkStyle 25 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5