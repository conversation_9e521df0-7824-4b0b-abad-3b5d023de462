graph TB
classDef package-node fill:#a29bfe,stroke:#2d3436,shape:ellipse
classDef package-scope-node fill:#ffeaa7,stroke:#2d3436,shape:stadium

  %% Package Scopes

  %% Node Definitions & Styles
  node0["JYZS"];
  style node0 fill:#74b9ff,stroke:#333,stroke-width:1px
  node1["doc"];
  style node1 fill:#74b9ff,stroke:#333,stroke-width:1px
  node2["docs"];
  style node2 fill:#74b9ff,stroke:#333,stroke-width:1px
  node3["ebbinghaus-backend"];
  style node3 fill:#74b9ff,stroke:#333,stroke-width:1px
  node4["ebbinghaus-learning-system"];
  style node4 fill:#74b9ff,stroke:#333,stroke-width:1px
  node5["scripts"];
  style node5 fill:#74b9ff,stroke:#333,stroke-width:1px
  node6["tests"];
  style node6 fill:#74b9ff,stroke:#333,stroke-width:1px
  node7["______doc"];
  style node7 fill:#74b9ff,stroke:#333,stroke-width:1px
  node8["docs"];
  style node8 fill:#74b9ff,stroke:#333,stroke-width:1px
  node9["scripts"];
  style node9 fill:#74b9ff,stroke:#333,stroke-width:1px
  node10["src"];
  style node10 fill:#74b9ff,stroke:#333,stroke-width:1px
  node11["tests"];
  style node11 fill:#74b9ff,stroke:#333,stroke-width:1px
  node12["docs"];
  style node12 fill:#74b9ff,stroke:#333,stroke-width:1px
  node13["src"];
  style node13 fill:#74b9ff,stroke:#333,stroke-width:1px
  node14["01-____"];
  style node14 fill:#74b9ff,stroke:#333,stroke-width:1px
  node15["02-____"];
  style node15 fill:#74b9ff,stroke:#333,stroke-width:1px
  node16["03-____"];
  style node16 fill:#74b9ff,stroke:#333,stroke-width:1px

  %% Edge Definitions
  node0 --> node1
  linkStyle 0 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node2
  linkStyle 1 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node3
  linkStyle 2 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node4
  linkStyle 3 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node5
  linkStyle 4 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node6
  linkStyle 5 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node0 --> node7
  linkStyle 6 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node3 --> node8
  linkStyle 7 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node3 --> node9
  linkStyle 8 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node3 --> node10
  linkStyle 9 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node3 --> node11
  linkStyle 10 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node4 --> node12
  linkStyle 11 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node4 --> node13
  linkStyle 12 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node7 --> node14
  linkStyle 13 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node7 --> node15
  linkStyle 14 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5
  node7 --> node16
  linkStyle 15 stroke:#dfe4ea,stroke-width:2px,stroke-dasharray:5,5