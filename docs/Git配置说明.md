# JYZS 项目 Git 配置说明

## 📋 概述

本文档说明了 JYZS 艾宾浩斯学习系统项目的 Git 配置和使用规范。

## 🔧 配置文件说明

### .gitignore 文件

项目包含三个层级的 `.gitignore` 文件：

1. **根目录 `.gitignore`** - 全局过滤规则
   - 依赖和包管理文件
   - 环境变量和配置文件
   - 构建输出和缓存
   - FileScopeMCP 分析文件
   - 临时文件和备份

2. **前端 `.gitignore`** - 前端项目特定规则
   - Vue/Vite 构建文件
   - 前端测试覆盖率
   - 前端缓存文件

3. **后端 `.gitignore`** - 后端项目特定规则
   - Node.js 后端文件
   - 数据库文件
   - 日志文件

### .gitattributes 文件

配置文件类型和处理规则：

- **行尾处理**: 自动转换行尾符
- **二进制文件**: 正确识别图片、字体等
- **语言检测**: 确保 GitHub 正确识别项目语言
- **合并策略**: 特定文件的合并规则

## 🚀 快速开始

### 1. 初始化 Git 配置

```bash
# 运行配置脚本
powershell -ExecutionPolicy Bypass -File "scripts/git-setup.ps1"

# 或使用快速命令
scripts/git-quick.bat setup
```

### 2. 基本 Git 操作

```bash
# 查看状态
scripts/git-quick.bat status

# 添加所有更改
scripts/git-quick.bat add

# 提交更改
scripts/git-quick.bat commit "feat: 添加新功能"

# 推送到远程
scripts/git-quick.bat push
```

## 📝 提交规范

### 提交信息格式

```
<type>(<scope>): <description>

[optional body]

[optional footer]
```

### 类型说明

- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 示例

```bash
feat(frontend): 添加任务创建功能
fix(backend): 修复用户认证问题
docs: 更新 API 文档
style(frontend): 统一代码格式
refactor(backend): 重构数据库连接逻辑
```

## 🔒 安全规则

### 敏感文件保护

以下文件类型被自动排除：

- 环境变量文件 (`.env*`)
- 密钥文件 (`.key`, `.pem`, `.cert`)
- 配置文件 (`config.json`)
- 数据库文件 (`.db`, `.sqlite`)

### 大文件处理

- 超过 10MB 的文件需要使用 Git LFS
- 压缩文件和媒体文件自动使用 LFS

## 🌿 分支策略

### 主要分支

- `main`: 主分支，稳定版本
- `develop`: 开发分支，集成新功能
- `feature/*`: 功能分支
- `hotfix/*`: 热修复分支

### 分支命名规范

```
feature/task-management    # 功能分支
hotfix/login-bug          # 修复分支
release/v1.0.0            # 发布分支
```

## 🔄 工作流程

### 1. 功能开发流程

```bash
# 1. 创建功能分支
git checkout -b feature/new-feature

# 2. 开发和提交
git add .
git commit -m "feat: 实现新功能"

# 3. 推送分支
git push -u origin feature/new-feature

# 4. 创建 Pull Request
# 5. 代码审查和合并
```

### 2. 日常维护

```bash
# 同步主分支
git checkout main
git pull origin main

# 清理本地分支
git branch -d feature/completed-feature

# 清理远程跟踪分支
git remote prune origin
```

## 🛠️ 工具集成

### VS Code 集成

推荐安装以下扩展：

- GitLens
- Git Graph
- Git History

### 配置文件

```json
{
  "git.autofetch": true,
  "git.confirmSync": false,
  "git.enableSmartCommit": true,
  "gitlens.codeLens.enabled": true
}
```

## 📊 统计和分析

### 查看项目统计

```bash
# 提交统计
git shortlog -sn

# 文件变更统计
git log --stat

# 代码行数统计
git ls-files | xargs wc -l
```

## 🚨 常见问题

### 1. 行尾符问题

```bash
# 统一转换行尾符
git config core.autocrlf true
```

### 2. 文件权限问题

```bash
# 忽略文件权限变更
git config core.filemode false
```

### 3. 中文文件名问题

```bash
# 正确显示中文文件名
git config core.quotepath false
```

## 📚 参考资源

- [Git 官方文档](https://git-scm.com/doc)
- [GitHub Flow](https://guides.github.com/introduction/flow/)
- [Conventional Commits](https://www.conventionalcommits.org/)
- [Git LFS](https://git-lfs.github.io/)

---

**维护者**: JYZS 开发团队  
**更新时间**: 2025-08-04  
**版本**: 1.0.0
