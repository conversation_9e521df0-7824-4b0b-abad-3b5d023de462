{"name": "ebbinghaus-learning-system-monorepo", "version": "1.0.0", "description": "艾宾浩斯记忆曲线学习管理系统 - Monorepo", "private": true, "workspaces": ["ebbinghaus-backend", "ebbinghaus-learning-system"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "npm run dev --workspace=ebbinghaus-backend", "dev:frontend": "npm run dev --workspace=ebbinghaus-learning-system", "build": "npm run build --workspace=ebbinghaus-backend && npm run build --workspace=ebbinghaus-learning-system", "test": "npm run test --workspaces", "clean": "npm run clean --workspaces", "install:all": "npm install"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "author": "JYZS Team", "license": "MIT", "dependencies": {"axios": "^1.11.0"}}