graph TB
classDef package-node fill:#a29bfe,stroke:#2d3436,shape:ellipse
classDef package-scope-node fill:#ffeaa7,stroke:#2d3436,shape:stadium

  %% Package Scopes

  %% Node Definitions & Styles
  node0["ebbinghaus-learning-system"];
  style node0 fill:#74b9ff,stroke:#333,stroke-width:1px
  node1["dist"];
  style node1 fill:#74b9ff,stroke:#333,stroke-width:1px
  node2["docs"];
  style node2 fill:#74b9ff,stroke:#333,stroke-width:1px
  node3["env.d.ts"];
  style node3 fill:#81ecec,stroke:#333,stroke-width:1px
  node4["index.html"];
  style node4 fill:#81ecec,stroke:#333,stroke-width:1px
  node5["package.json"];
  style node5 fill:#81ecec,stroke:#333,stroke-width:1px
  node6["src"];
  style node6 fill:#74b9ff,stroke:#333,stroke-width:1px
  node7["tsconfig.json"];
  style node7 fill:#81ecec,stroke:#333,stroke-width:1px
  node8["vite.config.ts"];
  style node8 fill:#81ecec,stroke:#333,stroke-width:1px
  node9["assets"];
  style node9 fill:#74b9ff,stroke:#333,stroke-width:1px
  node10["index.html"];
  style node10 fill:#81ecec,stroke:#333,stroke-width:1px
  node11["ApiTestView-fYkpcgSW.js"];
  style node11 fill:#81ecec,stroke:#333,stroke-width:1px
  node12["AppLayout-BNYRYDsa.js"];
  style node12 fill:#81ecec,stroke:#333,stroke-width:1px
  node13["ComponentTestView-D4pzNUcT.js"];
  style node13 fill:#81ecec,stroke:#333,stroke-width:1px
  node14["DashboardView-BNy4ZNFB.js"];
  style node14 fill:#81ecec,stroke:#333,stroke-width:1px
  node15["HomeView-BucT7Hhy.js"];
  style node15 fill:#81ecec,stroke:#333,stroke-width:1px
  node16["index-DfjgeC5z.js"];
  style node16 fill:#81ecec,stroke:#333,stroke-width:1px
  node17["index-Oeuw5Cvn.js"];
  style node17 fill:#74b9ff,stroke:#333,stroke-width:1px
  node18["MindMapCard-CqqMxIGg.js"];
  style node18 fill:#81ecec,stroke:#333,stroke-width:1px
  node19["MindMapDetailView-DwawTnhb.js"];
  style node19 fill:#81ecec,stroke:#333,stroke-width:1px
  node20["MindMapsView-BPcPzjj9.js"];
  style node20 fill:#81ecec,stroke:#333,stroke-width:1px
  node21["MindMapViewer-BPFIGvLL.js"];
  style node21 fill:#81ecec,stroke:#333,stroke-width:1px
  node22["ReviewCard-Df-3j-Bk.js"];
  style node22 fill:#81ecec,stroke:#333,stroke-width:1px
  node23["ReviewSessionView-F8ureHw_.js"];
  style node23 fill:#81ecec,stroke:#333,stroke-width:1px
  node24["ReviewsView-BnKJe1Gi.js"];
  style node24 fill:#81ecec,stroke:#333,stroke-width:1px
  node25["StoreTestView-CYNGWrzo.js"];
  style node25 fill:#81ecec,stroke:#333,stroke-width:1px
  node26["TaskDetailView-C3q_qLqY.js"];
  style node26 fill:#81ecec,stroke:#333,stroke-width:1px
  node27["TaskList-Vc7eSG33.js"];
  style node27 fill:#81ecec,stroke:#333,stroke-width:1px
  node28["TasksView-crze5JO2.js"];
  style node28 fill:#81ecec,stroke:#333,stroke-width:1px
  node29["TestView-DWL-s5qg.js"];
  style node29 fill:#81ecec,stroke:#333,stroke-width:1px
  node30["App.vue"];
  style node30 fill:#74b9ff,stroke:#333,stroke-width:1px
  node31["components"];
  style node31 fill:#74b9ff,stroke:#333,stroke-width:1px
  node32["constants"];
  style node32 fill:#74b9ff,stroke:#333,stroke-width:1px
  node33["main.ts"];
  style node33 fill:#ff7675,stroke:#333,stroke-width:1px
  node34["router"];
  style node34 fill:#74b9ff,stroke:#333,stroke-width:1px
  node35["services"];
  style node35 fill:#74b9ff,stroke:#333,stroke-width:1px
  node36["stores"];
  style node36 fill:#74b9ff,stroke:#333,stroke-width:1px
  node37["styles"];
  style node37 fill:#74b9ff,stroke:#333,stroke-width:1px
  node38["types"];
  style node38 fill:#74b9ff,stroke:#333,stroke-width:1px
  node39["utils"];
  style node39 fill:#74b9ff,stroke:#333,stroke-width:1px
  node40["views"];
  style node40 fill:#74b9ff,stroke:#333,stroke-width:1px
  node41["business"];
  style node41 fill:#74b9ff,stroke:#333,stroke-width:1px
  node42["common"];
  style node42 fill:#74b9ff,stroke:#333,stroke-width:1px
  node43["index.ts"];
  style node43 fill:#fdcb6e,stroke:#333,stroke-width:1px
  node44["layout"];
  style node44 fill:#74b9ff,stroke:#333,stroke-width:1px
  node45["mindmap"];
  style node45 fill:#74b9ff,stroke:#333,stroke-width:1px
  node46["MindMapCard.vue"];
  style node46 fill:#81ecec,stroke:#333,stroke-width:1px
  node47["MindMapEdge.vue"];
  style node47 fill:#81ecec,stroke:#333,stroke-width:1px
  node48["MindMapNode.vue"];
  style node48 fill:#81ecec,stroke:#333,stroke-width:1px
  node49["MindMapViewer.vue"];
  style node49 fill:#81ecec,stroke:#333,stroke-width:1px
  node50["ReviewCard.vue"];
  style node50 fill:#81ecec,stroke:#333,stroke-width:1px
  node51["TaskCard.vue"];
  style node51 fill:#81ecec,stroke:#333,stroke-width:1px
  node52["TaskList.vue"];
  style node52 fill:#81ecec,stroke:#333,stroke-width:1px
  node53["AppHeader.vue"];
  style node53 fill:#81ecec,stroke:#333,stroke-width:1px
  node54["AppLayout.vue"];
  style node54 fill:#81ecec,stroke:#333,stroke-width:1px
  node55["AppNotifications.vue"];
  style node55 fill:#81ecec,stroke:#333,stroke-width:1px
  node56["AppSidebar.vue"];
  style node56 fill:#81ecec,stroke:#333,stroke-width:1px
  node57["index.ts"];
  style node57 fill:#fdcb6e,stroke:#333,stroke-width:1px
  node58["index.ts"];
  style node58 fill:#ff7675,stroke:#333,stroke-width:1px
  node59["api"];
  style node59 fill:#74b9ff,stroke:#333,stroke-width:1px
  node60["analytics.ts"];
  style node60 fill:#fdcb6e,stroke:#333,stroke-width:1px
  node61["index.ts"];
  style node61 fill:#e17055,stroke:#333,stroke-width:1px
  node62["mindmap.ts"];
  style node62 fill:#fdcb6e,stroke:#333,stroke-width:1px
  node63["notification.ts"];
  style node63 fill:#fdcb6e,stroke:#333,stroke-width:1px
  node64["review.ts"];
  style node64 fill:#fdcb6e,stroke:#333,stroke-width:1px
  node65["task.ts"];
  style node65 fill:#fdcb6e,stroke:#333,stroke-width:1px
  node66["user.ts"];
  style node66 fill:#fdcb6e,stroke:#333,stroke-width:1px
  node67["app.ts"];
  style node67 fill:#ff7675,stroke:#333,stroke-width:1px
  node68["index.ts"];
  style node68 fill:#e17055,stroke:#333,stroke-width:1px
  node69["mindmap.ts"];
  style node69 fill:#a29bfe,stroke:#333,stroke-width:1px
  node70["review.ts"];
  style node70 fill:#a29bfe,stroke:#333,stroke-width:1px
  node71["task.ts"];
  style node71 fill:#a29bfe,stroke:#333,stroke-width:1px
  node72["user.ts"];
  style node72 fill:#a29bfe,stroke:#333,stroke-width:1px
  node73["index.css"];
  style node73 fill:#74b9ff,stroke:#333,stroke-width:1px
  node74["index.ts"];
  style node74 fill:#fdcb6e,stroke:#333,stroke-width:1px
  node75["http.ts"];
  style node75 fill:#00b894,stroke:#333,stroke-width:1px

  %% Edge Definitions
  node0 --> node1
  linkStyle 0 stroke:#636e72,stroke-width:1px
  node0 --> node2
  linkStyle 1 stroke:#636e72,stroke-width:1px
  node0 --> node3
  linkStyle 2 stroke:#636e72,stroke-width:1px
  node0 --> node4
  linkStyle 3 stroke:#636e72,stroke-width:1px
  node0 --> node5
  linkStyle 4 stroke:#636e72,stroke-width:1px
  node0 --> node6
  linkStyle 5 stroke:#636e72,stroke-width:1px
  node0 --> node7
  linkStyle 6 stroke:#636e72,stroke-width:1px
  node0 --> node8
  linkStyle 7 stroke:#636e72,stroke-width:1px
  node1 --> node9
  linkStyle 8 stroke:#636e72,stroke-width:1px
  node1 --> node10
  linkStyle 9 stroke:#636e72,stroke-width:1px
  node9 --> node11
  linkStyle 10 stroke:#636e72,stroke-width:1px
  node9 --> node12
  linkStyle 11 stroke:#636e72,stroke-width:1px
  node9 --> node13
  linkStyle 12 stroke:#636e72,stroke-width:1px
  node9 --> node14
  linkStyle 13 stroke:#636e72,stroke-width:1px
  node9 --> node15
  linkStyle 14 stroke:#636e72,stroke-width:1px
  node9 --> node16
  linkStyle 15 stroke:#636e72,stroke-width:1px
  node9 --> node17
  linkStyle 16 stroke:#636e72,stroke-width:1px
  node9 --> node18
  linkStyle 17 stroke:#636e72,stroke-width:1px
  node9 --> node19
  linkStyle 18 stroke:#636e72,stroke-width:1px
  node9 --> node20
  linkStyle 19 stroke:#636e72,stroke-width:1px
  node9 --> node21
  linkStyle 20 stroke:#636e72,stroke-width:1px
  node9 --> node22
  linkStyle 21 stroke:#636e72,stroke-width:1px
  node9 --> node23
  linkStyle 22 stroke:#636e72,stroke-width:1px
  node9 --> node24
  linkStyle 23 stroke:#636e72,stroke-width:1px
  node9 --> node25
  linkStyle 24 stroke:#636e72,stroke-width:1px
  node9 --> node26
  linkStyle 25 stroke:#636e72,stroke-width:1px
  node9 --> node27
  linkStyle 26 stroke:#636e72,stroke-width:1px
  node9 --> node28
  linkStyle 27 stroke:#636e72,stroke-width:1px
  node9 --> node29
  linkStyle 28 stroke:#636e72,stroke-width:1px
  node6 --> node30
  linkStyle 29 stroke:#636e72,stroke-width:1px
  node6 --> node31
  linkStyle 30 stroke:#636e72,stroke-width:1px
  node6 --> node32
  linkStyle 31 stroke:#636e72,stroke-width:1px
  node6 --> node33
  linkStyle 32 stroke:#636e72,stroke-width:1px
  node6 --> node34
  linkStyle 33 stroke:#636e72,stroke-width:1px
  node6 --> node35
  linkStyle 34 stroke:#636e72,stroke-width:1px
  node6 --> node36
  linkStyle 35 stroke:#636e72,stroke-width:1px
  node6 --> node37
  linkStyle 36 stroke:#636e72,stroke-width:1px
  node6 --> node38
  linkStyle 37 stroke:#636e72,stroke-width:1px
  node6 --> node39
  linkStyle 38 stroke:#636e72,stroke-width:1px
  node6 --> node40
  linkStyle 39 stroke:#636e72,stroke-width:1px
  node31 --> node41
  linkStyle 40 stroke:#636e72,stroke-width:1px
  node31 --> node42
  linkStyle 41 stroke:#636e72,stroke-width:1px
  node31 --> node43
  linkStyle 42 stroke:#636e72,stroke-width:1px
  node31 --> node44
  linkStyle 43 stroke:#636e72,stroke-width:1px
  node31 --> node45
  linkStyle 44 stroke:#636e72,stroke-width:1px
  node41 --> node46
  linkStyle 45 stroke:#636e72,stroke-width:1px
  node41 --> node47
  linkStyle 46 stroke:#636e72,stroke-width:1px
  node41 --> node48
  linkStyle 47 stroke:#636e72,stroke-width:1px
  node41 --> node49
  linkStyle 48 stroke:#636e72,stroke-width:1px
  node41 --> node50
  linkStyle 49 stroke:#636e72,stroke-width:1px
  node41 --> node51
  linkStyle 50 stroke:#636e72,stroke-width:1px
  node41 --> node52
  linkStyle 51 stroke:#636e72,stroke-width:1px
  node44 --> node53
  linkStyle 52 stroke:#636e72,stroke-width:1px
  node44 --> node54
  linkStyle 53 stroke:#636e72,stroke-width:1px
  node44 --> node55
  linkStyle 54 stroke:#636e72,stroke-width:1px
  node44 --> node56
  linkStyle 55 stroke:#636e72,stroke-width:1px
  node32 --> node57
  linkStyle 56 stroke:#636e72,stroke-width:1px
  node34 --> node58
  linkStyle 57 stroke:#636e72,stroke-width:1px
  node35 --> node59
  linkStyle 58 stroke:#636e72,stroke-width:1px
  node59 --> node60
  linkStyle 59 stroke:#636e72,stroke-width:1px
  node59 --> node61
  linkStyle 60 stroke:#636e72,stroke-width:1px
  node59 --> node62
  linkStyle 61 stroke:#636e72,stroke-width:1px
  node59 --> node63
  linkStyle 62 stroke:#636e72,stroke-width:1px
  node59 --> node64
  linkStyle 63 stroke:#636e72,stroke-width:1px
  node59 --> node65
  linkStyle 64 stroke:#636e72,stroke-width:1px
  node59 --> node66
  linkStyle 65 stroke:#636e72,stroke-width:1px
  node36 --> node67
  linkStyle 66 stroke:#636e72,stroke-width:1px
  node36 --> node68
  linkStyle 67 stroke:#636e72,stroke-width:1px
  node36 --> node69
  linkStyle 68 stroke:#636e72,stroke-width:1px
  node36 --> node70
  linkStyle 69 stroke:#636e72,stroke-width:1px
  node36 --> node71
  linkStyle 70 stroke:#636e72,stroke-width:1px
  node36 --> node72
  linkStyle 71 stroke:#636e72,stroke-width:1px
  node37 --> node73
  linkStyle 72 stroke:#636e72,stroke-width:1px
  node38 --> node74
  linkStyle 73 stroke:#636e72,stroke-width:1px
  node39 --> node75
  linkStyle 74 stroke:#636e72,stroke-width:1px
