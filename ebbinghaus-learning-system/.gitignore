# ===================================
# 前端项目 Git 过滤规则
# ===================================

# ===== 依赖 =====
node_modules/
.pnp
.pnp.js

# ===== 构建输出 =====
dist/
dist-ssr/
*.local

# ===== 环境变量 =====
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
# 注意：.env.development 和 .env.production 被保留在版本控制中

# ===== 日志 =====
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# ===== 编辑器 =====
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# ===== 缓存 =====
.cache/
.parcel-cache/
.vite/
.rollup.cache/
.eslintcache
.stylelintcache

# ===== 测试 =====
coverage/
.nyc_output/
test-results/
playwright-report/
playwright/.cache/

# ===== TypeScript =====
*.tsbuildinfo

# ===== 临时文件 =====
*.tmp
*.temp
.tmp/
.temp/

# ===== 特定文件 =====
# FileScopeMCP 分析文件
FileScopeMCP-tree-*.json

# 备份文件
*.backup
*.bak
*.old

# 压缩文件
*.zip
*.tar.gz
