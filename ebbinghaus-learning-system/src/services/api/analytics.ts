import { httpClient } from '@/utils/http'
import type { 
  StudyAnalytics,
  AnalyticsPeriod,
  PerformanceLevel,
  ApiResponse
} from '@/types'

/**
 * 分析统计API服务
 */
export class AnalyticsService {
  private static readonly BASE_URL = '/analytics'

  /**
   * 获取学习分析数据
   */
  static async getStudyAnalytics(params?: {
    period?: AnalyticsPeriod
    startDate?: string
    endDate?: string
    groupBy?: 'day' | 'week' | 'month'
  }): Promise<ApiResponse<StudyAnalytics>> {
    return httpClient.get(`${this.BASE_URL}/study`, { params })
  }

  /**
   * 获取仪表板数据
   */
  static async getDashboardData(): Promise<ApiResponse<{
    overview: {
      totalTasks: number
      completedTasks: number
      totalStudyTime: number
      streakDays: number
      todayProgress: {
        tasks: number
        reviews: number
        timeSpent: number
        goalProgress: number
      }
    }
    recentActivity: Array<{
      type: 'task' | 'review' | 'mindmap'
      action: string
      title: string
      timestamp: string
    }>
    upcomingReviews: Array<{
      taskTitle: string
      dueDate: string
      priority: string
    }>
    performanceTrend: Array<{
      date: string
      performance: number
      timeSpent: number
    }>
    categoryDistribution: Array<{
      category: string
      percentage: number
      timeSpent: number
    }>
  }>> {
    return httpClient.get(`${this.BASE_URL}/dashboard`)
  }

  /**
   * 获取学习时间统计
   */
  static async getTimeStats(params?: {
    period?: AnalyticsPeriod
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<{
    totalTime: number
    averageDaily: number
    longestSession: number
    totalSessions: number
    timeByCategory: Array<{
      category: string
      time: number
      percentage: number
    }>
    timeByDifficulty: Array<{
      difficulty: string
      time: number
      percentage: number
    }>
    dailyTrend: Array<{
      date: string
      time: number
      sessions: number
    }>
    hourlyDistribution: Array<{
      hour: number
      time: number
      sessions: number
    }>
  }>> {
    return httpClient.get(`${this.BASE_URL}/time`, { params })
  }

  /**
   * 获取任务完成统计
   */
  static async getTaskStats(params?: {
    period?: AnalyticsPeriod
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<{
    totalTasks: number
    completedTasks: number
    completionRate: number
    averageCompletionTime: number
    tasksByStatus: Record<string, number>
    tasksByType: Array<{
      type: string
      total: number
      completed: number
      completionRate: number
    }>
    tasksByDifficulty: Array<{
      difficulty: string
      total: number
      completed: number
      averageTime: number
    }>
    completionTrend: Array<{
      date: string
      completed: number
      total: number
      rate: number
    }>
  }>> {
    return httpClient.get(`${this.BASE_URL}/tasks`, { params })
  }

  /**
   * 获取复习效果统计
   */
  static async getReviewStats(params?: {
    period?: AnalyticsPeriod
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<{
    totalReviews: number
    completedReviews: number
    averagePerformance: PerformanceLevel
    retentionRate: number
    onTimeRate: number
    performanceDistribution: Record<PerformanceLevel, number>
    reviewsByStage: Array<{
      stage: number
      reviews: number
      averagePerformance: number
      retentionRate: number
    }>
    efficiencyTrend: Array<{
      date: string
      reviews: number
      performance: number
      retentionRate: number
    }>
    intervalAnalysis: Array<{
      interval: number
      reviews: number
      successRate: number
      averagePerformance: number
    }>
  }>> {
    return httpClient.get(`${this.BASE_URL}/reviews`, { params })
  }

  /**
   * 获取学习模式分析
   */
  static async getLearningPatterns(): Promise<ApiResponse<{
    preferredTimes: Array<{
      hour: number
      frequency: number
      performance: number
    }>
    sessionPatterns: {
      averageLength: number
      optimalLength: number
      breakFrequency: number
    }
    difficultyProgression: Array<{
      week: number
      averageDifficulty: number
      completionRate: number
    }>
    topicFocus: Array<{
      topic: string
      timeSpent: number
      mastery: number
      trend: 'improving' | 'stable' | 'declining'
    }>
    learningVelocity: {
      current: number
      trend: number
      prediction: number
    }
  }>> {
    return httpClient.get(`${this.BASE_URL}/patterns`)
  }

  /**
   * 获取成就和里程碑
   */
  static async getAchievements(): Promise<ApiResponse<{
    recent: Array<{
      id: string
      name: string
      description: string
      unlockedAt: string
      rarity: string
    }>
    progress: Array<{
      id: string
      name: string
      description: string
      progress: number
      target: number
      estimatedCompletion?: string
    }>
    milestones: Array<{
      type: 'time' | 'tasks' | 'streak' | 'performance'
      value: number
      achievedAt: string
      description: string
    }>
    streaks: {
      current: number
      longest: number
      history: Array<{
        start: string
        end: string
        length: number
      }>
    }
  }>> {
    return httpClient.get(`${this.BASE_URL}/achievements`)
  }

  /**
   * 获取学习建议
   */
  static async getRecommendations(): Promise<ApiResponse<{
    studyTime: Array<{
      type: 'increase' | 'decrease' | 'maintain'
      reason: string
      suggestion: string
      impact: 'high' | 'medium' | 'low'
    }>
    difficulty: Array<{
      type: 'increase' | 'decrease' | 'vary'
      reason: string
      suggestion: string
      topics?: string[]
    }>
    schedule: Array<{
      type: 'timing' | 'frequency' | 'duration'
      reason: string
      suggestion: string
      optimalValue?: number
    }>
    focus: Array<{
      type: 'strengthen' | 'review' | 'explore'
      topic: string
      reason: string
      priority: 'high' | 'medium' | 'low'
    }>
  }>> {
    return httpClient.get(`${this.BASE_URL}/recommendations`)
  }

  /**
   * 获取对比分析
   */
  static async getComparison(params?: {
    period1Start: string
    period1End: string
    period2Start: string
    period2End: string
  }): Promise<ApiResponse<{
    timeComparison: {
      period1: number
      period2: number
      change: number
      changePercent: number
    }
    taskComparison: {
      period1: number
      period2: number
      change: number
      changePercent: number
    }
    performanceComparison: {
      period1: number
      period2: number
      change: number
      changePercent: number
    }
    categoryChanges: Array<{
      category: string
      period1Time: number
      period2Time: number
      change: number
    }>
    insights: Array<{
      type: 'improvement' | 'decline' | 'stable'
      metric: string
      description: string
      significance: 'high' | 'medium' | 'low'
    }>
  }>> {
    return httpClient.get(`${this.BASE_URL}/comparison`, { params })
  }

  /**
   * 获取预测分析
   */
  static async getPredictions(): Promise<ApiResponse<{
    goalCompletion: Array<{
      goalType: string
      currentProgress: number
      predictedCompletion: string
      confidence: number
    }>
    performanceTrend: Array<{
      date: string
      predictedPerformance: number
      confidence: number
    }>
    retentionForecast: Array<{
      topic: string
      currentRetention: number
      predictedRetention: number
      recommendedReview: string
    }>
    burnoutRisk: {
      level: 'low' | 'medium' | 'high'
      factors: string[]
      recommendations: string[]
    }
  }>> {
    return httpClient.get(`${this.BASE_URL}/predictions`)
  }

  /**
   * 导出分析报告
   */
  static async exportReport(params: {
    type: 'comprehensive' | 'summary' | 'custom'
    period?: AnalyticsPeriod
    startDate?: string
    endDate?: string
    format?: 'pdf' | 'xlsx' | 'csv'
    sections?: string[]
  }): Promise<ApiResponse<{
    downloadUrl: string
    filename: string
  }>> {
    return httpClient.post(`${this.BASE_URL}/export`, params)
  }

  /**
   * 获取团队分析（如果支持团队功能）
   */
  static async getTeamAnalytics(teamId: string, params?: {
    period?: AnalyticsPeriod
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<{
    overview: {
      totalMembers: number
      activeMembers: number
      totalStudyTime: number
      averagePerformance: number
    }
    memberRanking: Array<{
      userId: string
      username: string
      studyTime: number
      tasksCompleted: number
      performance: number
      rank: number
    }>
    teamTrends: Array<{
      date: string
      activeMembers: number
      totalTime: number
      averagePerformance: number
    }>
    collaboration: {
      sharedMindMaps: number
      discussions: number
      helpRequests: number
    }
  }>> {
    return httpClient.get(`${this.BASE_URL}/team/${teamId}`, { params })
  }
}

// 导出默认实例
export default AnalyticsService
