import { httpClient } from '@/utils/http'
import type { 
  ReviewSchedule, 
  ReviewSession, 
  ReviewQueryParams,
  PerformanceLevel,
  ApiResponse,
  PaginationInfo
} from '@/types'

/**
 * 复习管理API服务
 */
export class ReviewService {
  private static readonly BASE_URL = '/reviews'

  /**
   * 获取复习计划列表
   */
  static async getReviewSchedules(params?: ReviewQueryParams): Promise<ApiResponse<{
    schedules: ReviewSchedule[]
    pagination: PaginationInfo
  }>> {
    return httpClient.get(`${this.BASE_URL}/schedules`, { params })
  }

  /**
   * 根据ID获取复习计划详情
   */
  static async getReviewScheduleById(id: string): Promise<ApiResponse<ReviewSchedule>> {
    return httpClient.get(`${this.BASE_URL}/schedules/${id}`)
  }

  /**
   * 创建复习计划
   */
  static async createReviewSchedule(data: {
    taskId: string
    customIntervals?: number[]
    startDate?: string
  }): Promise<ApiResponse<ReviewSchedule>> {
    return httpClient.post(`${this.BASE_URL}/schedules`, data)
  }

  /**
   * 更新复习计划
   */
  static async updateReviewSchedule(id: string, data: {
    intervals?: number[]
    nextReviewDate?: string
    isCompleted?: boolean
  }): Promise<ApiResponse<ReviewSchedule>> {
    return httpClient.put(`${this.BASE_URL}/schedules/${id}`, data)
  }

  /**
   * 删除复习计划
   */
  static async deleteReviewSchedule(id: string): Promise<ApiResponse<void>> {
    return httpClient.delete(`${this.BASE_URL}/schedules/${id}`)
  }

  /**
   * 完成复习阶段
   */
  static async completeReviewStage(scheduleId: string, data: {
    performance: PerformanceLevel
    timeSpent: number
    notes?: string
  }): Promise<ApiResponse<ReviewSchedule>> {
    return httpClient.post(`${this.BASE_URL}/schedules/${scheduleId}/complete-stage`, data)
  }

  /**
   * 跳过复习
   */
  static async skipReview(scheduleId: string, reason?: string): Promise<ApiResponse<ReviewSchedule>> {
    return httpClient.post(`${this.BASE_URL}/schedules/${scheduleId}/skip`, { reason })
  }

  /**
   * 重置复习计划
   */
  static async resetReviewSchedule(scheduleId: string): Promise<ApiResponse<ReviewSchedule>> {
    return httpClient.post(`${this.BASE_URL}/schedules/${scheduleId}/reset`)
  }

  /**
   * 获取今日复习任务
   */
  static async getTodayReviews(): Promise<ApiResponse<{
    pending: ReviewSchedule[]
    completed: ReviewSchedule[]
    overdue: ReviewSchedule[]
    total: number
  }>> {
    return httpClient.get(`${this.BASE_URL}/today`)
  }

  /**
   * 获取即将到期的复习任务
   */
  static async getUpcomingReviews(days: number = 7): Promise<ApiResponse<Array<{
    date: string
    reviews: ReviewSchedule[]
    count: number
  }>>> {
    return httpClient.get(`${this.BASE_URL}/upcoming`, { params: { days } })
  }

  /**
   * 获取过期的复习任务
   */
  static async getOverdueReviews(): Promise<ApiResponse<ReviewSchedule[]>> {
    return httpClient.get(`${this.BASE_URL}/overdue`)
  }

  /**
   * 开始复习会话
   */
  static async startReviewSession(data: {
    taskIds?: string[]
    scheduleIds?: string[]
    maxDuration?: number
    maxTasks?: number
  }): Promise<ApiResponse<ReviewSession>> {
    return httpClient.post(`${this.BASE_URL}/sessions/start`, data)
  }

  /**
   * 结束复习会话
   */
  static async endReviewSession(sessionId: string, data?: {
    notes?: string
  }): Promise<ApiResponse<ReviewSession>> {
    return httpClient.post(`${this.BASE_URL}/sessions/${sessionId}/end`, data)
  }

  /**
   * 获取复习会话详情
   */
  static async getReviewSession(sessionId: string): Promise<ApiResponse<ReviewSession>> {
    return httpClient.get(`${this.BASE_URL}/sessions/${sessionId}`)
  }

  /**
   * 获取复习会话历史
   */
  static async getReviewSessions(params?: {
    page?: number
    limit?: number
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<{
    sessions: ReviewSession[]
    pagination: PaginationInfo
  }>> {
    return httpClient.get(`${this.BASE_URL}/sessions`, { params })
  }

  /**
   * 获取复习统计
   */
  static async getReviewStats(params?: {
    startDate?: string
    endDate?: string
    groupBy?: 'day' | 'week' | 'month'
  }): Promise<ApiResponse<{
    totalReviews: number
    completedReviews: number
    averagePerformance: PerformanceLevel
    retentionRate: number
    streakDays: number
    timeSpent: number
    performanceDistribution: Record<PerformanceLevel, number>
    trends: Array<{
      date: string
      reviews: number
      completed: number
      averagePerformance: number
    }>
  }>> {
    return httpClient.get(`${this.BASE_URL}/stats`, { params })
  }

  /**
   * 获取复习效率分析
   */
  static async getReviewEfficiency(): Promise<ApiResponse<{
    onTimeRate: number
    improvementRate: number
    optimalIntervals: number[]
    recommendations: Array<{
      type: 'interval' | 'timing' | 'difficulty'
      message: string
      action?: string
    }>
  }>> {
    return httpClient.get(`${this.BASE_URL}/efficiency`)
  }

  /**
   * 获取记忆曲线数据
   */
  static async getMemoryCurve(taskId?: string): Promise<ApiResponse<{
    points: Array<{
      stage: number
      interval: number
      retentionRate: number
      performance: PerformanceLevel
    }>
    prediction: Array<{
      stage: number
      predictedRetention: number
      recommendedInterval: number
    }>
  }>> {
    return httpClient.get(`${this.BASE_URL}/memory-curve`, { 
      params: taskId ? { taskId } : undefined 
    })
  }

  /**
   * 批量创建复习计划
   */
  static async batchCreateReviewSchedules(data: {
    taskIds: string[]
    customIntervals?: number[]
    startDate?: string
  }): Promise<ApiResponse<{
    created: ReviewSchedule[]
    errors: Array<{
      taskId: string
      message: string
    }>
  }>> {
    return httpClient.post(`${this.BASE_URL}/schedules/batch-create`, data)
  }

  /**
   * 调整复习间隔
   */
  static async adjustReviewInterval(scheduleId: string, data: {
    newInterval: number
    reason?: string
  }): Promise<ApiResponse<ReviewSchedule>> {
    return httpClient.post(`${this.BASE_URL}/schedules/${scheduleId}/adjust-interval`, data)
  }

  /**
   * 获取复习提醒设置
   */
  static async getReviewReminders(): Promise<ApiResponse<{
    enabled: boolean
    times: string[]
    advanceDays: number
    methods: Array<'push' | 'email' | 'sms'>
  }>> {
    return httpClient.get(`${this.BASE_URL}/reminders`)
  }

  /**
   * 更新复习提醒设置
   */
  static async updateReviewReminders(data: {
    enabled?: boolean
    times?: string[]
    advanceDays?: number
    methods?: Array<'push' | 'email' | 'sms'>
  }): Promise<ApiResponse<void>> {
    return httpClient.put(`${this.BASE_URL}/reminders`, data)
  }

  /**
   * 获取复习日历
   */
  static async getReviewCalendar(params: {
    year: number
    month: number
  }): Promise<ApiResponse<Array<{
    date: string
    reviews: number
    completed: number
    performance: PerformanceLevel | null
  }>>> {
    return httpClient.get(`${this.BASE_URL}/calendar`, { params })
  }

  /**
   * 导出复习数据
   */
  static async exportReviewData(params?: {
    startDate?: string
    endDate?: string
    format?: 'json' | 'csv' | 'xlsx'
    includePerformance?: boolean
  }): Promise<ApiResponse<{
    downloadUrl: string
    filename: string
  }>> {
    return httpClient.post(`${this.BASE_URL}/export`, params)
  }
}

// 导出默认实例
export default ReviewService
