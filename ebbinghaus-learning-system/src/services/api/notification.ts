import { httpClient } from '@/utils/http'
import type { 
  Notification, 
  NotificationType,
  Priority,
  ApiResponse,
  PaginationInfo
} from '@/types'

/**
 * 通知管理API服务
 */
export class NotificationService {
  private static readonly BASE_URL = '/notifications'

  /**
   * 获取通知列表
   */
  static async getNotifications(params?: {
    page?: number
    limit?: number
    type?: NotificationType
    isRead?: boolean
    priority?: Priority
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<{
    notifications: Notification[]
    pagination: PaginationInfo
    unreadCount: number
  }>> {
    return httpClient.get(this.BASE_URL, { params })
  }

  /**
   * 根据ID获取通知详情
   */
  static async getNotificationById(id: string): Promise<ApiResponse<Notification>> {
    return httpClient.get(`${this.BASE_URL}/${id}`)
  }

  /**
   * 标记通知为已读
   */
  static async markAsRead(id: string): Promise<ApiResponse<void>> {
    return httpClient.put(`${this.BASE_URL}/${id}/read`)
  }

  /**
   * 标记通知为未读
   */
  static async markAsUnread(id: string): Promise<ApiResponse<void>> {
    return httpClient.put(`${this.BASE_URL}/${id}/unread`)
  }

  /**
   * 批量标记为已读
   */
  static async batchMarkAsRead(ids: string[]): Promise<ApiResponse<void>> {
    return httpClient.put(`${this.BASE_URL}/batch-read`, { ids })
  }

  /**
   * 标记所有通知为已读
   */
  static async markAllAsRead(): Promise<ApiResponse<void>> {
    return httpClient.put(`${this.BASE_URL}/read-all`)
  }

  /**
   * 删除通知
   */
  static async deleteNotification(id: string): Promise<ApiResponse<void>> {
    return httpClient.delete(`${this.BASE_URL}/${id}`)
  }

  /**
   * 批量删除通知
   */
  static async batchDeleteNotifications(ids: string[]): Promise<ApiResponse<void>> {
    return httpClient.delete(`${this.BASE_URL}/batch-delete`, { data: { ids } })
  }

  /**
   * 清空所有通知
   */
  static async clearAllNotifications(): Promise<ApiResponse<void>> {
    return httpClient.delete(`${this.BASE_URL}/clear-all`)
  }

  /**
   * 获取未读通知数量
   */
  static async getUnreadCount(): Promise<ApiResponse<{
    total: number
    byType: Record<NotificationType, number>
    byPriority: Record<Priority, number>
  }>> {
    return httpClient.get(`${this.BASE_URL}/unread-count`)
  }

  /**
   * 获取最新通知
   */
  static async getLatestNotifications(limit: number = 10): Promise<ApiResponse<Notification[]>> {
    return httpClient.get(`${this.BASE_URL}/latest`, { params: { limit } })
  }

  /**
   * 创建通知（管理员功能）
   */
  static async createNotification(data: {
    type: NotificationType
    title: string
    message: string
    priority: Priority
    actionUrl?: string
    metadata?: Record<string, any>
    targetUsers?: string[]
    scheduleAt?: string
  }): Promise<ApiResponse<Notification>> {
    return httpClient.post(this.BASE_URL, data)
  }

  /**
   * 获取通知设置
   */
  static async getNotificationSettings(): Promise<ApiResponse<{
    pushEnabled: boolean
    emailEnabled: boolean
    smsEnabled: boolean
    types: Record<NotificationType, {
      push: boolean
      email: boolean
      sms: boolean
    }>
    quietHours: {
      enabled: boolean
      start: string
      end: string
    }
    frequency: {
      immediate: boolean
      daily: boolean
      weekly: boolean
    }
  }>> {
    return httpClient.get(`${this.BASE_URL}/settings`)
  }

  /**
   * 更新通知设置
   */
  static async updateNotificationSettings(data: {
    pushEnabled?: boolean
    emailEnabled?: boolean
    smsEnabled?: boolean
    types?: Record<NotificationType, {
      push?: boolean
      email?: boolean
      sms?: boolean
    }>
    quietHours?: {
      enabled?: boolean
      start?: string
      end?: string
    }
    frequency?: {
      immediate?: boolean
      daily?: boolean
      weekly?: boolean
    }
  }): Promise<ApiResponse<void>> {
    return httpClient.put(`${this.BASE_URL}/settings`, data)
  }

  /**
   * 测试通知
   */
  static async testNotification(type: 'push' | 'email' | 'sms'): Promise<ApiResponse<void>> {
    return httpClient.post(`${this.BASE_URL}/test`, { type })
  }

  /**
   * 订阅推送通知
   */
  static async subscribePush(subscription: {
    endpoint: string
    keys: {
      p256dh: string
      auth: string
    }
  }): Promise<ApiResponse<void>> {
    return httpClient.post(`${this.BASE_URL}/push/subscribe`, subscription)
  }

  /**
   * 取消订阅推送通知
   */
  static async unsubscribePush(): Promise<ApiResponse<void>> {
    return httpClient.post(`${this.BASE_URL}/push/unsubscribe`)
  }

  /**
   * 获取通知模板
   */
  static async getNotificationTemplates(): Promise<ApiResponse<Array<{
    id: string
    type: NotificationType
    name: string
    title: string
    content: string
    variables: string[]
  }>>> {
    return httpClient.get(`${this.BASE_URL}/templates`)
  }

  /**
   * 获取通知统计
   */
  static async getNotificationStats(params?: {
    startDate?: string
    endDate?: string
    groupBy?: 'day' | 'week' | 'month'
  }): Promise<ApiResponse<{
    total: number
    read: number
    unread: number
    deleted: number
    byType: Record<NotificationType, number>
    byPriority: Record<Priority, number>
    trends: Array<{
      date: string
      sent: number
      read: number
      clicked: number
    }>
    engagement: {
      readRate: number
      clickRate: number
      averageReadTime: number
    }
  }>> {
    return httpClient.get(`${this.BASE_URL}/stats`, { params })
  }

  /**
   * 获取通知历史
   */
  static async getNotificationHistory(params?: {
    page?: number
    limit?: number
    type?: NotificationType
    action?: 'sent' | 'read' | 'clicked' | 'deleted'
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<{
    history: Array<{
      id: string
      notificationId: string
      action: string
      timestamp: string
      metadata?: any
    }>
    pagination: PaginationInfo
  }>> {
    return httpClient.get(`${this.BASE_URL}/history`, { params })
  }

  /**
   * 导出通知数据
   */
  static async exportNotifications(params?: {
    startDate?: string
    endDate?: string
    type?: NotificationType
    format?: 'json' | 'csv' | 'xlsx'
    includeContent?: boolean
  }): Promise<ApiResponse<{
    downloadUrl: string
    filename: string
  }>> {
    return httpClient.post(`${this.BASE_URL}/export`, params)
  }

  /**
   * 获取通知摘要
   */
  static async getNotificationSummary(): Promise<ApiResponse<{
    today: {
      sent: number
      read: number
      pending: number
    }
    thisWeek: {
      sent: number
      read: number
      averageReadTime: number
    }
    topTypes: Array<{
      type: NotificationType
      count: number
      readRate: number
    }>
    recentActivity: Array<{
      type: string
      count: number
      lastOccurred: string
    }>
  }>> {
    return httpClient.get(`${this.BASE_URL}/summary`)
  }

  /**
   * 重新发送通知
   */
  static async resendNotification(id: string): Promise<ApiResponse<void>> {
    return httpClient.post(`${this.BASE_URL}/${id}/resend`)
  }

  /**
   * 取消计划通知
   */
  static async cancelScheduledNotification(id: string): Promise<ApiResponse<void>> {
    return httpClient.post(`${this.BASE_URL}/${id}/cancel`)
  }

  /**
   * 获取计划通知列表
   */
  static async getScheduledNotifications(): Promise<ApiResponse<Array<{
    id: string
    type: NotificationType
    title: string
    scheduledAt: string
    status: 'pending' | 'sent' | 'cancelled'
    targetUsers: number
  }>>> {
    return httpClient.get(`${this.BASE_URL}/scheduled`)
  }
}

// 导出默认实例
export default NotificationService
