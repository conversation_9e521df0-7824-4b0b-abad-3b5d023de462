import { httpClient } from '@/utils/http'
import type { 
  MindMap, 
  MindMapNode, 
  MindMapEdge,
  CreateMindMapForm, 
  UpdateMindMapForm,
  ApiResponse,
  PaginationInfo
} from '@/types'

/**
 * 思维导图API服务
 */
export class MindMapService {
  private static readonly BASE_URL = '/mindmaps'

  /**
   * 获取思维导图列表
   */
  static async getMindMaps(params?: {
    page?: number
    limit?: number
    search?: string
    tags?: string[]
    isPublic?: boolean
    sortBy?: 'createdAt' | 'updatedAt' | 'title'
    sortOrder?: 'asc' | 'desc'
  }): Promise<ApiResponse<{
    mindMaps: MindMap[]
    pagination: PaginationInfo
  }>> {
    return httpClient.get(this.BASE_URL, { params })
  }

  /**
   * 根据ID获取思维导图详情
   */
  static async getMindMapById(id: string): Promise<ApiResponse<MindMap>> {
    return httpClient.get(`${this.BASE_URL}/${id}`)
  }

  /**
   * 创建新思维导图
   */
  static async createMindMap(data: CreateMindMapForm): Promise<ApiResponse<MindMap>> {
    return httpClient.post(this.BASE_URL, data)
  }

  /**
   * 更新思维导图
   */
  static async updateMindMap(data: UpdateMindMapForm): Promise<ApiResponse<MindMap>> {
    const { id, ...updateData } = data
    return httpClient.put(`${this.BASE_URL}/${id}`, updateData)
  }

  /**
   * 删除思维导图
   */
  static async deleteMindMap(id: string): Promise<ApiResponse<void>> {
    return httpClient.delete(`${this.BASE_URL}/${id}`)
  }

  /**
   * 复制思维导图
   */
  static async duplicateMindMap(id: string, data?: {
    title?: string
    isPublic?: boolean
  }): Promise<ApiResponse<MindMap>> {
    return httpClient.post(`${this.BASE_URL}/${id}/duplicate`, data)
  }

  /**
   * 保存思维导图内容
   */
  static async saveMindMapContent(id: string, data: {
    nodes: MindMapNode[]
    edges: MindMapEdge[]
    layout?: any
    style?: any
  }): Promise<ApiResponse<MindMap>> {
    return httpClient.put(`${this.BASE_URL}/${id}/content`, data)
  }

  /**
   * 添加节点
   */
  static async addNode(mindMapId: string, node: Omit<MindMapNode, 'id'>): Promise<ApiResponse<MindMapNode>> {
    return httpClient.post(`${this.BASE_URL}/${mindMapId}/nodes`, node)
  }

  /**
   * 更新节点
   */
  static async updateNode(mindMapId: string, nodeId: string, data: Partial<MindMapNode>): Promise<ApiResponse<MindMapNode>> {
    return httpClient.put(`${this.BASE_URL}/${mindMapId}/nodes/${nodeId}`, data)
  }

  /**
   * 删除节点
   */
  static async deleteNode(mindMapId: string, nodeId: string): Promise<ApiResponse<void>> {
    return httpClient.delete(`${this.BASE_URL}/${mindMapId}/nodes/${nodeId}`)
  }

  /**
   * 添加边
   */
  static async addEdge(mindMapId: string, edge: Omit<MindMapEdge, 'id'>): Promise<ApiResponse<MindMapEdge>> {
    return httpClient.post(`${this.BASE_URL}/${mindMapId}/edges`, edge)
  }

  /**
   * 更新边
   */
  static async updateEdge(mindMapId: string, edgeId: string, data: Partial<MindMapEdge>): Promise<ApiResponse<MindMapEdge>> {
    return httpClient.put(`${this.BASE_URL}/${mindMapId}/edges/${edgeId}`, data)
  }

  /**
   * 删除边
   */
  static async deleteEdge(mindMapId: string, edgeId: string): Promise<ApiResponse<void>> {
    return httpClient.delete(`${this.BASE_URL}/${mindMapId}/edges/${edgeId}`)
  }

  /**
   * 获取思维导图模板
   */
  static async getMindMapTemplates(): Promise<ApiResponse<Array<{
    id: string
    name: string
    description: string
    preview: string
    category: string
    template: Partial<MindMap>
  }>>> {
    return httpClient.get(`${this.BASE_URL}/templates`)
  }

  /**
   * 从模板创建思维导图
   */
  static async createFromTemplate(templateId: string, data?: {
    title?: string
    description?: string
    isPublic?: boolean
  }): Promise<ApiResponse<MindMap>> {
    return httpClient.post(`${this.BASE_URL}/templates/${templateId}/create`, data)
  }

  /**
   * 导出思维导图
   */
  static async exportMindMap(id: string, format: 'json' | 'png' | 'svg' | 'pdf' | 'xmind'): Promise<ApiResponse<{
    downloadUrl: string
    filename: string
  }>> {
    return httpClient.post(`${this.BASE_URL}/${id}/export`, { format })
  }

  /**
   * 导入思维导图
   */
  static async importMindMap(file: File, _options?: {
    title?: string
    isPublic?: boolean
  }): Promise<ApiResponse<MindMap>> {
    return httpClient.upload(`${this.BASE_URL}/import`, file)
  }

  /**
   * 分享思维导图
   */
  static async shareMindMap(id: string, data: {
    isPublic: boolean
    shareSettings?: {
      allowEdit?: boolean
      allowComment?: boolean
      allowDownload?: boolean
      expiresAt?: string
    }
  }): Promise<ApiResponse<{
    shareUrl: string
    shareCode: string
  }>> {
    return httpClient.post(`${this.BASE_URL}/${id}/share`, data)
  }

  /**
   * 获取分享信息
   */
  static async getShareInfo(id: string): Promise<ApiResponse<{
    isPublic: boolean
    shareUrl?: string
    shareCode?: string
    shareSettings?: any
  }>> {
    return httpClient.get(`${this.BASE_URL}/${id}/share`)
  }

  /**
   * 通过分享码访问思维导图
   */
  static async getSharedMindMap(shareCode: string): Promise<ApiResponse<MindMap>> {
    return httpClient.get(`${this.BASE_URL}/shared/${shareCode}`)
  }

  /**
   * 获取协作者列表
   */
  static async getCollaborators(id: string): Promise<ApiResponse<Array<{
    userId: string
    username: string
    avatar?: string
    role: 'owner' | 'editor' | 'viewer'
    joinedAt: string
  }>>> {
    return httpClient.get(`${this.BASE_URL}/${id}/collaborators`)
  }

  /**
   * 添加协作者
   */
  static async addCollaborator(id: string, data: {
    email: string
    role: 'editor' | 'viewer'
  }): Promise<ApiResponse<void>> {
    return httpClient.post(`${this.BASE_URL}/${id}/collaborators`, data)
  }

  /**
   * 移除协作者
   */
  static async removeCollaborator(id: string, userId: string): Promise<ApiResponse<void>> {
    return httpClient.delete(`${this.BASE_URL}/${id}/collaborators/${userId}`)
  }

  /**
   * 更新协作者权限
   */
  static async updateCollaboratorRole(id: string, userId: string, role: 'editor' | 'viewer'): Promise<ApiResponse<void>> {
    return httpClient.put(`${this.BASE_URL}/${id}/collaborators/${userId}`, { role })
  }

  /**
   * 获取版本历史
   */
  static async getVersionHistory(id: string): Promise<ApiResponse<Array<{
    version: number
    createdAt: string
    author: string
    description?: string
    changes: {
      nodesAdded: number
      nodesModified: number
      nodesDeleted: number
      edgesAdded: number
      edgesModified: number
      edgesDeleted: number
    }
  }>>> {
    return httpClient.get(`${this.BASE_URL}/${id}/versions`)
  }

  /**
   * 恢复到指定版本
   */
  static async restoreVersion(id: string, version: number): Promise<ApiResponse<MindMap>> {
    return httpClient.post(`${this.BASE_URL}/${id}/versions/${version}/restore`)
  }

  /**
   * 搜索思维导图
   */
  static async searchMindMaps(query: string, params?: {
    limit?: number
    includeContent?: boolean
    includePublic?: boolean
  }): Promise<ApiResponse<MindMap[]>> {
    return httpClient.get(`${this.BASE_URL}/search`, {
      params: { q: query, ...params }
    })
  }

  /**
   * 获取思维导图统计
   */
  static async getMindMapStats(): Promise<ApiResponse<{
    total: number
    public: number
    private: number
    shared: number
    totalNodes: number
    totalEdges: number
    averageNodes: number
    mostUsedTags: Array<{
      name: string
      count: number
    }>
  }>> {
    return httpClient.get(`${this.BASE_URL}/stats`)
  }

  /**
   * 获取最近访问的思维导图
   */
  static async getRecentMindMaps(limit: number = 10): Promise<ApiResponse<MindMap[]>> {
    return httpClient.get(`${this.BASE_URL}/recent`, { params: { limit } })
  }

  /**
   * 获取收藏的思维导图
   */
  static async getFavoriteMindMaps(): Promise<ApiResponse<MindMap[]>> {
    return httpClient.get(`${this.BASE_URL}/favorites`)
  }

  /**
   * 添加到收藏
   */
  static async addToFavorites(id: string): Promise<ApiResponse<void>> {
    return httpClient.post(`${this.BASE_URL}/${id}/favorite`)
  }

  /**
   * 从收藏中移除
   */
  static async removeFromFavorites(id: string): Promise<ApiResponse<void>> {
    return httpClient.delete(`${this.BASE_URL}/${id}/favorite`)
  }
}

// 导出默认实例
export default MindMapService
