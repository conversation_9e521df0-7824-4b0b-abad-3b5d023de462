import { httpClient } from '@/utils/http'
import type { 
  LearningTask, 
  CreateTaskForm, 
  UpdateTaskForm, 
  TaskQueryParams,
  ApiResponse,
  PaginationInfo
} from '@/types'

/**
 * 任务管理API服务
 */
export class TaskService {
  private static readonly BASE_URL = '/tasks'

  /**
   * 获取任务列表
   */
  static async getTasks(params?: TaskQueryParams): Promise<ApiResponse<{
    tasks: LearningTask[]
    pagination: PaginationInfo
  }>> {
    return httpClient.get(this.BASE_URL, { params })
  }

  /**
   * 根据ID获取任务详情
   */
  static async getTaskById(id: string): Promise<ApiResponse<LearningTask>> {
    return httpClient.get(`${this.BASE_URL}/${id}`)
  }

  /**
   * 创建新任务
   */
  static async createTask(data: CreateTaskForm): Promise<ApiResponse<LearningTask>> {
    return httpClient.post(this.BASE_URL, data)
  }

  /**
   * 更新任务
   */
  static async updateTask(data: UpdateTaskForm): Promise<ApiResponse<LearningTask>> {
    const { id, ...updateData } = data
    return httpClient.put(`${this.BASE_URL}/${id}`, updateData)
  }

  /**
   * 删除任务
   */
  static async deleteTask(id: string): Promise<ApiResponse<void>> {
    return httpClient.delete(`${this.BASE_URL}/${id}`)
  }

  /**
   * 批量删除任务
   */
  static async batchDeleteTasks(ids: string[]): Promise<ApiResponse<void>> {
    return httpClient.post(`${this.BASE_URL}/batch-delete`, { ids })
  }

  /**
   * 更新任务状态
   */
  static async updateTaskStatus(id: string, status: string): Promise<ApiResponse<LearningTask>> {
    return httpClient.patch(`${this.BASE_URL}/${id}/status`, { status })
  }

  /**
   * 开始任务
   */
  static async startTask(id: string): Promise<ApiResponse<LearningTask>> {
    return httpClient.post(`${this.BASE_URL}/${id}/start`)
  }

  /**
   * 暂停任务
   */
  static async pauseTask(id: string): Promise<ApiResponse<LearningTask>> {
    return httpClient.post(`${this.BASE_URL}/${id}/pause`)
  }

  /**
   * 完成任务
   */
  static async completeTask(id: string, data?: {
    notes?: string
    timeSpent?: number
    performance?: string
  }): Promise<ApiResponse<LearningTask>> {
    return httpClient.post(`${this.BASE_URL}/${id}/complete`, data)
  }

  /**
   * 取消任务
   */
  static async cancelTask(id: string, reason?: string): Promise<ApiResponse<LearningTask>> {
    return httpClient.post(`${this.BASE_URL}/${id}/cancel`, { reason })
  }

  /**
   * 复制任务
   */
  static async duplicateTask(id: string): Promise<ApiResponse<LearningTask>> {
    return httpClient.post(`${this.BASE_URL}/${id}/duplicate`)
  }

  /**
   * 获取任务统计信息
   */
  static async getTaskStats(params?: {
    startDate?: string
    endDate?: string
    groupBy?: 'day' | 'week' | 'month'
  }): Promise<ApiResponse<{
    total: number
    completed: number
    inProgress: number
    pending: number
    cancelled: number
    completionRate: number
    averageTimeSpent: number
    trends: Array<{
      date: string
      count: number
      completed: number
    }>
  }>> {
    return httpClient.get(`${this.BASE_URL}/stats`, { params })
  }

  /**
   * 搜索任务
   */
  static async searchTasks(query: string, params?: {
    limit?: number
    includeContent?: boolean
    includeAttachments?: boolean
  }): Promise<ApiResponse<LearningTask[]>> {
    return httpClient.get(`${this.BASE_URL}/search`, {
      params: { q: query, ...params }
    })
  }

  /**
   * 获取推荐任务
   */
  static async getRecommendedTasks(params?: {
    limit?: number
    basedOn?: 'difficulty' | 'type' | 'tags' | 'history'
  }): Promise<ApiResponse<LearningTask[]>> {
    return httpClient.get(`${this.BASE_URL}/recommendations`, { params })
  }

  /**
   * 获取任务依赖关系
   */
  static async getTaskDependencies(id: string): Promise<ApiResponse<{
    prerequisites: LearningTask[]
    dependents: LearningTask[]
  }>> {
    return httpClient.get(`${this.BASE_URL}/${id}/dependencies`)
  }

  /**
   * 设置任务依赖
   */
  static async setTaskDependencies(id: string, data: {
    prerequisites?: string[]
    dependents?: string[]
  }): Promise<ApiResponse<void>> {
    return httpClient.put(`${this.BASE_URL}/${id}/dependencies`, data)
  }

  /**
   * 导出任务
   */
  static async exportTasks(params?: {
    ids?: string[]
    format?: 'json' | 'csv' | 'xlsx'
    includeAttachments?: boolean
  }): Promise<ApiResponse<{
    downloadUrl: string
    filename: string
  }>> {
    return httpClient.post(`${this.BASE_URL}/export`, params)
  }

  /**
   * 导入任务
   */
  static async importTasks(file: File, _options?: {
    skipDuplicates?: boolean
    updateExisting?: boolean
  }): Promise<ApiResponse<{
    imported: number
    skipped: number
    errors: Array<{
      row: number
      message: string
    }>
  }>> {
    return httpClient.upload(`${this.BASE_URL}/import`, file)
  }

  /**
   * 获取任务模板
   */
  static async getTaskTemplates(): Promise<ApiResponse<Array<{
    id: string
    name: string
    description: string
    template: Partial<CreateTaskForm>
  }>>> {
    return httpClient.get(`${this.BASE_URL}/templates`)
  }

  /**
   * 从模板创建任务
   */
  static async createTaskFromTemplate(templateId: string, data?: Partial<CreateTaskForm>): Promise<ApiResponse<LearningTask>> {
    return httpClient.post(`${this.BASE_URL}/templates/${templateId}/create`, data)
  }

  /**
   * 获取任务标签
   */
  static async getTaskTags(): Promise<ApiResponse<Array<{
    name: string
    count: number
    color?: string
  }>>> {
    return httpClient.get(`${this.BASE_URL}/tags`)
  }

  /**
   * 获取任务分类
   */
  static async getTaskCategories(): Promise<ApiResponse<Array<{
    id: string
    name: string
    description?: string
    taskCount: number
    parentId?: string
    children?: any[]
  }>>> {
    return httpClient.get(`${this.BASE_URL}/categories`)
  }

  /**
   * 获取用户的最近任务
   */
  static async getRecentTasks(limit: number = 10): Promise<ApiResponse<LearningTask[]>> {
    return httpClient.get(`${this.BASE_URL}/recent`, { params: { limit } })
  }

  /**
   * 获取今日任务
   */
  static async getTodayTasks(): Promise<ApiResponse<{
    scheduled: LearningTask[]
    overdue: LearningTask[]
    completed: LearningTask[]
  }>> {
    return httpClient.get(`${this.BASE_URL}/today`)
  }

  /**
   * 获取本周任务概览
   */
  static async getWeeklyOverview(): Promise<ApiResponse<{
    totalTasks: number
    completedTasks: number
    timeSpent: number
    dailyBreakdown: Array<{
      date: string
      tasks: number
      completed: number
      timeSpent: number
    }>
  }>> {
    return httpClient.get(`${this.BASE_URL}/weekly-overview`)
  }
}

// 导出默认实例
export default TaskService
