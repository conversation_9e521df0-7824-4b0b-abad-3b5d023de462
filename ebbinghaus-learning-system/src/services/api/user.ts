import { httpClient } from '@/utils/http'
import type { 
  User, 
  UserPreferences,
  ApiResponse
} from '@/types'

/**
 * 用户管理API服务
 */
export class UserService {
  private static readonly BASE_URL = '/users'

  /**
   * 用户登录
   */
  static async login(data: {
    email: string
    password: string
    rememberMe?: boolean
  }): Promise<ApiResponse<{
    user: User
    tokens: {
      accessToken: string
      refreshToken: string
    }
  }>> {
    // 后端期望的是identifier字段，而不是email
    const loginData = {
      identifier: data.email,
      password: data.password,
      rememberMe: data.rememberMe
    }
    return httpClient.post('/auth/login', loginData)
  }

  /**
   * 用户注册
   */
  static async register(data: {
    username: string
    email: string
    password: string
    confirmPassword: string
    inviteCode?: string
  }): Promise<ApiResponse<{
    user: User
    tokens: {
      accessToken: string
      refreshToken: string
    }
  }>> {
    return httpClient.post('/auth/register', data)
  }

  /**
   * 刷新访问令牌
   */
  static async refreshToken(refreshToken: string): Promise<ApiResponse<{
    tokens: {
      accessToken: string
      refreshToken: string
    }
  }>> {
    return httpClient.post('/auth/refresh', { refreshToken })
  }

  /**
   * 用户登出
   */
  static async logout(): Promise<ApiResponse<void>> {
    return httpClient.post('/auth/logout')
  }

  /**
   * 发送验证邮件
   */
  static async sendVerificationEmail(email: string): Promise<ApiResponse<void>> {
    return httpClient.post('/auth/send-verification', { email })
  }

  /**
   * 验证邮箱
   */
  static async verifyEmail(token: string): Promise<ApiResponse<void>> {
    return httpClient.post('/auth/verify-email', { token })
  }

  /**
   * 发送密码重置邮件
   */
  static async sendPasswordResetEmail(email: string): Promise<ApiResponse<void>> {
    return httpClient.post('/auth/forgot-password', { email })
  }

  /**
   * 重置密码
   */
  static async resetPassword(data: {
    token: string
    password: string
    confirmPassword: string
  }): Promise<ApiResponse<void>> {
    return httpClient.post('/auth/reset-password', data)
  }

  /**
   * 获取当前用户信息
   */
  static async getCurrentUser(): Promise<ApiResponse<User>> {
    return httpClient.get(`${this.BASE_URL}/me`)
  }

  /**
   * 更新用户信息
   */
  static async updateProfile(data: {
    username?: string
    email?: string
    avatar?: string
  }): Promise<ApiResponse<User>> {
    return httpClient.put(`${this.BASE_URL}/me`, data)
  }

  /**
   * 修改密码
   */
  static async changePassword(data: {
    currentPassword: string
    newPassword: string
    confirmPassword: string
  }): Promise<ApiResponse<void>> {
    return httpClient.put(`${this.BASE_URL}/me/password`, data)
  }

  /**
   * 上传头像
   */
  static async uploadAvatar(file: File): Promise<ApiResponse<{
    avatar: string
  }>> {
    return httpClient.upload(`${this.BASE_URL}/me/avatar`, file)
  }

  /**
   * 获取用户偏好设置
   */
  static async getPreferences(): Promise<ApiResponse<UserPreferences>> {
    return httpClient.get(`${this.BASE_URL}/me/preferences`)
  }

  /**
   * 更新用户偏好设置
   */
  static async updatePreferences(preferences: Partial<UserPreferences>): Promise<ApiResponse<UserPreferences>> {
    return httpClient.put(`${this.BASE_URL}/me/preferences`, preferences)
  }

  /**
   * 获取用户统计信息
   */
  static async getUserStats(): Promise<ApiResponse<{
    totalTasks: number
    completedTasks: number
    totalStudyTime: number
    streakDays: number
    totalReviews: number
    averagePerformance: number
    joinedDays: number
    achievements: Array<{
      id: string
      name: string
      description: string
      unlockedAt: string
      icon: string
    }>
  }>> {
    return httpClient.get(`${this.BASE_URL}/me/stats`)
  }

  /**
   * 获取学习报告
   */
  static async getStudyReport(params?: {
    period?: 'week' | 'month' | 'year'
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<{
    period: string
    totalTime: number
    tasksCompleted: number
    reviewsCompleted: number
    averagePerformance: number
    dailyBreakdown: Array<{
      date: string
      studyTime: number
      tasksCompleted: number
      reviewsCompleted: number
    }>
    categoryBreakdown: Array<{
      category: string
      time: number
      percentage: number
    }>
    achievements: Array<{
      name: string
      description: string
      unlockedAt: string
    }>
  }>> {
    return httpClient.get(`${this.BASE_URL}/me/report`, { params })
  }

  /**
   * 获取学习目标
   */
  static async getStudyGoals(): Promise<ApiResponse<Array<{
    id: string
    type: 'daily' | 'weekly' | 'monthly'
    target: number
    current: number
    unit: 'minutes' | 'tasks' | 'reviews'
    deadline?: string
    isCompleted: boolean
  }>>> {
    return httpClient.get(`${this.BASE_URL}/me/goals`)
  }

  /**
   * 设置学习目标
   */
  static async setStudyGoal(data: {
    type: 'daily' | 'weekly' | 'monthly'
    target: number
    unit: 'minutes' | 'tasks' | 'reviews'
    deadline?: string
  }): Promise<ApiResponse<void>> {
    return httpClient.post(`${this.BASE_URL}/me/goals`, data)
  }

  /**
   * 更新学习目标
   */
  static async updateStudyGoal(goalId: string, data: {
    target?: number
    deadline?: string
  }): Promise<ApiResponse<void>> {
    return httpClient.put(`${this.BASE_URL}/me/goals/${goalId}`, data)
  }

  /**
   * 删除学习目标
   */
  static async deleteStudyGoal(goalId: string): Promise<ApiResponse<void>> {
    return httpClient.delete(`${this.BASE_URL}/me/goals/${goalId}`)
  }

  /**
   * 获取成就列表
   */
  static async getAchievements(): Promise<ApiResponse<{
    unlocked: Array<{
      id: string
      name: string
      description: string
      icon: string
      unlockedAt: string
      rarity: 'common' | 'rare' | 'epic' | 'legendary'
    }>
    locked: Array<{
      id: string
      name: string
      description: string
      icon: string
      progress: number
      target: number
      rarity: 'common' | 'rare' | 'epic' | 'legendary'
    }>
  }>> {
    return httpClient.get(`${this.BASE_URL}/me/achievements`)
  }

  /**
   * 获取活动日志
   */
  static async getActivityLog(params?: {
    page?: number
    limit?: number
    type?: 'task' | 'review' | 'mindmap' | 'achievement'
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<{
    activities: Array<{
      id: string
      type: string
      action: string
      description: string
      metadata?: any
      createdAt: string
    }>
    pagination: any
  }>> {
    return httpClient.get(`${this.BASE_URL}/me/activities`, { params })
  }

  /**
   * 导出用户数据
   */
  static async exportUserData(format: 'json' | 'csv' = 'json'): Promise<ApiResponse<{
    downloadUrl: string
    filename: string
  }>> {
    return httpClient.post(`${this.BASE_URL}/me/export`, { format })
  }

  /**
   * 删除用户账户
   */
  static async deleteAccount(data: {
    password: string
    reason?: string
  }): Promise<ApiResponse<void>> {
    return httpClient.delete(`${this.BASE_URL}/me`, { data })
  }

  /**
   * 获取账户安全设置
   */
  static async getSecuritySettings(): Promise<ApiResponse<{
    twoFactorEnabled: boolean
    lastPasswordChange: string
    activeSessions: Array<{
      id: string
      device: string
      location: string
      lastActive: string
      isCurrent: boolean
    }>
    loginHistory: Array<{
      ip: string
      device: string
      location: string
      timestamp: string
      success: boolean
    }>
  }>> {
    return httpClient.get(`${this.BASE_URL}/me/security`)
  }

  /**
   * 启用两步验证
   */
  static async enableTwoFactor(): Promise<ApiResponse<{
    qrCode: string
    secret: string
    backupCodes: string[]
  }>> {
    return httpClient.post(`${this.BASE_URL}/me/security/2fa/enable`)
  }

  /**
   * 确认两步验证
   */
  static async confirmTwoFactor(code: string): Promise<ApiResponse<void>> {
    return httpClient.post(`${this.BASE_URL}/me/security/2fa/confirm`, { code })
  }

  /**
   * 禁用两步验证
   */
  static async disableTwoFactor(password: string): Promise<ApiResponse<void>> {
    return httpClient.post(`${this.BASE_URL}/me/security/2fa/disable`, { password })
  }

  /**
   * 终止其他会话
   */
  static async terminateOtherSessions(): Promise<ApiResponse<void>> {
    return httpClient.post(`${this.BASE_URL}/me/security/terminate-sessions`)
  }
}

// 导出默认实例
export default UserService
