import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../views/HomeView.vue')
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue')
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/RegisterView.vue')
    },
    {
      path: '/test',
      name: 'test',
      component: () => import('../views/TestView.vue')
    },
    {
      path: '/api-test',
      name: 'api-test',
      component: () => import('../views/ApiTestView.vue')
    },
    {
      path: '/store-test',
      name: 'store-test',
      component: () => import('../views/StoreTestView.vue')
    },
    {
      path: '/component-test',
      name: 'component-test',
      component: () => import('../views/ComponentTestView.vue')
    },
    {
      path: '/dashboard',
      name: 'dashboard',
      component: () => import('../views/DashboardView.vue')
    },
    {
      path: '/tasks',
      name: 'tasks',
      component: () => import('../views/TasksView.vue')
    },
    {
      path: '/tasks/create',
      name: 'task-create',
      component: () => import('../views/TaskCreateView.vue')
    },
    {
      path: '/tasks/:id',
      name: 'task-detail',
      component: () => import('../views/TaskDetailView.vue')
    },
    {
      path: '/reviews',
      name: 'reviews',
      component: () => import('../views/ReviewsView.vue')
    },
    {
      path: '/reviews/session',
      name: 'review-session',
      component: () => import('../views/ReviewSessionView.vue')
    },
    {
      path: '/mindmaps',
      name: 'mindmaps',
      component: () => import('../views/MindMapsView.vue')
    },
    {
      path: '/mindmaps/:id',
      name: 'mindmap-detail',
      component: () => import('../views/MindMapDetailView.vue')
    }
  ]
})

export default router
