// 基础类型定义
export interface BaseEntity {
  id: string
  createdAt: string
  updatedAt: string
}

// 用户相关类型
export interface User extends BaseEntity {
  username: string
  email: string
  avatar?: string
  preferences: UserPreferences
}

export interface UserPreferences {
  theme: 'light' | 'dark'
  language: 'zh-CN' | 'en-US'
  notifications: NotificationSettings
  studySettings: StudySettings
}

export interface NotificationSettings {
  reviewReminders: boolean
  taskDeadlines: boolean
  achievementAlerts: boolean
  emailNotifications: boolean
}

export interface StudySettings {
  dailyStudyGoal: number // 分钟
  preferredStudyTime: string // HH:mm 格式
  difficultyPreference: 'easy' | 'medium' | 'hard'
  autoAdvance: boolean
}

// 学习任务相关类型
export interface LearningTask extends BaseEntity {
  title: string
  description: string
  content: string
  type: TaskType
  difficulty: DifficultyLevel
  estimatedDuration: number // 分钟
  tags: string[]
  status: TaskStatus
  priority: Priority
  dueDate?: string
  completedAt?: string
  userId: string
  categoryId?: string
  attachments: Attachment[]
  metadata: TaskMetadata
}

export type TaskType = 'math' | 'physics' | 'chemistry' | 'biology' | 'history' | 'geography' | 'chinese' | 'english' | 'politics'
export type DifficultyLevel = 1 | 2 | 3 | 4 | 5
export type TaskStatus = 'pending' | 'in_progress' | 'completed' | 'cancelled'
export type Priority = 1 | 2 | 3 | 4 | 5

export interface TaskMetadata {
  source?: string
  references?: string[]
  prerequisites?: string[]
  learningObjectives?: string[]
  assessmentCriteria?: string[]
}

export interface Attachment {
  id: string
  name: string
  type: 'image' | 'document' | 'video' | 'audio' | 'link'
  url: string
  size?: number
}

// 艾宾浩斯复习相关类型
export interface ReviewSchedule extends BaseEntity {
  taskId: string
  userId: string
  intervals: ReviewInterval[]
  currentStage: number
  nextReviewDate: string
  isCompleted: boolean
  performance: ReviewPerformance[]
}

export interface ReviewInterval {
  stage: number
  interval: number // 天数
  description: string
  isCompleted: boolean
  completedAt?: string
  performance?: PerformanceLevel
}

export interface ReviewPerformance {
  stage: number
  date: string
  performance: PerformanceLevel
  timeSpent: number // 分钟
  notes?: string
}

export type PerformanceLevel = 'poor' | 'fair' | 'good' | 'excellent'

// 复习会话类型
export interface ReviewSession extends BaseEntity {
  userId: string
  taskIds: string[]
  startTime: string
  endTime?: string
  totalTimeSpent: number
  tasksCompleted: number
  averagePerformance: PerformanceLevel
  notes?: string
}

// 思维导图相关类型
export interface MindMap extends BaseEntity {
  title: string
  description?: string
  userId: string
  isPublic: boolean
  tags: string[]
  nodes: MindMapNode[]
  edges: MindMapEdge[]
  layout: MindMapLayout
  style: MindMapStyle
}

export interface MindMapNode {
  id: string
  title: string
  content?: string
  type: NodeType
  position: Position
  size?: Size
  style?: NodeStyle
  tags?: string[]
  metadata?: Record<string, any>
  parentId?: string
  children?: string[]
}

export interface MindMapEdge {
  id: string
  sourceId: string
  targetId: string
  type: EdgeType
  style?: EdgeStyle
  label?: string
}

export type NodeType = 'root' | 'branch' | 'leaf' | 'note' | 'image' | 'link'
export type EdgeType = 'straight' | 'curved' | 'orthogonal' | 'solid' | 'dashed' | 'dotted'

export interface Position {
  x: number
  y: number
}

export interface Size {
  width: number
  height: number
}

export interface NodeStyle {
  backgroundColor?: string
  borderColor?: string
  borderWidth?: string | number
  color?: string
  fontSize?: string | number
  fontWeight?: 'normal' | 'bold' | string
  borderRadius?: string | number
}

export interface EdgeStyle {
  color?: string
  width?: number
  dashArray?: string
}

export interface NodeData {
  content: string
  attachments?: Attachment[]
  links?: string[]
  notes?: string
  taskId?: string
}

export interface MindMapLayout {
  type: 'hierarchical' | 'radial' | 'force' | 'circular'
  direction: 'top-bottom' | 'bottom-top' | 'left-right' | 'right-left'
  spacing: {
    node: number
    rank: number
  }
}

export interface MindMapStyle {
  theme: 'default' | 'dark' | 'colorful' | 'minimal'
  backgroundColor: string
  gridVisible: boolean
  snapToGrid: boolean
}

// 分析和统计相关类型
export interface StudyAnalytics {
  userId: string
  period: AnalyticsPeriod
  totalStudyTime: number // 分钟
  tasksCompleted: number
  averagePerformance: PerformanceLevel
  streakDays: number
  categoryBreakdown: CategoryStats[]
  performanceTrend: PerformanceTrend[]
  reviewEfficiency: ReviewEfficiency
}

export type AnalyticsPeriod = 'daily' | 'weekly' | 'monthly' | 'yearly'

export interface CategoryStats {
  categoryId: string
  categoryName: string
  timeSpent: number
  tasksCompleted: number
  averagePerformance: PerformanceLevel
}

export interface PerformanceTrend {
  date: string
  performance: PerformanceLevel
  timeSpent: number
  tasksCompleted: number
}

export interface ReviewEfficiency {
  totalReviews: number
  onTimeReviews: number
  overdueReviews: number
  averageRetentionRate: number
  improvementRate: number
}

// 通知相关类型
export interface Notification extends BaseEntity {
  userId: string
  type: NotificationType
  title: string
  message: string
  isRead: boolean
  priority: Priority
  actionUrl?: string
  metadata?: Record<string, any>
}

export type NotificationType = 'review_reminder' | 'task_deadline' | 'achievement' | 'system' | 'social'

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  errors?: string[]
  pagination?: PaginationInfo
}

export interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

// 请求参数类型
export interface PaginationParams {
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface TaskQueryParams extends PaginationParams {
  status?: TaskStatus
  type?: TaskType
  difficulty?: DifficultyLevel
  priority?: Priority
  tags?: string[]
  search?: string
  categoryId?: string
  dueDate?: string
}

export interface ReviewQueryParams extends PaginationParams {
  status?: 'pending' | 'completed' | 'overdue'
  performance?: PerformanceLevel
  dateRange?: {
    start: string
    end: string
  }
}

// 表单数据类型
export interface CreateTaskForm {
  title: string
  description: string
  content: string
  type: TaskType
  difficulty: DifficultyLevel
  estimatedDuration: number
  tags: string[]
  priority: Priority
  dueDate?: string
  categoryId?: string
  attachments?: File[]
}

export interface UpdateTaskForm extends Partial<CreateTaskForm> {
  id: string
  status?: TaskStatus
}

export interface CreateMindMapForm {
  title: string
  description?: string
  isPublic: boolean
  tags: string[]
}

export interface UpdateMindMapForm extends Partial<CreateMindMapForm> {
  id: string
}

// 组件Props类型
export interface TaskCardProps {
  task: LearningTask
  showActions?: boolean
  compact?: boolean
  onEdit?: (task: LearningTask) => void
  onDelete?: (taskId: string) => void
  onStatusChange?: (taskId: string, status: TaskStatus) => void
}

export interface ReviewCardProps {
  schedule: ReviewSchedule
  task: LearningTask
  onComplete?: (scheduleId: string, performance: PerformanceLevel) => void
  onSkip?: (scheduleId: string) => void
}

export interface MindMapProps {
  mindMap: MindMap
  readonly?: boolean
  onNodeClick?: (node: MindMapNode) => void
  onEdgeClick?: (edge: MindMapEdge) => void
  onSave?: (mindMap: MindMap) => void
}

// 状态管理类型
export interface AppState {
  user: User | null
  isAuthenticated: boolean
  loading: boolean
  error: string | null
}

export interface TaskState {
  tasks: LearningTask[]
  currentTask: LearningTask | null
  loading: boolean
  error: string | null
  pagination: PaginationInfo | null
}

export interface ReviewState {
  schedules: ReviewSchedule[]
  currentSession: ReviewSession | null
  todayReviews: ReviewSchedule[]
  loading: boolean
  error: string | null
}

export interface MindMapState {
  mindMaps: MindMap[]
  currentMindMap: MindMap | null
  loading: boolean
  error: string | null
}
