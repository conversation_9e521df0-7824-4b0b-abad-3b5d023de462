// 应用常量
export const APP_CONFIG = {
  NAME: '艾宾浩斯记忆曲线学习管理系统',
  VERSION: '1.0.0',
  DESCRIPTION: '基于艾宾浩斯记忆曲线的智能学习管理系统',
  AUTHOR: 'JYZS Team',
  COPYRIGHT: '© 2024 JYZS. All rights reserved.',
}

// API相关常量
export const API_CONFIG = {
  BASE_URL: '/api',
  TIMEOUT: 10000,
  RETRY_TIMES: 3,
  RETRY_DELAY: 1000,
}

// 分页配置
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE: 1,
  DEFAULT_PAGE_SIZE: 20,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
  MAX_PAGE_SIZE: 100,
}

// 文件上传配置
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_DOCUMENT_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain',
  ],
  ALLOWED_VIDEO_TYPES: ['video/mp4', 'video/webm', 'video/ogg'],
  ALLOWED_AUDIO_TYPES: ['audio/mp3', 'audio/wav', 'audio/ogg'],
}

// 艾宾浩斯记忆曲线配置
export const EBBINGHAUS_CONFIG = {
  // 标准复习间隔（天）
  STANDARD_INTERVALS: [1, 2, 4, 7, 15, 30, 60, 120],
  
  // 表现调整系数
  PERFORMANCE_MULTIPLIERS: {
    poor: 0.5,
    fair: 0.8,
    good: 1.2,
    excellent: 1.5,
  },
  
  // 最小和最大间隔限制
  MIN_INTERVAL: 1,
  MAX_INTERVAL: 365,
  
  // 记忆保持率
  RETENTION_RATES: {
    poor: 0.2,
    fair: 0.5,
    good: 0.8,
    excellent: 0.95,
  },
}

// 任务相关常量
export const TASK_CONFIG = {
  // 默认预估时长（分钟）
  DEFAULT_DURATION: 30,

  // 最大预估时长（分钟）
  MAX_DURATION: 480, // 8小时

  // 任务类型配置 - 匹配后端学科枚举
  TYPES: {
    'math': { label: '数学', color: '#409EFF', icon: 'Edit' },
    'physics': { label: '物理', color: '#67C23A', icon: 'View' },
    'chemistry': { label: '化学', color: '#E6A23C', icon: 'View' },
    'biology': { label: '生物', color: '#95D475', icon: 'Memo' },
    'history': { label: '历史', color: '#F56C6C', icon: 'Memo' },
    'geography': { label: '地理', color: '#909399', icon: 'Memo' },
    'chinese': { label: '语文', color: '#FF7875', icon: 'Reading' },
    'english': { label: '英语', color: '#40A9FF', icon: 'Reading' },
    'politics': { label: '政治', color: '#52C41A', icon: 'Setting' },
  },

  // 难度等级配置 - 匹配后端数字格式
  DIFFICULTIES: {
    1: { label: '很简单', color: '#67C23A', level: 1 },
    2: { label: '简单', color: '#95D475', level: 2 },
    3: { label: '中等', color: '#409EFF', level: 3 },
    4: { label: '困难', color: '#E6A23C', level: 4 },
    5: { label: '很困难', color: '#F56C6C', level: 5 },
  },
  
  // 优先级配置 - 匹配后端数字格式
  PRIORITIES: {
    1: { label: '很低', color: '#C0C4CC', level: 1 },
    2: { label: '低', color: '#909399', level: 2 },
    3: { label: '中', color: '#409EFF', level: 3 },
    4: { label: '高', color: '#E6A23C', level: 4 },
    5: { label: '很高', color: '#F56C6C', level: 5 },
  },
  
  // 状态配置 - 匹配后端格式
  STATUSES: {
    pending: { label: '待开始', color: '#909399', icon: 'Clock' },
    in_progress: { label: '进行中', color: '#409EFF', icon: 'VideoPlay' },
    completed: { label: '已完成', color: '#67C23A', icon: 'Check' },
    cancelled: { label: '已取消', color: '#F56C6C', icon: 'Close' },
  },
}

// 复习相关常量
export const REVIEW_CONFIG = {
  // 表现等级配置
  PERFORMANCE_LEVELS: {
    poor: { label: '差', color: '#F56C6C', score: 1 },
    fair: { label: '一般', color: '#E6A23C', score: 2 },
    good: { label: '良好', color: '#409EFF', score: 3 },
    excellent: { label: '优秀', color: '#67C23A', score: 4 },
  },
  
  // 复习提醒时间
  REMINDER_TIMES: ['09:00', '14:00', '19:00'],
  
  // 每日最大复习任务数
  MAX_DAILY_REVIEWS: 50,
  
  // 复习会话最大时长（分钟）
  MAX_SESSION_DURATION: 120,
}

// 思维导图相关常量
export const MINDMAP_CONFIG = {
  // 节点类型配置
  NODE_TYPES: {
    root: { label: '根节点', color: '#409EFF', shape: 'ellipse' },
    branch: { label: '分支节点', color: '#67C23A', shape: 'rectangle' },
    leaf: { label: '叶子节点', color: '#E6A23C', shape: 'round-rectangle' },
    note: { label: '注释节点', color: '#909399', shape: 'diamond' },
    image: { label: '图片节点', color: '#F56C6C', shape: 'hexagon' },
    link: { label: '链接节点', color: '#8E44AD', shape: 'triangle' },
  },
  
  // 边类型配置
  EDGE_TYPES: {
    straight: { label: '直线', style: 'solid' },
    curved: { label: '曲线', style: 'solid' },
    bezier: { label: '贝塞尔曲线', style: 'solid' },
    step: { label: '阶梯线', style: 'solid' },
  },
  
  // 布局类型配置
  LAYOUT_TYPES: {
    hierarchical: { label: '层次布局', direction: 'top-bottom' },
    radial: { label: '径向布局', direction: 'center-out' },
    force: { label: '力导向布局', direction: 'none' },
    circular: { label: '环形布局', direction: 'clockwise' },
  },
  
  // 主题配置
  THEMES: {
    default: { label: '默认主题', backgroundColor: '#ffffff' },
    dark: { label: '暗色主题', backgroundColor: '#1a1a1a' },
    colorful: { label: '彩色主题', backgroundColor: '#f0f8ff' },
    minimal: { label: '简约主题', backgroundColor: '#fafafa' },
  },
  
  // 画布配置
  CANVAS_CONFIG: {
    DEFAULT_WIDTH: 1200,
    DEFAULT_HEIGHT: 800,
    MIN_ZOOM: 0.1,
    MAX_ZOOM: 3.0,
    ZOOM_STEP: 0.1,
  },
}

// 通知相关常量
export const NOTIFICATION_CONFIG = {
  // 通知类型配置
  TYPES: {
    review_reminder: { label: '复习提醒', color: '#409EFF', icon: 'Bell' },
    task_deadline: { label: '任务截止', color: '#E6A23C', icon: 'Warning' },
    achievement: { label: '成就解锁', color: '#67C23A', icon: 'Trophy' },
    system: { label: '系统通知', color: '#909399', icon: 'Message' },
    social: { label: '社交消息', color: '#F56C6C', icon: 'ChatDotRound' },
  },
  
  // 显示时长（毫秒）
  DISPLAY_DURATION: {
    success: 3000,
    warning: 4000,
    error: 5000,
    info: 3000,
  },
  
  // 最大显示数量
  MAX_DISPLAY_COUNT: 5,
}

// 分析统计相关常量
export const ANALYTICS_CONFIG = {
  // 时间周期配置
  PERIODS: {
    daily: { label: '日', days: 1 },
    weekly: { label: '周', days: 7 },
    monthly: { label: '月', days: 30 },
    yearly: { label: '年', days: 365 },
  },
  
  // 图表颜色配置
  CHART_COLORS: [
    '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399',
    '#8E44AD', '#3498DB', '#2ECC71', '#F39C12', '#E74C3C',
  ],
  
  // 统计指标配置
  METRICS: {
    study_time: { label: '学习时长', unit: '分钟', format: 'duration' },
    tasks_completed: { label: '完成任务', unit: '个', format: 'number' },
    review_accuracy: { label: '复习准确率', unit: '%', format: 'percentage' },
    streak_days: { label: '连续天数', unit: '天', format: 'number' },
  },
}

// 用户偏好默认值
export const USER_PREFERENCES_DEFAULTS = {
  theme: 'light',
  language: 'zh-CN',
  notifications: {
    reviewReminders: true,
    taskDeadlines: true,
    achievementAlerts: true,
    emailNotifications: false,
  },
  studySettings: {
    dailyStudyGoal: 60, // 分钟
    preferredStudyTime: '19:00',
    difficultyPreference: 'medium',
    autoAdvance: true,
  },
}

// 本地存储键名
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_INFO: 'user_info',
  USER_PREFERENCES: 'user_preferences',
  THEME: 'theme',
  LANGUAGE: 'language',
  RECENT_TASKS: 'recent_tasks',
  DRAFT_CONTENT: 'draft_content',
}

// 路由路径常量
export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  REGISTER: '/register',
  DASHBOARD: '/dashboard',
  TASKS: '/tasks',
  TASK_DETAIL: '/tasks/:id',
  REVIEWS: '/reviews',
  MINDMAPS: '/mindmaps',
  MINDMAP_DETAIL: '/mindmaps/:id',
  ANALYTICS: '/analytics',
  PROFILE: '/profile',
  SETTINGS: '/settings',
  ABOUT: '/about',
}

// 正则表达式常量
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^1[3-9]\d{9}$/,
  USERNAME: /^[a-zA-Z0-9_]{3,20}$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  HEX_COLOR: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
}

// 错误消息常量
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  TIMEOUT_ERROR: '请求超时，请稍后再试',
  SERVER_ERROR: '服务器内部错误',
  UNAUTHORIZED: '登录已过期，请重新登录',
  FORBIDDEN: '没有权限访问该资源',
  NOT_FOUND: '请求的资源不存在',
  VALIDATION_ERROR: '请求参数错误',
  RATE_LIMIT: '请求过于频繁，请稍后再试',
  FILE_TOO_LARGE: '文件大小超出限制',
  INVALID_FILE_TYPE: '不支持的文件类型',
}
