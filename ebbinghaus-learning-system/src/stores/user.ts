import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { UserService } from '@/services/api'
import { useAppStore } from './app'
import type { User, UserPreferences } from '@/types'

/**
 * 用户状态管理
 */
export const useUserStore = defineStore('user', () => {
  const appStore = useAppStore()

  // 状态
  const user = ref<User | null>(null)
  const preferences = ref<UserPreferences | null>(null)
  const isAuthenticated = ref(false)
  const loading = ref(false)
  const loginLoading = ref(false)

  // 计算属性
  const userInfo = computed(() => user.value)
  const userName = computed(() => user.value?.username || '')
  const userEmail = computed(() => user.value?.email || '')
  const userAvatar = computed(() => user.value?.avatar || '')
  const isLoggedIn = computed(() => isAuthenticated.value && !!user.value)
  const userPreferences = computed(() => preferences.value)

  // 操作方法
  const setUser = (userData: User | null) => {
    user.value = userData
    isAuthenticated.value = !!userData
    
    if (userData) {
      // 保存用户信息到本地存储
      localStorage.setItem('user_info', JSON.stringify(userData))
    } else {
      // 清除本地存储
      localStorage.removeItem('user_info')
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
    }
  }

  const setPreferences = (prefs: UserPreferences) => {
    preferences.value = prefs
    // 保存到本地存储
    localStorage.setItem('user_preferences', JSON.stringify(prefs))
    
    // 同步到应用状态
    if (prefs.theme) {
      appStore.setTheme(prefs.theme)
    }
    if (prefs.language) {
      appStore.setLanguage(prefs.language)
    }
  }

  const setLoading = (state: boolean) => {
    loading.value = state
  }

  const setLoginLoading = (state: boolean) => {
    loginLoading.value = state
  }

  // 登录
  const login = async (credentials: {
    email: string
    password: string
    rememberMe?: boolean
  }) => {
    try {
      setLoginLoading(true)
      appStore.clearError()

      const response = await UserService.login(credentials)

      if (response.success && response.data) {
        const { user: userData, tokens } = response.data
        const { accessToken, refreshToken } = tokens

        // 保存令牌
        localStorage.setItem('access_token', accessToken)
        localStorage.setItem('refresh_token', refreshToken)

        // 设置用户信息
        setUser(userData)

        // 获取用户偏好（可选，失败不影响登录）
        try {
          await fetchPreferences()
        } catch (error) {
          console.warn('获取用户偏好失败，但不影响登录:', error)
        }

        appStore.showSuccess('登录成功', `欢迎回来，${userData.username}！`)
        return true
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '登录失败'
      appStore.setError(message)
      appStore.showError('登录失败', message)
      return false
    } finally {
      setLoginLoading(false)
    }
  }

  // 注册
  const register = async (userData: {
    username: string
    email: string
    password: string
    confirmPassword: string
    inviteCode?: string
  }) => {
    try {
      setLoading(true)
      appStore.clearError()

      const response = await UserService.register(userData)

      if (response.success && response.data) {
        const { user: newUser, tokens } = response.data
        const { accessToken, refreshToken } = tokens

        // 保存令牌
        localStorage.setItem('access_token', accessToken)
        localStorage.setItem('refresh_token', refreshToken)

        // 设置用户信息
        setUser(newUser)

        // 获取用户偏好（可选，失败不影响注册）
        try {
          await fetchPreferences()
        } catch (error) {
          console.warn('获取用户偏好失败，但不影响注册:', error)
        }

        appStore.showSuccess('注册成功', `欢迎加入，${newUser.username}！`)
        return true
      } else {
        throw new Error(response.message || '注册失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '注册失败'
      appStore.setError(message)
      appStore.showError('注册失败', message)
      return false
    } finally {
      setLoading(false)
    }
  }

  // 登出
  const logout = async () => {
    try {
      setLoading(true)
      
      // 调用登出API
      await UserService.logout()
      
      // 清除状态
      setUser(null)
      preferences.value = null
      
      appStore.showInfo('已登出', '您已成功登出')
    } catch (error) {
      // 即使API调用失败，也要清除本地状态
      setUser(null)
      preferences.value = null
      
      console.error('登出API调用失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 获取当前用户信息
  const fetchCurrentUser = async () => {
    try {
      setLoading(true)
      
      const response = await UserService.getCurrentUser()
      
      if (response.success && response.data) {
        setUser(response.data)
        return response.data
      } else {
        throw new Error(response.message || '获取用户信息失败')
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取失败，可能是token过期，清除登录状态
      setUser(null)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 更新用户资料
  const updateProfile = async (profileData: {
    username?: string
    email?: string
    avatar?: string
  }) => {
    try {
      setLoading(true)
      
      const response = await UserService.updateProfile(profileData)
      
      if (response.success && response.data) {
        setUser(response.data)
        appStore.showSuccess('更新成功', '用户资料已更新')
        return response.data
      } else {
        throw new Error(response.message || '更新失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '更新失败'
      appStore.showError('更新失败', message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 获取用户偏好
  const fetchPreferences = async () => {
    try {
      const response = await UserService.getPreferences()
      
      if (response.success && response.data) {
        setPreferences(response.data)
        return response.data
      }
    } catch (error) {
      console.error('获取用户偏好失败:', error)
    }
  }

  // 更新用户偏好
  const updatePreferences = async (prefs: Partial<UserPreferences>) => {
    try {
      const response = await UserService.updatePreferences(prefs)
      
      if (response.success && response.data) {
        setPreferences(response.data)
        appStore.showSuccess('设置已保存', '偏好设置已更新')
        return response.data
      } else {
        throw new Error(response.message || '更新偏好失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '更新偏好失败'
      appStore.showError('更新失败', message)
      throw error
    }
  }

  // 修改密码
  const changePassword = async (passwordData: {
    currentPassword: string
    newPassword: string
    confirmPassword: string
  }) => {
    try {
      setLoading(true)
      
      const response = await UserService.changePassword(passwordData)
      
      if (response.success) {
        appStore.showSuccess('密码已更改', '您的密码已成功更改')
        return true
      } else {
        throw new Error(response.message || '密码更改失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '密码更改失败'
      appStore.showError('更改失败', message)
      return false
    } finally {
      setLoading(false)
    }
  }

  // 初始化用户状态
  const initialize = async () => {
    // 从本地存储恢复用户信息
    const savedUser = localStorage.getItem('user_info')
    const savedPreferences = localStorage.getItem('user_preferences')
    const accessToken = localStorage.getItem('access_token')

    if (savedUser && accessToken) {
      try {
        const userData = JSON.parse(savedUser)
        setUser(userData)
        
        if (savedPreferences) {
          const prefs = JSON.parse(savedPreferences)
          setPreferences(prefs)
        }
        
        // 验证token是否有效
        await fetchCurrentUser()
      } catch (error) {
        console.error('初始化用户状态失败:', error)
        // 清除无效的本地数据
        setUser(null)
        preferences.value = null
      }
    }
  }

  // 重置状态
  const reset = () => {
    user.value = null
    preferences.value = null
    isAuthenticated.value = false
    loading.value = false
    loginLoading.value = false
  }

  return {
    // 状态
    user,
    preferences,
    isAuthenticated,
    loading,
    loginLoading,

    // 计算属性
    userInfo,
    userName,
    userEmail,
    userAvatar,
    isLoggedIn,
    userPreferences,

    // 操作方法
    setUser,
    setPreferences,
    setLoading,
    setLoginLoading,
    login,
    register,
    logout,
    fetchCurrentUser,
    updateProfile,
    fetchPreferences,
    updatePreferences,
    changePassword,
    initialize,
    reset
  }
})

export default useUserStore
