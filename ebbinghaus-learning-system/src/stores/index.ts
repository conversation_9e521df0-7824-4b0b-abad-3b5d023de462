// 状态管理统一导出
export { useAppStore, default as appStore } from './app'
export { useUserStore, default as userStore } from './user'
export { useTaskStore, default as taskStore } from './task'
export { useReviewStore, default as reviewStore } from './review'
export { useMindMapStore, default as mindmapStore } from './mindmap'

// 统一的状态管理对象
import { useAppStore } from './app'
import { useUserStore } from './user'
import { useTaskStore } from './task'
import { useReviewStore } from './review'
import { useMindMapStore } from './mindmap'

export const stores = {
  app: useAppStore,
  user: useUserStore,
  task: useTaskStore,
  review: useReviewStore,
  mindmap: useMindMapStore,
}

// 初始化所有store
export const initializeStores = async () => {
  const appStore = useAppStore()
  const userStore = useUserStore()
  
  // 初始化应用状态
  appStore.initialize()
  
  // 初始化用户状态
  await userStore.initialize()
}

// 重置所有store
export const resetAllStores = () => {
  const appStore = useAppStore()
  const userStore = useUserStore()
  const taskStore = useTaskStore()
  const reviewStore = useReviewStore()
  const mindmapStore = useMindMapStore()
  
  appStore.reset()
  userStore.reset()
  taskStore.reset()
  reviewStore.reset()
  mindmapStore.reset()
}

// 默认导出
export default stores
