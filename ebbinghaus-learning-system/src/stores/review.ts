import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ReviewService } from '@/services/api'
import { useAppStore } from './app'
import type { 
  ReviewSchedule, 
  ReviewSession, 
  ReviewQueryParams,
  PerformanceLevel,
  PaginationInfo
} from '@/types'

/**
 * 复习状态管理
 */
export const useReviewStore = defineStore('review', () => {
  const appStore = useAppStore()

  // 状态
  const schedules = ref<ReviewSchedule[]>([])
  const currentSession = ref<ReviewSession | null>(null)
  const todayReviews = ref<{
    pending: ReviewSchedule[]
    completed: ReviewSchedule[]
    overdue: ReviewSchedule[]
    total: number
  }>({
    pending: [],
    completed: [],
    overdue: [],
    total: 0
  })
  const upcomingReviews = ref<Array<{
    date: string
    reviews: ReviewSchedule[]
    count: number
  }>>([])
  const loading = ref(false)
  const sessionLoading = ref(false)
  const pagination = ref<PaginationInfo | null>(null)

  // 计算属性
  const scheduleList = computed(() => schedules.value)
  const scheduleCount = computed(() => schedules.value.length)
  const pendingSchedules = computed(() => 
    schedules.value.filter(schedule => !schedule.isCompleted)
  )
  const completedSchedules = computed(() => 
    schedules.value.filter(schedule => schedule.isCompleted)
  )
  const todayPendingCount = computed(() => todayReviews.value.pending.length)
  const todayCompletedCount = computed(() => todayReviews.value.completed.length)
  const todayOverdueCount = computed(() => todayReviews.value.overdue.length)
  const isLoading = computed(() => loading.value)
  const hasActiveSession = computed(() => !!currentSession.value)

  // 操作方法
  const setLoading = (state: boolean) => {
    loading.value = state
  }

  const setSessionLoading = (state: boolean) => {
    sessionLoading.value = state
  }

  const setSchedules = (scheduleList: ReviewSchedule[]) => {
    schedules.value = scheduleList
  }

  const setCurrentSession = (session: ReviewSession | null) => {
    currentSession.value = session
  }

  const setPagination = (paginationInfo: PaginationInfo) => {
    pagination.value = paginationInfo
  }

  // 获取复习计划列表
  const fetchSchedules = async (params?: ReviewQueryParams) => {
    try {
      setLoading(true)
      
      const response = await ReviewService.getReviewSchedules(params)
      
      if (response.success && response.data) {
        setSchedules(response.data.schedules)
        setPagination(response.data.pagination)
        return response.data
      } else {
        throw new Error(response.message || '获取复习计划失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取复习计划失败'
      appStore.showError('获取失败', message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 创建复习计划
  const createSchedule = async (data: {
    taskId: string
    customIntervals?: number[]
    startDate?: string
  }) => {
    try {
      setLoading(true)
      
      const response = await ReviewService.createReviewSchedule(data)
      
      if (response.success && response.data) {
        schedules.value.unshift(response.data)
        appStore.showSuccess('创建成功', '复习计划已创建')
        return response.data
      } else {
        throw new Error(response.message || '创建复习计划失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '创建复习计划失败'
      appStore.showError('创建失败', message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 完成复习阶段
  const completeReviewStage = async (scheduleId: string, data: {
    performance: PerformanceLevel
    timeSpent: number
    notes?: string
  }) => {
    try {
      const response = await ReviewService.completeReviewStage(scheduleId, data)
      
      if (response.success && response.data) {
        // 更新复习计划
        const index = schedules.value.findIndex(s => s.id === scheduleId)
        if (index > -1) {
          schedules.value[index] = response.data
        }
        
        // 更新今日复习数据
        await fetchTodayReviews()
        
        appStore.showSuccess('复习完成', `复习表现：${getPerformanceLabel(data.performance)}`)
        return response.data
      } else {
        throw new Error(response.message || '完成复习失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '完成复习失败'
      appStore.showError('操作失败', message)
      throw error
    }
  }

  // 跳过复习
  const skipReview = async (scheduleId: string, reason?: string) => {
    try {
      const response = await ReviewService.skipReview(scheduleId, reason)
      
      if (response.success && response.data) {
        // 更新复习计划
        const index = schedules.value.findIndex(s => s.id === scheduleId)
        if (index > -1) {
          schedules.value[index] = response.data
        }
        
        // 更新今日复习数据
        await fetchTodayReviews()
        
        appStore.showInfo('已跳过', '复习已跳过')
        return response.data
      } else {
        throw new Error(response.message || '跳过复习失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '跳过复习失败'
      appStore.showError('操作失败', message)
      throw error
    }
  }

  // 获取今日复习
  const fetchTodayReviews = async () => {
    try {
      const response = await ReviewService.getTodayReviews()
      
      if (response.success && response.data) {
        todayReviews.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取今日复习失败')
      }
    } catch (error) {
      console.error('获取今日复习失败:', error)
      throw error
    }
  }

  // 获取即将到期的复习
  const fetchUpcomingReviews = async (days: number = 7) => {
    try {
      const response = await ReviewService.getUpcomingReviews(days)
      
      if (response.success && response.data) {
        upcomingReviews.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取即将到期复习失败')
      }
    } catch (error) {
      console.error('获取即将到期复习失败:', error)
      throw error
    }
  }

  // 开始复习会话
  const startReviewSession = async (data: {
    taskIds?: string[]
    scheduleIds?: string[]
    maxDuration?: number
    maxTasks?: number
  }) => {
    try {
      setSessionLoading(true)
      
      const response = await ReviewService.startReviewSession(data)
      
      if (response.success && response.data) {
        setCurrentSession(response.data)
        appStore.showSuccess('会话开始', '复习会话已开始')
        return response.data
      } else {
        throw new Error(response.message || '开始复习会话失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '开始复习会话失败'
      appStore.showError('开始失败', message)
      throw error
    } finally {
      setSessionLoading(false)
    }
  }

  // 结束复习会话
  const endReviewSession = async (sessionId: string, data?: {
    notes?: string
  }) => {
    try {
      setSessionLoading(true)
      
      const response = await ReviewService.endReviewSession(sessionId, data)
      
      if (response.success && response.data) {
        setCurrentSession(null)
        
        // 刷新相关数据
        await fetchTodayReviews()
        
        const session = response.data
        appStore.showSuccess('会话结束', 
          `本次复习：${session.tasksCompleted}个任务，用时${Math.round(session.totalTimeSpent)}分钟`)
        return response.data
      } else {
        throw new Error(response.message || '结束复习会话失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '结束复习会话失败'
      appStore.showError('结束失败', message)
      throw error
    } finally {
      setSessionLoading(false)
    }
  }

  // 批量创建复习计划
  const batchCreateSchedules = async (data: {
    taskIds: string[]
    customIntervals?: number[]
    startDate?: string
  }) => {
    try {
      setLoading(true)
      
      const response = await ReviewService.batchCreateReviewSchedules(data)
      
      if (response.success && response.data) {
        // 添加新创建的计划到列表
        schedules.value.unshift(...response.data.created)
        
        const { created, errors } = response.data
        if (errors.length > 0) {
          appStore.showWarning('部分创建成功', 
            `成功创建${created.length}个计划，${errors.length}个失败`)
        } else {
          appStore.showSuccess('批量创建成功', `成功创建${created.length}个复习计划`)
        }
        
        return response.data
      } else {
        throw new Error(response.message || '批量创建复习计划失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '批量创建复习计划失败'
      appStore.showError('创建失败', message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 调整复习间隔
  const adjustReviewInterval = async (scheduleId: string, data: {
    newInterval: number
    reason?: string
  }) => {
    try {
      const response = await ReviewService.adjustReviewInterval(scheduleId, data)
      
      if (response.success && response.data) {
        // 更新复习计划
        const index = schedules.value.findIndex(s => s.id === scheduleId)
        if (index > -1) {
          schedules.value[index] = response.data
        }
        
        appStore.showSuccess('间隔已调整', `复习间隔已调整为${data.newInterval}天`)
        return response.data
      } else {
        throw new Error(response.message || '调整间隔失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '调整间隔失败'
      appStore.showError('调整失败', message)
      throw error
    }
  }

  // 辅助方法：获取表现等级标签
  const getPerformanceLabel = (performance: PerformanceLevel): string => {
    const labels = {
      poor: '差',
      fair: '一般',
      good: '良好',
      excellent: '优秀'
    }
    return labels[performance]
  }

  // 重置状态
  const reset = () => {
    schedules.value = []
    currentSession.value = null
    todayReviews.value = {
      pending: [],
      completed: [],
      overdue: [],
      total: 0
    }
    upcomingReviews.value = []
    loading.value = false
    sessionLoading.value = false
    pagination.value = null
  }

  return {
    // 状态
    schedules,
    currentSession,
    todayReviews,
    upcomingReviews,
    loading,
    sessionLoading,
    pagination,

    // 计算属性
    scheduleList,
    scheduleCount,
    pendingSchedules,
    completedSchedules,
    todayPendingCount,
    todayCompletedCount,
    todayOverdueCount,
    isLoading,
    hasActiveSession,

    // 操作方法
    setLoading,
    setSessionLoading,
    setSchedules,
    setCurrentSession,
    setPagination,
    fetchSchedules,
    createSchedule,
    completeReviewStage,
    skipReview,
    fetchTodayReviews,
    fetchUpcomingReviews,
    startReviewSession,
    endReviewSession,
    batchCreateSchedules,
    adjustReviewInterval,
    reset
  }
})

export default useReviewStore
