import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { AppState } from '@/types'

/**
 * 应用状态管理
 */
export const useAppStore = defineStore('app', () => {
  // 状态
  const loading = ref(false)
  const error = ref<string | null>(null)
  const theme = ref<'light' | 'dark'>('light')
  const language = ref<'zh-CN' | 'en-US'>('zh-CN')
  const sidebarCollapsed = ref(false)
  const notifications = ref<Array<{
    id: string
    type: 'success' | 'warning' | 'error' | 'info'
    title: string
    message: string
    duration?: number
    timestamp: number
  }>>([])

  // 计算属性
  const isDark = computed(() => theme.value === 'dark')
  const isLoading = computed(() => loading.value)
  const hasError = computed(() => !!error.value)
  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.duration || Date.now() - n.timestamp < (n.duration * 1000))
  )

  // 操作方法
  const setLoading = (state: boolean) => {
    loading.value = state
  }

  const setError = (message: string | null) => {
    error.value = message
  }

  const clearError = () => {
    error.value = null
  }

  const setTheme = (newTheme: 'light' | 'dark') => {
    theme.value = newTheme
    // 保存到本地存储
    localStorage.setItem('theme', newTheme)
    // 更新HTML类名
    document.documentElement.className = newTheme
  }

  const toggleTheme = () => {
    setTheme(theme.value === 'light' ? 'dark' : 'light')
  }

  const setLanguage = (newLanguage: 'zh-CN' | 'en-US') => {
    language.value = newLanguage
    // 保存到本地存储
    localStorage.setItem('language', newLanguage)
  }

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  const setSidebarCollapsed = (collapsed: boolean) => {
    sidebarCollapsed.value = collapsed
  }

  const addNotification = (notification: {
    type: 'success' | 'warning' | 'error' | 'info'
    title: string
    message: string
    duration?: number
  }) => {
    const id = Date.now().toString()
    notifications.value.push({
      id,
      ...notification,
      timestamp: Date.now()
    })

    // 自动移除通知
    if (notification.duration !== 0) {
      const duration = notification.duration || 3000
      setTimeout(() => {
        removeNotification(id)
      }, duration)
    }

    return id
  }

  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clearAllNotifications = () => {
    notifications.value = []
  }

  const showSuccess = (title: string, message: string, duration?: number) => {
    return addNotification({ type: 'success', title, message, duration })
  }

  const showWarning = (title: string, message: string, duration?: number) => {
    return addNotification({ type: 'warning', title, message, duration })
  }

  const showError = (title: string, message: string, duration?: number) => {
    return addNotification({ type: 'error', title, message, duration })
  }

  const showInfo = (title: string, message: string, duration?: number) => {
    return addNotification({ type: 'info', title, message, duration })
  }

  // 初始化
  const initialize = () => {
    // 从本地存储恢复主题
    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' | null
    if (savedTheme) {
      setTheme(savedTheme)
    }

    // 从本地存储恢复语言
    const savedLanguage = localStorage.getItem('language') as 'zh-CN' | 'en-US' | null
    if (savedLanguage) {
      setLanguage(savedLanguage)
    }

    // 监听系统主题变化
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      mediaQuery.addEventListener('change', (e) => {
        if (!localStorage.getItem('theme')) {
          setTheme(e.matches ? 'dark' : 'light')
        }
      })
    }
  }

  // 重置状态
  const reset = () => {
    loading.value = false
    error.value = null
    notifications.value = []
  }

  // 获取应用状态快照
  const getState = (): AppState => {
    return {
      user: null, // 这里会被用户store覆盖
      isAuthenticated: false, // 这里会被用户store覆盖
      loading: loading.value,
      error: error.value
    }
  }

  return {
    // 状态
    loading,
    error,
    theme,
    language,
    sidebarCollapsed,
    notifications,

    // 计算属性
    isDark,
    isLoading,
    hasError,
    unreadNotifications,

    // 操作方法
    setLoading,
    setError,
    clearError,
    setTheme,
    toggleTheme,
    setLanguage,
    toggleSidebar,
    setSidebarCollapsed,
    addNotification,
    removeNotification,
    clearAllNotifications,
    showSuccess,
    showWarning,
    showError,
    showInfo,
    initialize,
    reset,
    getState
  }
})

export default useAppStore
