import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { MindMapService } from '@/services/api'
import { useAppStore } from './app'
import type { 
  MindMap, 
  MindMapNode, 
  MindMapEdge,
  CreateMindMapForm, 
  UpdateMindMapForm,
  PaginationInfo
} from '@/types'

/**
 * 思维导图状态管理
 */
export const useMindMapStore = defineStore('mindmap', () => {
  const appStore = useAppStore()

  // 状态
  const mindMaps = ref<MindMap[]>([])
  const currentMindMap = ref<MindMap | null>(null)
  const loading = ref(false)
  const saving = ref(false)
  const pagination = ref<PaginationInfo | null>(null)
  const recentMindMaps = ref<MindMap[]>([])
  const favoriteMindMaps = ref<MindMap[]>([])
  const templates = ref<Array<{
    id: string
    name: string
    description: string
    preview: string
    category: string
    template: Partial<MindMap>
  }>>([])

  // 计算属性
  const mindMapList = computed(() => mindMaps.value)
  const mindMapCount = computed(() => mindMaps.value.length)
  const publicMindMaps = computed(() => 
    mindMaps.value.filter(mindMap => mindMap.isPublic)
  )
  const privateMindMaps = computed(() => 
    mindMaps.value.filter(mindMap => !mindMap.isPublic)
  )
  const isLoading = computed(() => loading.value)
  const isSaving = computed(() => saving.value)
  const hasCurrentMindMap = computed(() => !!currentMindMap.value)

  // 操作方法
  const setLoading = (state: boolean) => {
    loading.value = state
  }

  const setSaving = (state: boolean) => {
    saving.value = state
  }

  const setMindMaps = (mindMapList: MindMap[]) => {
    mindMaps.value = mindMapList
  }

  const setCurrentMindMap = (mindMap: MindMap | null) => {
    currentMindMap.value = mindMap
  }

  const setPagination = (paginationInfo: PaginationInfo) => {
    pagination.value = paginationInfo
  }

  // 获取思维导图列表
  const fetchMindMaps = async (params?: {
    page?: number
    limit?: number
    search?: string
    tags?: string[]
    isPublic?: boolean
    sortBy?: 'createdAt' | 'updatedAt' | 'title'
    sortOrder?: 'asc' | 'desc'
  }) => {
    try {
      setLoading(true)
      
      const response = await MindMapService.getMindMaps(params)
      
      if (response.success && response.data) {
        setMindMaps(response.data.mindMaps)
        setPagination(response.data.pagination)
        return response.data
      } else {
        throw new Error(response.message || '获取思维导图列表失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取思维导图列表失败'
      appStore.showError('获取失败', message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 获取思维导图详情
  const fetchMindMapById = async (id: string) => {
    try {
      setLoading(true)
      
      const response = await MindMapService.getMindMapById(id)
      
      if (response.success && response.data) {
        setCurrentMindMap(response.data)
        return response.data
      } else {
        throw new Error(response.message || '获取思维导图详情失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取思维导图详情失败'
      appStore.showError('获取失败', message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 创建思维导图
  const createMindMap = async (mindMapData: CreateMindMapForm) => {
    try {
      setLoading(true)
      
      const response = await MindMapService.createMindMap(mindMapData)
      
      if (response.success && response.data) {
        // 添加到思维导图列表
        mindMaps.value.unshift(response.data)
        
        appStore.showSuccess('创建成功', `思维导图"${response.data.title}"已创建`)
        return response.data
      } else {
        throw new Error(response.message || '创建思维导图失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '创建思维导图失败'
      appStore.showError('创建失败', message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 更新思维导图
  const updateMindMap = async (mindMapData: UpdateMindMapForm) => {
    try {
      setLoading(true)
      
      const response = await MindMapService.updateMindMap(mindMapData)
      
      if (response.success && response.data) {
        // 更新思维导图列表中的项目
        const index = mindMaps.value.findIndex(mindMap => mindMap.id === mindMapData.id)
        if (index > -1) {
          mindMaps.value[index] = response.data
        }
        
        // 更新当前思维导图
        if (currentMindMap.value?.id === mindMapData.id) {
          setCurrentMindMap(response.data)
        }
        
        appStore.showSuccess('更新成功', `思维导图"${response.data.title}"已更新`)
        return response.data
      } else {
        throw new Error(response.message || '更新思维导图失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '更新思维导图失败'
      appStore.showError('更新失败', message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 删除思维导图
  const deleteMindMap = async (id: string) => {
    try {
      setLoading(true)
      
      const response = await MindMapService.deleteMindMap(id)
      
      if (response.success) {
        // 从思维导图列表中移除
        const index = mindMaps.value.findIndex(mindMap => mindMap.id === id)
        if (index > -1) {
          const deletedMindMap = mindMaps.value[index]
          mindMaps.value.splice(index, 1)
          appStore.showSuccess('删除成功', `思维导图"${deletedMindMap.title}"已删除`)
        }
        
        // 清除当前思维导图
        if (currentMindMap.value?.id === id) {
          setCurrentMindMap(null)
        }
        
        return true
      } else {
        throw new Error(response.message || '删除思维导图失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '删除思维导图失败'
      appStore.showError('删除失败', message)
      return false
    } finally {
      setLoading(false)
    }
  }

  // 保存思维导图内容
  const saveMindMapContent = async (id: string, data: {
    nodes: MindMapNode[]
    edges: MindMapEdge[]
    layout?: any
    style?: any
  }) => {
    try {
      setSaving(true)
      
      const response = await MindMapService.saveMindMapContent(id, data)
      
      if (response.success && response.data) {
        // 更新当前思维导图
        if (currentMindMap.value?.id === id) {
          setCurrentMindMap(response.data)
        }
        
        // 更新列表中的项目
        const index = mindMaps.value.findIndex(mindMap => mindMap.id === id)
        if (index > -1) {
          mindMaps.value[index] = response.data
        }
        
        return response.data
      } else {
        throw new Error(response.message || '保存思维导图内容失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '保存思维导图内容失败'
      appStore.showError('保存失败', message)
      throw error
    } finally {
      setSaving(false)
    }
  }

  // 添加节点
  const addNode = async (mindMapId: string, node: Omit<MindMapNode, 'id'>) => {
    try {
      const response = await MindMapService.addNode(mindMapId, node)
      
      if (response.success && response.data) {
        // 更新当前思维导图的节点
        if (currentMindMap.value?.id === mindMapId) {
          currentMindMap.value.nodes.push(response.data)
        }
        
        return response.data
      } else {
        throw new Error(response.message || '添加节点失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '添加节点失败'
      appStore.showError('添加失败', message)
      throw error
    }
  }

  // 更新节点
  const updateNode = async (mindMapId: string, nodeId: string, data: Partial<MindMapNode>) => {
    try {
      const response = await MindMapService.updateNode(mindMapId, nodeId, data)
      
      if (response.success && response.data) {
        // 更新当前思维导图的节点
        if (currentMindMap.value?.id === mindMapId) {
          const index = currentMindMap.value.nodes.findIndex(n => n.id === nodeId)
          if (index > -1) {
            currentMindMap.value.nodes[index] = response.data
          }
        }
        
        return response.data
      } else {
        throw new Error(response.message || '更新节点失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '更新节点失败'
      appStore.showError('更新失败', message)
      throw error
    }
  }

  // 删除节点
  const deleteNode = async (mindMapId: string, nodeId: string) => {
    try {
      const response = await MindMapService.deleteNode(mindMapId, nodeId)
      
      if (response.success) {
        // 从当前思维导图中移除节点
        if (currentMindMap.value?.id === mindMapId) {
          const index = currentMindMap.value.nodes.findIndex(n => n.id === nodeId)
          if (index > -1) {
            currentMindMap.value.nodes.splice(index, 1)
          }
        }
        
        return true
      } else {
        throw new Error(response.message || '删除节点失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '删除节点失败'
      appStore.showError('删除失败', message)
      return false
    }
  }

  // 获取模板列表
  const fetchTemplates = async () => {
    try {
      const response = await MindMapService.getMindMapTemplates()
      
      if (response.success && response.data) {
        templates.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取模板列表失败')
      }
    } catch (error) {
      console.error('获取模板列表失败:', error)
      throw error
    }
  }

  // 从模板创建思维导图
  const createFromTemplate = async (templateId: string, data?: {
    title?: string
    description?: string
    isPublic?: boolean
  }) => {
    try {
      setLoading(true)
      
      const response = await MindMapService.createFromTemplate(templateId, data)
      
      if (response.success && response.data) {
        mindMaps.value.unshift(response.data)
        appStore.showSuccess('创建成功', `从模板创建思维导图"${response.data.title}"`)
        return response.data
      } else {
        throw new Error(response.message || '从模板创建失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '从模板创建失败'
      appStore.showError('创建失败', message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 获取最近访问的思维导图
  const fetchRecentMindMaps = async (limit: number = 10) => {
    try {
      const response = await MindMapService.getRecentMindMaps(limit)
      
      if (response.success && response.data) {
        recentMindMaps.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取最近思维导图失败')
      }
    } catch (error) {
      console.error('获取最近思维导图失败:', error)
      throw error
    }
  }

  // 获取收藏的思维导图
  const fetchFavoriteMindMaps = async () => {
    try {
      const response = await MindMapService.getFavoriteMindMaps()
      
      if (response.success && response.data) {
        favoriteMindMaps.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取收藏思维导图失败')
      }
    } catch (error) {
      console.error('获取收藏思维导图失败:', error)
      throw error
    }
  }

  // 添加到收藏
  const addToFavorites = async (id: string) => {
    try {
      const response = await MindMapService.addToFavorites(id)
      
      if (response.success) {
        // 刷新收藏列表
        await fetchFavoriteMindMaps()
        appStore.showSuccess('已收藏', '思维导图已添加到收藏')
        return true
      } else {
        throw new Error(response.message || '添加收藏失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '添加收藏失败'
      appStore.showError('操作失败', message)
      return false
    }
  }

  // 从收藏中移除
  const removeFromFavorites = async (id: string) => {
    try {
      const response = await MindMapService.removeFromFavorites(id)
      
      if (response.success) {
        // 从收藏列表中移除
        const index = favoriteMindMaps.value.findIndex(mindMap => mindMap.id === id)
        if (index > -1) {
          favoriteMindMaps.value.splice(index, 1)
        }
        
        appStore.showSuccess('已取消收藏', '思维导图已从收藏中移除')
        return true
      } else {
        throw new Error(response.message || '取消收藏失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '取消收藏失败'
      appStore.showError('操作失败', message)
      return false
    }
  }

  // 重置状态
  const reset = () => {
    mindMaps.value = []
    currentMindMap.value = null
    loading.value = false
    saving.value = false
    pagination.value = null
    recentMindMaps.value = []
    favoriteMindMaps.value = []
    templates.value = []
  }

  return {
    // 状态
    mindMaps,
    currentMindMap,
    loading,
    saving,
    pagination,
    recentMindMaps,
    favoriteMindMaps,
    templates,

    // 计算属性
    mindMapList,
    mindMapCount,
    publicMindMaps,
    privateMindMaps,
    isLoading,
    isSaving,
    hasCurrentMindMap,

    // 操作方法
    setLoading,
    setSaving,
    setMindMaps,
    setCurrentMindMap,
    setPagination,
    fetchMindMaps,
    fetchMindMapById,
    createMindMap,
    updateMindMap,
    deleteMindMap,
    saveMindMapContent,
    addNode,
    updateNode,
    deleteNode,
    fetchTemplates,
    createFromTemplate,
    fetchRecentMindMaps,
    fetchFavoriteMindMaps,
    addToFavorites,
    removeFromFavorites,
    reset
  }
})

export default useMindMapStore
