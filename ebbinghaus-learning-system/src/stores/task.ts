import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { TaskService } from '@/services/api'
import { useAppStore } from './app'
import type { 
  LearningTask, 
  CreateTaskForm, 
  UpdateTaskForm, 
  TaskQueryParams,
  TaskStatus,
  PaginationInfo
} from '@/types'

/**
 * 任务状态管理
 */
export const useTaskStore = defineStore('task', () => {
  const appStore = useAppStore()

  // 状态
  const tasks = ref<LearningTask[]>([])
  const currentTask = ref<LearningTask | null>(null)
  const loading = ref(false)
  const pagination = ref<PaginationInfo | null>(null)
  const filters = ref<TaskQueryParams>({})
  const recentTasks = ref<LearningTask[]>([])
  const todayTasks = ref<{
    scheduled: LearningTask[]
    overdue: LearningTask[]
    completed: LearningTask[]
  }>({
    scheduled: [],
    overdue: [],
    completed: []
  })

  // 计算属性
  const taskList = computed(() => tasks.value)
  const taskCount = computed(() => tasks.value.length)
  const completedTasks = computed(() => 
    tasks.value.filter(task => task.status === 'completed')
  )
  const pendingTasks = computed(() => 
    tasks.value.filter(task => task.status === 'pending')
  )
  const inProgressTasks = computed(() => 
    tasks.value.filter(task => task.status === 'in_progress')
  )
  const isLoading = computed(() => loading.value)
  const hasCurrentTask = computed(() => !!currentTask.value)

  // 操作方法
  const setLoading = (state: boolean) => {
    loading.value = state
  }

  const setTasks = (taskList: LearningTask[]) => {
    tasks.value = taskList
  }

  const setCurrentTask = (task: LearningTask | null) => {
    currentTask.value = task
  }

  const setPagination = (paginationInfo: PaginationInfo) => {
    pagination.value = paginationInfo
  }

  const setFilters = (newFilters: TaskQueryParams) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const clearFilters = () => {
    filters.value = {}
  }

  // 获取任务列表
  const fetchTasks = async (params?: TaskQueryParams) => {
    try {
      setLoading(true)

      const queryParams = { ...filters.value, ...params }
      const response = await TaskService.getTasks(queryParams)

      if (response.success && response.data) {
        // 转换后端任务数据为前端格式
        const transformedTasks = response.data.tasks?.map(transformBackendTaskToFrontendSimple) || []
        setTasks(transformedTasks)
        setPagination(response.data.pagination)
        return { ...response.data, tasks: transformedTasks }
      } else {
        throw new Error(response.message || '获取任务列表失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取任务列表失败'
      appStore.showError('获取失败', message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 获取任务详情
  const fetchTaskById = async (id: string) => {
    try {
      setLoading(true)
      
      const response = await TaskService.getTaskById(id)
      
      if (response.success && response.data) {
        setCurrentTask(response.data)
        return response.data
      } else {
        throw new Error(response.message || '获取任务详情失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '获取任务详情失败'
      appStore.showError('获取失败', message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 创建任务
  const createTask = async (taskData: CreateTaskForm) => {
    try {
      setLoading(true)

      // 转换前端数据格式为后端期望的格式
      const backendTaskData = {
        title: taskData.title,
        content: {
          text: taskData.content
        },
        metadata: {
          subject: taskData.type, // 直接使用前端的type作为subject
          estimatedTime: taskData.estimatedDuration || 30,
          priority: mapPriorityToNumber(taskData.priority),
          difficulty: mapDifficultyToNumber(taskData.difficulty),
          tags: taskData.tags || []
        }
      }

      const response = await TaskService.createTask(backendTaskData)

      if (response.success && response.data) {
        // 确保tasks数组已初始化
        if (!tasks.value) {
          tasks.value = []
        }

        // 转换后端数据格式为前端格式
        const frontendTask = transformBackendTaskToFrontend(response.data, taskData)

        // 添加到任务列表
        tasks.value.unshift(frontendTask)

        appStore.showSuccess('创建成功', `任务"${frontendTask.title}"已创建`)
        return frontendTask
      } else {
        throw new Error(response.message || '创建任务失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '创建任务失败'
      appStore.showError('创建失败', message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 删除不需要的分类映射函数

  // 映射前端优先级到后端数字
  const mapPriorityToNumber = (priority: Priority): number => {
    // 现在前端直接使用数字，无需映射
    return priority
  }

  // 映射前端难度到后端数字
  const mapDifficultyToNumber = (difficulty: DifficultyLevel): number => {
    // 现在前端直接使用数字，无需映射
    return difficulty
  }

  // 更新任务
  const updateTask = async (taskData: UpdateTaskForm) => {
    try {
      setLoading(true)
      
      const response = await TaskService.updateTask(taskData)
      
      if (response.success && response.data) {
        // 更新任务列表中的任务
        const index = tasks.value.findIndex(task => task.id === taskData.id)
        if (index > -1) {
          tasks.value[index] = response.data
        }
        
        // 更新当前任务
        if (currentTask.value?.id === taskData.id) {
          setCurrentTask(response.data)
        }
        
        appStore.showSuccess('更新成功', `任务"${response.data.title}"已更新`)
        return response.data
      } else {
        throw new Error(response.message || '更新任务失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '更新任务失败'
      appStore.showError('更新失败', message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 删除任务
  const deleteTask = async (id: string) => {
    try {
      setLoading(true)
      
      const response = await TaskService.deleteTask(id)
      
      if (response.success) {
        // 从任务列表中移除
        const index = tasks.value.findIndex(task => task.id === id)
        if (index > -1) {
          const deletedTask = tasks.value[index]
          tasks.value.splice(index, 1)
          appStore.showSuccess('删除成功', `任务"${deletedTask.title}"已删除`)
        }
        
        // 清除当前任务
        if (currentTask.value?.id === id) {
          setCurrentTask(null)
        }
        
        return true
      } else {
        throw new Error(response.message || '删除任务失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '删除任务失败'
      appStore.showError('删除失败', message)
      return false
    } finally {
      setLoading(false)
    }
  }

  // 更新任务状态
  const updateTaskStatus = async (id: string, status: TaskStatus) => {
    try {
      const response = await TaskService.updateTaskStatus(id, status)
      
      if (response.success && response.data) {
        // 更新任务列表中的任务
        const index = tasks.value.findIndex(task => task.id === id)
        if (index > -1) {
          tasks.value[index] = response.data
        }
        
        // 更新当前任务
        if (currentTask.value?.id === id) {
          setCurrentTask(response.data)
        }
        
        return response.data
      } else {
        throw new Error(response.message || '更新状态失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '更新状态失败'
      appStore.showError('更新失败', message)
      throw error
    }
  }

  // 开始任务
  const startTask = async (id: string) => {
    try {
      const response = await TaskService.startTask(id)
      
      if (response.success && response.data) {
        await updateTaskInStore(response.data)
        appStore.showSuccess('任务已开始', `开始执行任务"${response.data.title}"`)
        return response.data
      } else {
        throw new Error(response.message || '开始任务失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '开始任务失败'
      appStore.showError('操作失败', message)
      throw error
    }
  }

  // 完成任务
  const completeTask = async (id: string, data?: {
    notes?: string
    timeSpent?: number
    performance?: string
  }) => {
    try {
      const response = await TaskService.completeTask(id, data)
      
      if (response.success && response.data) {
        await updateTaskInStore(response.data)
        appStore.showSuccess('任务已完成', `恭喜完成任务"${response.data.title}"！`)
        return response.data
      } else {
        throw new Error(response.message || '完成任务失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '完成任务失败'
      appStore.showError('操作失败', message)
      throw error
    }
  }

  // 获取今日任务
  const fetchTodayTasks = async () => {
    try {
      const response = await TaskService.getTodayTasks()
      
      if (response.success && response.data) {
        todayTasks.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取今日任务失败')
      }
    } catch (error) {
      console.error('获取今日任务失败:', error)
      throw error
    }
  }

  // 获取最近任务
  const fetchRecentTasks = async (limit: number = 10) => {
    try {
      const response = await TaskService.getRecentTasks(limit)
      
      if (response.success && response.data) {
        recentTasks.value = response.data
        return response.data
      } else {
        throw new Error(response.message || '获取最近任务失败')
      }
    } catch (error) {
      console.error('获取最近任务失败:', error)
      throw error
    }
  }

  // 搜索任务
  const searchTasks = async (query: string, params?: {
    limit?: number
    includeContent?: boolean
    includeAttachments?: boolean
  }) => {
    try {
      setLoading(true)
      
      const response = await TaskService.searchTasks(query, params)
      
      if (response.success && response.data) {
        return response.data
      } else {
        throw new Error(response.message || '搜索任务失败')
      }
    } catch (error) {
      const message = error instanceof Error ? error.message : '搜索任务失败'
      appStore.showError('搜索失败', message)
      throw error
    } finally {
      setLoading(false)
    }
  }

  // 辅助方法：更新store中的任务
  const updateTaskInStore = (updatedTask: LearningTask) => {
    const index = tasks.value.findIndex(task => task.id === updatedTask.id)
    if (index > -1) {
      tasks.value[index] = updatedTask
    }
    
    if (currentTask.value?.id === updatedTask.id) {
      setCurrentTask(updatedTask)
    }
  }

  // 重置状态
  const reset = () => {
    tasks.value = []
    currentTask.value = null
    loading.value = false
    pagination.value = null
    filters.value = {}
    recentTasks.value = []
    todayTasks.value = {
      scheduled: [],
      overdue: [],
      completed: []
    }
  }

  return {
    // 状态
    tasks,
    currentTask,
    loading,
    pagination,
    filters,
    recentTasks,
    todayTasks,

    // 计算属性
    taskList,
    taskCount,
    completedTasks,
    pendingTasks,
    inProgressTasks,
    isLoading,
    hasCurrentTask,

    // 操作方法
    setLoading,
    setTasks,
    setCurrentTask,
    setPagination,
    setFilters,
    clearFilters,
    fetchTasks,
    fetchTaskById,
    createTask,
    updateTask,
    deleteTask,
    updateTaskStatus,
    startTask,
    completeTask,
    fetchTodayTasks,
    fetchRecentTasks,
    searchTasks,
    reset
  }
})

// 简化的数据转换函数：用于任务列表
function transformBackendTaskToFrontendSimple(backendTask: any): LearningTask {
  return {
    id: backendTask._id || backendTask.taskId || '',
    title: backendTask.title || '未命名任务',
    description: backendTask.content?.text || '',
    content: backendTask.content?.text || '',
    type: backendTask.metadata?.subject || 'math',
    difficulty: backendTask.metadata?.difficulty || 3,
    estimatedDuration: backendTask.metadata?.estimatedTime || 30,
    tags: backendTask.metadata?.tags || [],
    status: backendTask.status || 'pending',
    priority: backendTask.metadata?.priority || 3,
    dueDate: backendTask.dueDate || undefined,
    completedAt: backendTask.progress?.completedAt || undefined,
    userId: backendTask.userId || '',
    categoryId: '',
    attachments: [],
    metadata: {
      source: '',
      references: [],
      prerequisites: [],
      learningObjectives: [],
      assessmentCriteria: []
    },
    createdAt: backendTask.createdAt || new Date().toISOString(),
    updatedAt: backendTask.updatedAt || new Date().toISOString()
  }
}

// 删除不需要的映射函数，直接使用后端的学科类型

// 数据转换函数：将后端任务数据转换为前端格式
function transformBackendTaskToFrontend(backendTask: any, originalFormData: CreateTaskForm): LearningTask {
  return {
    id: backendTask._id || backendTask.taskId,
    title: backendTask.title,
    description: originalFormData.description,
    content: backendTask.content?.text || originalFormData.content,
    type: backendTask.metadata?.subject || originalFormData.type,
    difficulty: backendTask.metadata?.difficulty || originalFormData.difficulty,
    estimatedDuration: backendTask.metadata?.estimatedTime || originalFormData.estimatedDuration,
    tags: backendTask.metadata?.tags || originalFormData.tags || [],
    status: backendTask.status || 'pending',
    priority: backendTask.metadata?.priority || originalFormData.priority,
    dueDate: originalFormData.dueDate,
    completedAt: backendTask.progress?.completedAt,
    userId: backendTask.userId,
    categoryId: originalFormData.categoryId,
    attachments: [],
    metadata: {
      source: '',
      references: [],
      prerequisites: [],
      learningObjectives: [],
      assessmentCriteria: []
    },
    createdAt: backendTask.createdAt,
    updatedAt: backendTask.updatedAt
  }
}

export default useTaskStore
