<template>
  <div class="app-sidebar">
    <el-menu
      :default-active="activeMenu"
      :collapse="appStore.sidebarCollapsed"
      :unique-opened="true"
      router
      class="sidebar-menu"
    >
      <!-- 仪表板 -->
      <el-menu-item index="/dashboard">
        <el-icon><Odometer /></el-icon>
        <template #title>仪表板</template>
      </el-menu-item>
      
      <!-- 任务管理 -->
      <el-sub-menu index="tasks">
        <template #title>
          <el-icon><Document /></el-icon>
          <span>任务管理</span>
        </template>
        <el-menu-item index="/tasks">
          <el-icon><List /></el-icon>
          <template #title>任务列表</template>
        </el-menu-item>
        <el-menu-item index="/tasks/create">
          <el-icon><Plus /></el-icon>
          <template #title>创建任务</template>
        </el-menu-item>
        <el-menu-item index="/tasks/today">
          <el-icon><Calendar /></el-icon>
          <template #title>今日任务</template>
        </el-menu-item>
      </el-sub-menu>
      
      <!-- 复习管理 -->
      <el-sub-menu index="reviews">
        <template #title>
          <el-icon><Refresh /></el-icon>
          <span>复习管理</span>
        </template>
        <el-menu-item index="/reviews">
          <el-icon><Clock /></el-icon>
          <template #title>复习计划</template>
        </el-menu-item>
        <el-menu-item index="/reviews/today">
          <el-icon><AlarmClock /></el-icon>
          <template #title>今日复习</template>
        </el-menu-item>
        <el-menu-item index="/reviews/session">
          <el-icon><VideoPlay /></el-icon>
          <template #title>复习会话</template>
        </el-menu-item>
      </el-sub-menu>
      
      <!-- 思维导图 -->
      <el-sub-menu index="mindmaps">
        <template #title>
          <el-icon><Connection /></el-icon>
          <span>思维导图</span>
        </template>
        <el-menu-item index="/mindmaps">
          <el-icon><Grid /></el-icon>
          <template #title>我的导图</template>
        </el-menu-item>
        <el-menu-item index="/mindmaps/create">
          <el-icon><Plus /></el-icon>
          <template #title>创建导图</template>
        </el-menu-item>
        <el-menu-item index="/mindmaps/templates">
          <el-icon><Files /></el-icon>
          <template #title>模板库</template>
        </el-menu-item>
      </el-sub-menu>
      
      <!-- 数据分析 -->
      <el-menu-item index="/analytics">
        <el-icon><TrendCharts /></el-icon>
        <template #title>数据分析</template>
      </el-menu-item>
      
      <!-- 设置 -->
      <el-menu-item index="/settings">
        <el-icon><Setting /></el-icon>
        <template #title>设置</template>
      </el-menu-item>
    </el-menu>
    
    <!-- 底部信息 -->
    <div class="sidebar-footer" v-if="!appStore.sidebarCollapsed">
      <div class="user-stats">
        <div class="stat-item">
          <span class="stat-label">今日学习</span>
          <span class="stat-value">{{ todayStudyTime }}分钟</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">连续天数</span>
          <span class="stat-value">{{ streakDays }}天</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useAppStore } from '@/stores'
import {
  Odometer,
  Document,
  List,
  Plus,
  Calendar,
  Refresh,
  Clock,
  AlarmClock,
  VideoPlay,
  Connection,
  Grid,
  Files,
  TrendCharts,
  Setting
} from '@element-plus/icons-vue'

const route = useRoute()
const appStore = useAppStore()

// 计算当前激活的菜单项
const activeMenu = computed(() => {
  return route.path
})

// 模拟数据
const todayStudyTime = ref(120)
const streakDays = ref(7)
</script>

<style scoped>
.app-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sidebar-menu {
  flex: 1;
  border: none;
  background: transparent;
}

.sidebar-menu:not(.el-menu--collapse) {
  width: 240px;
}

.sidebar-footer {
  padding: 16px;
  border-top: 1px solid #e8e8e8;
  background: #fafafa;
}

.user-stats {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.stat-label {
  color: #666;
}

.stat-value {
  color: #409EFF;
  font-weight: 600;
}

/* 菜单项样式调整 */
:deep(.el-menu-item) {
  height: 48px;
  line-height: 48px;
}

:deep(.el-sub-menu .el-sub-menu__title) {
  height: 48px;
  line-height: 48px;
}

:deep(.el-menu-item.is-active) {
  background-color: #e6f7ff;
  color: #409EFF;
}

:deep(.el-menu-item:hover) {
  background-color: #f5f5f5;
}

/* 暗色主题 */
.dark-theme .sidebar-footer {
  background: #262626;
  border-top-color: #333;
}

.dark-theme .stat-label {
  color: #999;
}

.dark-theme :deep(.el-menu-item:hover) {
  background-color: #333;
}

.dark-theme :deep(.el-menu-item.is-active) {
  background-color: #1890ff;
  color: #fff;
}
</style>
