<template>
  <div class="app-header">
    <div class="header-left">
      <!-- 菜单折叠按钮 -->
      <el-button 
        type="text" 
        @click="appStore.toggleSidebar()"
        class="sidebar-toggle"
      >
        <el-icon size="18">
          <Fold v-if="!appStore.sidebarCollapsed" />
          <Expand v-else />
        </el-icon>
      </el-button>
      
      <!-- 应用标题 -->
      <div class="app-title">
        <router-link to="/" class="title-link">
          {{ APP_CONFIG.NAME }}
        </router-link>
      </div>
    </div>
    
    <div class="header-center">
      <!-- 搜索框 -->
      <el-input
        v-model="searchQuery"
        placeholder="搜索任务、复习、思维导图..."
        class="search-input"
        @keyup.enter="handleSearch"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
      </el-input>
    </div>
    
    <div class="header-right">
      <!-- 通知按钮 -->
      <el-badge :value="appStore.unreadNotifications.length" class="notification-badge">
        <el-button type="text" @click="showNotifications">
          <el-icon size="18"><Bell /></el-icon>
        </el-button>
      </el-badge>
      
      <!-- 主题切换 -->
      <el-button type="text" @click="appStore.toggleTheme()" class="theme-toggle">
        <el-icon size="18">
          <Sunny v-if="appStore.isDark" />
          <Moon v-else />
        </el-icon>
      </el-button>
      
      <!-- 用户菜单 -->
      <el-dropdown @command="handleUserCommand" class="user-dropdown">
        <div class="user-info">
          <el-avatar 
            :src="userStore.userAvatar" 
            :size="32"
            class="user-avatar"
          >
            {{ userStore.userName.charAt(0).toUpperCase() }}
          </el-avatar>
          <span class="user-name">{{ userStore.userName || '未登录' }}</span>
          <el-icon><ArrowDown /></el-icon>
        </div>
        
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile" v-if="userStore.isLoggedIn">
              <el-icon><User /></el-icon>
              个人资料
            </el-dropdown-item>
            <el-dropdown-item command="settings" v-if="userStore.isLoggedIn">
              <el-icon><Setting /></el-icon>
              设置
            </el-dropdown-item>
            <el-dropdown-item divided command="logout" v-if="userStore.isLoggedIn">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
            <el-dropdown-item command="login" v-if="!userStore.isLoggedIn">
              <el-icon><User /></el-icon>
              登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore, useUserStore } from '@/stores'
import { APP_CONFIG } from '@/constants'
import { 
  Fold, 
  Expand, 
  Search, 
  Bell, 
  Sunny, 
  Moon, 
  User, 
  Setting, 
  SwitchButton, 
  ArrowDown 
} from '@element-plus/icons-vue'

const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()

// 响应式数据
const searchQuery = ref('')

// 方法
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    router.push({
      path: '/search',
      query: { q: searchQuery.value }
    })
  }
}

const showNotifications = () => {
  // 显示通知面板
  appStore.showInfo('通知', '通知功能开发中...')
}

const handleUserCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      userStore.logout()
      router.push('/')
      break
    case 'login':
      router.push('/login')
      break
  }
}
</script>

<style scoped>
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 100%;
  background: #fff;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.sidebar-toggle {
  padding: 8px;
  color: #666;
}

.sidebar-toggle:hover {
  color: #409EFF;
}

.app-title {
  font-size: 18px;
  font-weight: 600;
}

.title-link {
  color: #333;
  text-decoration: none;
}

.title-link:hover {
  color: #409EFF;
}

.header-center {
  flex: 1;
  max-width: 400px;
  margin: 0 40px;
}

.search-input {
  width: 100%;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notification-badge {
  margin-right: 8px;
}

.theme-toggle {
  padding: 8px;
  color: #666;
}

.theme-toggle:hover {
  color: #409EFF;
}

.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 6px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: #f5f5f5;
}

.user-avatar {
  background: #409EFF;
  color: white;
  font-weight: 600;
}

.user-name {
  font-size: 14px;
  color: #333;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 暗色主题 */
.dark-theme .app-header {
  background: #1f1f1f;
}

.dark-theme .title-link {
  color: #fff;
}

.dark-theme .user-name {
  color: #fff;
}

.dark-theme .user-info:hover {
  background-color: #333;
}
</style>
