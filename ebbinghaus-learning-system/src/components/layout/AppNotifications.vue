<template>
  <div class="app-notifications">
    <transition-group name="notification" tag="div" class="notification-container">
      <div
        v-for="notification in appStore.notifications"
        :key="notification.id"
        :class="[
          'notification-item',
          `notification-${notification.type}`
        ]"
      >
        <div class="notification-icon">
          <el-icon size="20">
            <SuccessFilled v-if="notification.type === 'success'" />
            <WarningFilled v-else-if="notification.type === 'warning'" />
            <CircleCloseFilled v-else-if="notification.type === 'error'" />
            <InfoFilled v-else />
          </el-icon>
        </div>
        
        <div class="notification-content">
          <div class="notification-title">{{ notification.title }}</div>
          <div class="notification-message">{{ notification.message }}</div>
        </div>
        
        <div class="notification-close">
          <el-button
            type="text"
            @click="appStore.removeNotification(notification.id)"
            class="close-btn"
          >
            <el-icon size="16"><Close /></el-icon>
          </el-button>
        </div>
      </div>
    </transition-group>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores'
import {
  SuccessFilled,
  WarningFilled,
  CircleCloseFilled,
  InfoFilled,
  Close
} from '@element-plus/icons-vue'

const appStore = useAppStore()
</script>

<style scoped>
.app-notifications {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 2000;
  pointer-events: none;
}

.notification-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 320px;
  max-width: 400px;
  pointer-events: auto;
  border-left: 4px solid;
}

.notification-success {
  border-left-color: #67C23A;
}

.notification-warning {
  border-left-color: #E6A23C;
}

.notification-error {
  border-left-color: #F56C6C;
}

.notification-info {
  border-left-color: #409EFF;
}

.notification-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.notification-success .notification-icon {
  color: #67C23A;
}

.notification-warning .notification-icon {
  color: #E6A23C;
}

.notification-error .notification-icon {
  color: #F56C6C;
}

.notification-info .notification-icon {
  color: #409EFF;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.4;
}

.notification-message {
  font-size: 13px;
  color: #666;
  line-height: 1.4;
  word-break: break-word;
}

.notification-close {
  flex-shrink: 0;
}

.close-btn {
  padding: 4px;
  color: #999;
  min-height: auto;
}

.close-btn:hover {
  color: #666;
  background: #f5f5f5;
}

/* 动画效果 */
.notification-enter-active {
  transition: all 0.3s ease;
}

.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

.notification-move {
  transition: transform 0.3s ease;
}

/* 暗色主题 */
.dark-theme .notification-item {
  background: #2d2d2d;
  color: #fff;
}

.dark-theme .notification-title {
  color: #fff;
}

.dark-theme .notification-message {
  color: #ccc;
}

.dark-theme .close-btn:hover {
  background: #404040;
}
</style>
