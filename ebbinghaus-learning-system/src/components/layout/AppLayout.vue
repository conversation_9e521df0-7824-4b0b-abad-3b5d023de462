<template>
  <div class="app-layout" :class="{ 'dark-theme': appStore.isDark }">
    <el-container>
      <!-- 顶部导航栏 -->
      <el-header class="app-header">
        <AppHeader />
      </el-header>
      
      <el-container>
        <!-- 侧边栏 -->
        <el-aside 
          :width="appStore.sidebarCollapsed ? '64px' : '240px'" 
          class="app-sidebar"
        >
          <AppSidebar />
        </el-aside>
        
        <!-- 主内容区 -->
        <el-main class="app-main">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
    
    <!-- 全局通知 -->
    <AppNotifications />
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores'
import AppHeader from './AppHeader.vue'
import AppSidebar from './AppSidebar.vue'
import AppNotifications from './AppNotifications.vue'

const appStore = useAppStore()
</script>

<style scoped>
.app-layout {
  height: 100vh;
  overflow: hidden;
}

.app-header {
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  padding: 0;
  height: 60px !important;
  line-height: 60px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.app-sidebar {
  background: #fff;
  border-right: 1px solid #e8e8e8;
  transition: width 0.3s ease;
  overflow: hidden;
}

.app-main {
  background: #f5f5f5;
  padding: 20px;
  overflow-y: auto;
}

/* 暗色主题 */
.dark-theme .app-header {
  background: #1f1f1f;
  border-bottom-color: #333;
}

.dark-theme .app-sidebar {
  background: #1f1f1f;
  border-right-color: #333;
}

.dark-theme .app-main {
  background: #141414;
}
</style>
