<template>
  <div class="mindmap-viewer" :class="{ 'fullscreen': isFullscreen }">
    <!-- 工具栏 -->
    <div class="viewer-toolbar" v-if="showToolbar">
      <div class="toolbar-left">
        <h3 class="mindmap-title">{{ mindMap.title }}</h3>
        <el-tag v-if="mindMap.isPublic" type="success" size="small">公开</el-tag>
        <el-tag v-else type="info" size="small">私有</el-tag>
      </div>
      
      <div class="toolbar-right">
        <el-button-group size="small">
          <el-button @click="zoomIn">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
          <el-button @click="resetZoom">
            <el-icon><Refresh /></el-icon>
          </el-button>
          <el-button @click="zoomOut">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
        </el-button-group>
        
        <el-button @click="toggleFullscreen" size="small">
          <el-icon><FullScreen v-if="!isFullscreen" /><Aim v-else /></el-icon>
        </el-button>
        
        <el-dropdown @command="handleAction" trigger="click" v-if="showActions">
          <el-button size="small">
            <el-icon><MoreFilled /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="edit">
                <el-icon><Edit /></el-icon>
                编辑
              </el-dropdown-item>
              <el-dropdown-item command="export">
                <el-icon><Download /></el-icon>
                导出
              </el-dropdown-item>
              <el-dropdown-item command="share">
                <el-icon><Share /></el-icon>
                分享
              </el-dropdown-item>
              <el-dropdown-item command="favorite" v-if="!isFavorite">
                <el-icon><Star /></el-icon>
                收藏
              </el-dropdown-item>
              <el-dropdown-item command="unfavorite" v-else>
                <el-icon><StarFilled /></el-icon>
                取消收藏
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <!-- 思维导图画布 -->
    <div class="mindmap-canvas" ref="canvasRef">
      <div 
        class="mindmap-container"
        :style="containerStyle"
        @wheel="handleWheel"
        @mousedown="handleMouseDown"
        @mousemove="handleMouseMove"
        @mouseup="handleMouseUp"
      >
        <!-- 根节点 -->
        <MindMapNode
          v-if="rootNode"
          :node="rootNode"
          :is-root="true"
          :zoom="zoom"
          @node-click="handleNodeClick"
        />
        
        <!-- 子节点 -->
        <MindMapNode
          v-for="node in childNodes"
          :key="node.id"
          :node="node"
          :is-root="false"
          :zoom="zoom"
          @node-click="handleNodeClick"
        />
        
        <!-- 连接线 -->
        <svg class="mindmap-edges" :style="svgStyle">
          <MindMapEdge
            v-for="edge in mindMap.edges"
            :key="edge.id"
            :edge="edge"
            :nodes="allNodes"
            :zoom="zoom"
          />
        </svg>
      </div>
    </div>
    
    <!-- 缩放指示器 -->
    <div class="zoom-indicator" v-if="showZoomIndicator">
      {{ Math.round(zoom * 100) }}%
    </div>
    
    <!-- 节点详情面板 -->
    <el-drawer
      v-model="showNodeDetail"
      title="节点详情"
      direction="rtl"
      size="400px"
    >
      <div v-if="selectedNode" class="node-detail">
        <div class="detail-section">
          <h4>基本信息</h4>
          <p><strong>标题:</strong> {{ selectedNode.title }}</p>
          <p><strong>内容:</strong> {{ selectedNode.content || '无' }}</p>
          <p><strong>类型:</strong> {{ getNodeTypeLabel(selectedNode.type) }}</p>
        </div>
        
        <div class="detail-section" v-if="selectedNode.metadata">
          <h4>扩展信息</h4>
          <div v-for="(value, key) in selectedNode.metadata" :key="key" class="metadata-item">
            <strong>{{ key }}:</strong> {{ value }}
          </div>
        </div>
        
        <div class="detail-section">
          <h4>样式信息</h4>
          <div class="style-preview" :style="getNodeStyle(selectedNode)">
            样式预览
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import type { MindMap, MindMapNode as MindMapNodeType } from '@/types'
import MindMapNode from './MindMapNode.vue'
import MindMapEdge from './MindMapEdge.vue'
import {
  ZoomIn,
  ZoomOut,
  Refresh,
  FullScreen,
  Aim,
  MoreFilled,
  Edit,
  Download,
  Share,
  Star,
  StarFilled
} from '@element-plus/icons-vue'

// Props
interface Props {
  mindMap: MindMap
  showToolbar?: boolean
  showActions?: boolean
  showZoomIndicator?: boolean
  isFavorite?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showToolbar: true,
  showActions: true,
  showZoomIndicator: true,
  isFavorite: false
})

// Emits
const emit = defineEmits<{
  edit: [mindMap: MindMap]
  export: [mindMap: MindMap]
  share: [mindMap: MindMap]
  favorite: [mindMapId: string]
  unfavorite: [mindMapId: string]
  nodeClick: [node: MindMapNodeType]
}>()

// 响应式数据
const canvasRef = ref<HTMLElement>()
const zoom = ref(1)
const panX = ref(0)
const panY = ref(0)
const isFullscreen = ref(false)
const isDragging = ref(false)
const dragStart = ref({ x: 0, y: 0 })
const showNodeDetail = ref(false)
const selectedNode = ref<MindMapNodeType | null>(null)

// 计算属性
const allNodes = computed(() => props.mindMap.nodes)

const rootNode = computed(() => {
  return allNodes.value.find(node => node.type === 'root')
})

const childNodes = computed(() => {
  return allNodes.value.filter(node => node.type !== 'root')
})

const containerStyle = computed(() => ({
  transform: `translate(${panX.value}px, ${panY.value}px) scale(${zoom.value})`,
  transformOrigin: 'center center'
}))

const svgStyle = computed(() => ({
  position: 'absolute' as const,
  top: '0',
  left: '0',
  width: '100%',
  height: '100%',
  pointerEvents: 'none' as const,
  zIndex: '1'
}))

// 方法
const zoomIn = () => {
  zoom.value = Math.min(zoom.value * 1.2, 3)
}

const zoomOut = () => {
  zoom.value = Math.max(zoom.value / 1.2, 0.1)
}

const resetZoom = () => {
  zoom.value = 1
  panX.value = 0
  panY.value = 0
}

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  if (isFullscreen.value) {
    document.documentElement.requestFullscreen?.()
  } else {
    document.exitFullscreen?.()
  }
}

const handleWheel = (event: WheelEvent) => {
  event.preventDefault()
  const delta = event.deltaY > 0 ? 0.9 : 1.1
  zoom.value = Math.max(0.1, Math.min(3, zoom.value * delta))
}

const handleMouseDown = (event: MouseEvent) => {
  if (event.button === 0) { // 左键
    isDragging.value = true
    dragStart.value = { x: event.clientX - panX.value, y: event.clientY - panY.value }
  }
}

const handleMouseMove = (event: MouseEvent) => {
  if (isDragging.value) {
    panX.value = event.clientX - dragStart.value.x
    panY.value = event.clientY - dragStart.value.y
  }
}

const handleMouseUp = () => {
  isDragging.value = false
}

const handleAction = (command: string) => {
  switch (command) {
    case 'edit':
      emit('edit', props.mindMap)
      break
    case 'export':
      emit('export', props.mindMap)
      break
    case 'share':
      emit('share', props.mindMap)
      break
    case 'favorite':
      emit('favorite', props.mindMap.id)
      break
    case 'unfavorite':
      emit('unfavorite', props.mindMap.id)
      break
  }
}

const handleNodeClick = (node: MindMapNodeType) => {
  selectedNode.value = node
  showNodeDetail.value = true
  emit('nodeClick', node)
}

const getNodeTypeLabel = (type: string) => {
  const typeLabels = {
    root: '根节点',
    branch: '分支节点',
    leaf: '叶子节点'
  }
  return typeLabels[type as keyof typeof typeLabels] || type
}

const getNodeStyle = (node: MindMapNodeType) => {
  return {
    backgroundColor: node.style?.backgroundColor || '#fff',
    color: node.style?.color || '#333',
    borderColor: node.style?.borderColor || '#ddd',
    borderRadius: '4px',
    padding: '8px',
    border: '1px solid'
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('mouseup', handleMouseUp)
  document.addEventListener('mousemove', handleMouseMove)
})

onUnmounted(() => {
  document.removeEventListener('mouseup', handleMouseUp)
  document.removeEventListener('mousemove', handleMouseMove)
})
</script>

<style scoped>
.mindmap-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

.mindmap-viewer.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  border-radius: 0;
}

.viewer-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.mindmap-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mindmap-canvas {
  flex: 1;
  position: relative;
  overflow: hidden;
  cursor: grab;
}

.mindmap-canvas:active {
  cursor: grabbing;
}

.mindmap-container {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.1s ease;
}

.mindmap-edges {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.zoom-indicator {
  position: absolute;
  bottom: 16px;
  right: 16px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  pointer-events: none;
}

.node-detail {
  padding: 16px 0;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h4 {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 8px;
}

.detail-section p {
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.5;
}

.metadata-item {
  margin: 8px 0;
  font-size: 14px;
}

.style-preview {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

/* 暗色主题 */
.dark-theme .mindmap-viewer {
  background: #1f1f1f;
}

.dark-theme .viewer-toolbar {
  background: #2d2d2d;
  border-bottom-color: #404040;
}

.dark-theme .mindmap-title {
  color: #fff;
}

.dark-theme .detail-section h4 {
  color: #fff;
  border-bottom-color: #404040;
}

.dark-theme .detail-section p {
  color: #ccc;
}

.dark-theme .metadata-item {
  color: #ccc;
}
</style>
