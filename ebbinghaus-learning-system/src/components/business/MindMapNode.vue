<template>
  <div
    class="mindmap-node"
    :class="[
      `node-${node.type}`,
      { 'node-selected': isSelected, 'node-root': isRoot }
    ]"
    :style="nodeStyle"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <!-- 节点图标 -->
    <div class="node-icon" v-if="showIcon">
      <el-icon :size="iconSize">
        <component :is="getNodeIcon()" />
      </el-icon>
    </div>
    
    <!-- 节点内容 -->
    <div class="node-content">
      <div class="node-title">{{ node.title }}</div>
      <div class="node-description" v-if="node.content && showDescription">
        {{ truncateText(node.content, 50) }}
      </div>
    </div>
    
    <!-- 节点标签 -->
    <div class="node-tags" v-if="node.tags && node.tags.length > 0 && showTags">
      <el-tag
        v-for="tag in node.tags.slice(0, 3)"
        :key="tag"
        size="small"
        effect="plain"
        class="node-tag"
      >
        {{ tag }}
      </el-tag>
      <span v-if="node.tags.length > 3" class="more-tags">
        +{{ node.tags.length - 3 }}
      </span>
    </div>
    
    <!-- 节点操作按钮 -->
    <div class="node-actions" v-if="showActions && isHovered">
      <el-button-group size="small">
        <el-button @click.stop="handleEdit">
          <el-icon><Edit /></el-icon>
        </el-button>
        <el-button @click.stop="handleAddChild" v-if="node.type !== 'leaf'">
          <el-icon><Plus /></el-icon>
        </el-button>
        <el-button @click.stop="handleDelete" v-if="!isRoot">
          <el-icon><Delete /></el-icon>
        </el-button>
      </el-button-group>
    </div>
    
    <!-- 展开/收起按钮 -->
    <div 
      class="expand-button" 
      v-if="hasChildren && showExpandButton"
      @click.stop="toggleExpand"
    >
      <el-icon :size="12">
        <ArrowDown v-if="isExpanded" />
        <ArrowRight v-else />
      </el-icon>
    </div>
    
    <!-- 连接点 -->
    <div class="connection-points">
      <div class="connection-point connection-top"></div>
      <div class="connection-point connection-right"></div>
      <div class="connection-point connection-bottom"></div>
      <div class="connection-point connection-left"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import type { MindMapNode as MindMapNodeType } from '@/types'
import {
  Edit,
  Plus,
  Delete,
  ArrowDown,
  ArrowRight,
  Document,
  Folder,
  Star,
  Link
} from '@element-plus/icons-vue'

// Props
interface Props {
  node: MindMapNodeType
  isRoot?: boolean
  isSelected?: boolean
  zoom?: number
  showIcon?: boolean
  showDescription?: boolean
  showTags?: boolean
  showActions?: boolean
  showExpandButton?: boolean
  hasChildren?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isRoot: false,
  isSelected: false,
  zoom: 1,
  showIcon: true,
  showDescription: true,
  showTags: true,
  showActions: true,
  showExpandButton: true,
  hasChildren: false
})

// Emits
const emit = defineEmits<{
  click: [node: MindMapNodeType]
  edit: [node: MindMapNodeType]
  addChild: [node: MindMapNodeType]
  delete: [node: MindMapNodeType]
  expand: [node: MindMapNodeType, expanded: boolean]
}>()

// 响应式数据
const isHovered = ref(false)
const isExpanded = ref(true)

// 计算属性
const nodeStyle = computed(() => {
  const baseStyle = {
    position: 'absolute' as const,
    left: `${props.node.position.x}px`,
    top: `${props.node.position.y}px`,
    transform: `scale(${props.zoom})`,
    transformOrigin: 'center center'
  }

  // 应用节点自定义样式
  if (props.node.style) {
    return {
      ...baseStyle,
      backgroundColor: props.node.style.backgroundColor || '#fff',
      color: props.node.style.color || '#333',
      borderColor: props.node.style.borderColor || '#ddd',
      borderWidth: props.node.style.borderWidth || '1px',
      borderRadius: props.node.style.borderRadius || '8px'
    }
  }

  return baseStyle
})

const iconSize = computed(() => {
  if (props.isRoot) return 20
  if (props.node.type === 'branch') return 16
  return 14
})

// 方法
const getNodeIcon = () => {
  switch (props.node.type) {
    case 'root':
      return Star
    case 'branch':
      return Folder
    case 'leaf':
      return Document
    default:
      return Link
  }
}

const truncateText = (text: string, maxLength: number) => {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

const handleClick = () => {
  emit('click', props.node)
}

const handleMouseEnter = () => {
  isHovered.value = true
}

const handleMouseLeave = () => {
  isHovered.value = false
}

const handleEdit = () => {
  emit('edit', props.node)
}

const handleAddChild = () => {
  emit('addChild', props.node)
}

const handleDelete = () => {
  emit('delete', props.node)
}

const toggleExpand = () => {
  isExpanded.value = !isExpanded.value
  emit('expand', props.node, isExpanded.value)
}
</script>

<style scoped>
.mindmap-node {
  display: flex;
  flex-direction: column;
  min-width: 120px;
  max-width: 200px;
  padding: 12px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.mindmap-node:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.mindmap-node.node-selected {
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.mindmap-node.node-root {
  min-width: 160px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: #667eea;
}

.node-root .node-title {
  font-size: 16px;
  font-weight: 600;
}

.node-branch {
  background: #f8f9fa;
  border-color: #409EFF;
}

.node-leaf {
  background: #fff;
  border-color: #67C23A;
}

.node-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  color: #666;
}

.node-root .node-icon {
  color: white;
}

.node-content {
  flex: 1;
  text-align: center;
}

.node-title {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 4px;
  word-break: break-word;
}

.node-description {
  font-size: 12px;
  color: #666;
  line-height: 1.3;
  margin-bottom: 8px;
}

.node-root .node-description {
  color: rgba(255, 255, 255, 0.8);
}

.node-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
  justify-content: center;
}

.node-tag {
  font-size: 10px;
  height: 18px;
  line-height: 16px;
  padding: 0 4px;
}

.more-tags {
  font-size: 10px;
  color: #999;
  background: #f0f0f0;
  padding: 1px 4px;
  border-radius: 2px;
}

.node-actions {
  position: absolute;
  top: -12px;
  right: -12px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 20;
}

.expand-button {
  position: absolute;
  bottom: -8px;
  right: 8px;
  width: 16px;
  height: 16px;
  background: #409EFF;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 15;
}

.expand-button:hover {
  background: #337ecc;
  transform: scale(1.1);
}

.connection-points {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.connection-point {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #409EFF;
  border: 2px solid white;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.mindmap-node:hover .connection-point {
  opacity: 1;
}

.connection-top {
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
}

.connection-right {
  right: -4px;
  top: 50%;
  transform: translateY(-50%);
}

.connection-bottom {
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
}

.connection-left {
  left: -4px;
  top: 50%;
  transform: translateY(-50%);
}

/* 暗色主题 */
.dark-theme .mindmap-node {
  background: #2d2d2d;
  border-color: #404040;
  color: #fff;
}

.dark-theme .node-branch {
  background: #404040;
}

.dark-theme .node-leaf {
  background: #2d2d2d;
}

.dark-theme .node-description {
  color: #999;
}

.dark-theme .node-icon {
  color: #999;
}

.dark-theme .node-root .node-icon {
  color: white;
}

.dark-theme .more-tags {
  background: #404040;
  color: #ccc;
}

.dark-theme .node-actions {
  background: #2d2d2d;
}
</style>
