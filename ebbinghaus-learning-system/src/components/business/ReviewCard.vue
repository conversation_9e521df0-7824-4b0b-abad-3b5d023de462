<template>
  <div class="review-card" :class="{ 'overdue': isOverdue }">
    <div class="review-header">
      <div class="review-info">
        <h3 class="task-title">{{ task.title }}</h3>
        <div class="review-meta">
          <el-tag 
            :type="getStageTagType()" 
            size="small"
            class="stage-tag"
          >
            第 {{ schedule.currentStage }} 次复习
          </el-tag>
          <el-tag 
            :type="isOverdue ? 'danger' : 'warning'"
            size="small"
            effect="plain"
          >
            {{ isOverdue ? '已过期' : '待复习' }}
          </el-tag>
        </div>
      </div>
      
      <div class="review-actions">
        <el-button 
          type="primary" 
          size="small"
          @click="handleStartReview"
          :loading="loading"
        >
          开始复习
        </el-button>
        <el-dropdown @command="handleAction" trigger="click">
          <el-button type="text" class="action-btn">
            <el-icon><MoreFilled /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="skip">
                <el-icon><Right /></el-icon>
                跳过复习
              </el-dropdown-item>
              <el-dropdown-item command="adjust">
                <el-icon><Setting /></el-icon>
                调整间隔
              </el-dropdown-item>
              <el-dropdown-item command="reset" divided>
                <el-icon><RefreshLeft /></el-icon>
                重置计划
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <div class="review-content">
      <div class="task-description">
        {{ task.description }}
      </div>
      
      <div class="review-details">
        <div class="detail-row">
          <div class="detail-item">
            <el-icon><Clock /></el-icon>
            <span>下次复习: {{ formatNextReview() }}</span>
          </div>
          <div class="detail-item">
            <el-icon><TrendCharts /></el-icon>
            <span>间隔: {{ getCurrentInterval() }}天</span>
          </div>
        </div>
        
        <div class="detail-row">
          <div class="detail-item">
            <el-icon><Star /></el-icon>
            <span>上次表现: {{ getLastPerformance() }}</span>
          </div>
          <div class="detail-item">
            <el-icon><DataAnalysis /></el-icon>
            <span>完成率: {{ getCompletionRate() }}%</span>
          </div>
        </div>
      </div>
      
      <div class="review-progress">
        <div class="progress-header">
          <span class="progress-label">复习进度</span>
          <span class="progress-text">{{ getCompletedStages() }}/{{ getTotalStages() }}</span>
        </div>
        <el-progress 
          :percentage="getProgressPercentage()" 
          :stroke-width="8"
          :show-text="false"
          class="progress-bar"
        />
      </div>
    </div>
    
    <!-- 复习表现选择对话框 -->
    <el-dialog
      v-model="showPerformanceDialog"
      title="复习完成"
      width="400px"
      :close-on-click-modal="false"
    >
      <div class="performance-selection">
        <p class="performance-question">请评价本次复习的表现：</p>
        
        <el-radio-group v-model="selectedPerformance" class="performance-options">
          <el-radio label="poor" class="performance-option">
            <div class="option-content">
              <span class="option-label">差</span>
              <span class="option-desc">完全不记得，需要重新学习</span>
            </div>
          </el-radio>
          <el-radio label="fair" class="performance-option">
            <div class="option-content">
              <span class="option-label">一般</span>
              <span class="option-desc">有些印象，但不够清晰</span>
            </div>
          </el-radio>
          <el-radio label="good" class="performance-option">
            <div class="option-content">
              <span class="option-label">良好</span>
              <span class="option-desc">记得大部分内容</span>
            </div>
          </el-radio>
          <el-radio label="excellent" class="performance-option">
            <div class="option-content">
              <span class="option-label">优秀</span>
              <span class="option-desc">完全记得，非常清晰</span>
            </div>
          </el-radio>
        </el-radio-group>
        
        <div class="time-spent">
          <el-input-number
            v-model="timeSpent"
            :min="1"
            :max="300"
            controls-position="right"
            class="time-input"
          />
          <span class="time-label">分钟</span>
        </div>
        
        <el-input
          v-model="reviewNotes"
          type="textarea"
          placeholder="复习笔记（可选）"
          :rows="3"
          maxlength="500"
          show-word-limit
          class="notes-input"
        />
      </div>
      
      <template #footer>
        <el-button @click="showPerformanceDialog = false">取消</el-button>
        <el-button 
          type="primary" 
          @click="handleCompleteReview"
          :disabled="!selectedPerformance"
          :loading="submitting"
        >
          完成复习
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { dateUtils } from '@/utils'
import type { ReviewSchedule, LearningTask, PerformanceLevel } from '@/types'
import {
  MoreFilled,
  Right,
  Setting,
  RefreshLeft,
  Clock,
  TrendCharts,
  Star,
  DataAnalysis
} from '@element-plus/icons-vue'

// Props
interface Props {
  schedule: ReviewSchedule
  task: LearningTask
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  complete: [scheduleId: string, performance: PerformanceLevel, timeSpent: number, notes?: string]
  skip: [scheduleId: string]
  adjust: [scheduleId: string]
  reset: [scheduleId: string]
}>()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const showPerformanceDialog = ref(false)
const selectedPerformance = ref<PerformanceLevel | ''>('')
const timeSpent = ref(30)
const reviewNotes = ref('')

// 计算属性
const isOverdue = computed(() => {
  return dateUtils.isOverdue(props.schedule.nextReviewDate)
})

const getStageTagType = () => {
  const stage = props.schedule.currentStage
  if (stage <= 2) return 'warning'
  if (stage <= 4) return 'primary'
  return 'success'
}

// 方法
const formatNextReview = () => {
  const date = props.schedule.nextReviewDate
  if (dateUtils.isToday(date)) {
    return '今天'
  }
  if (isOverdue.value) {
    return `过期 ${dateUtils.fromNow(date)}`
  }
  return dateUtils.fromNow(date)
}

const getCurrentInterval = () => {
  const currentInterval = props.schedule.intervals.find(
    interval => interval.stage === props.schedule.currentStage
  )
  return currentInterval?.interval || 0
}

const getLastPerformance = () => {
  const lastPerformance = props.schedule.performance[props.schedule.performance.length - 1]
  if (!lastPerformance) return '暂无'
  
  const performanceLabels = {
    poor: '差',
    fair: '一般',
    good: '良好',
    excellent: '优秀'
  }
  return performanceLabels[lastPerformance.performance]
}

const getCompletionRate = () => {
  const completed = props.schedule.intervals.filter(interval => interval.isCompleted).length
  const total = props.schedule.intervals.length
  return total > 0 ? Math.round((completed / total) * 100) : 0
}

const getCompletedStages = () => {
  return props.schedule.intervals.filter(interval => interval.isCompleted).length
}

const getTotalStages = () => {
  return props.schedule.intervals.length
}

const getProgressPercentage = () => {
  return getCompletionRate()
}

const handleStartReview = () => {
  showPerformanceDialog.value = true
  selectedPerformance.value = ''
  timeSpent.value = 30
  reviewNotes.value = ''
}

const handleAction = (command: string) => {
  switch (command) {
    case 'skip':
      emit('skip', props.schedule.id)
      break
    case 'adjust':
      emit('adjust', props.schedule.id)
      break
    case 'reset':
      emit('reset', props.schedule.id)
      break
  }
}

const handleCompleteReview = () => {
  if (!selectedPerformance.value) return
  
  submitting.value = true
  
  emit('complete', 
    props.schedule.id, 
    selectedPerformance.value as PerformanceLevel,
    timeSpent.value,
    reviewNotes.value || undefined
  )
  
  // 模拟API调用延迟
  setTimeout(() => {
    submitting.value = false
    showPerformanceDialog.value = false
  }, 1000)
}
</script>

<style scoped>
.review-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
}

.review-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.review-card.overdue {
  border-left: 4px solid #F56C6C;
  background: #FEF0F0;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.review-info {
  flex: 1;
  min-width: 0;
}

.task-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.review-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.stage-tag {
  font-size: 12px;
}

.review-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
  margin-left: 16px;
}

.action-btn {
  padding: 4px;
  color: #999;
}

.review-content {
  margin-bottom: 16px;
}

.task-description {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 16px;
}

.review-details {
  margin-bottom: 16px;
}

.detail-row {
  display: flex;
  gap: 24px;
  margin-bottom: 8px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #666;
}

.detail-item .el-icon {
  font-size: 14px;
}

.review-progress {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-label {
  font-size: 13px;
  color: #666;
}

.progress-text {
  font-size: 13px;
  color: #409EFF;
  font-weight: 600;
}

.progress-bar {
  margin-bottom: 0;
}

/* 复习表现对话框样式 */
.performance-selection {
  padding: 8px 0;
}

.performance-question {
  font-size: 16px;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.performance-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.performance-option {
  width: 100%;
  margin: 0;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.performance-option:hover {
  border-color: #409EFF;
  background: #f0f8ff;
}

.performance-option.is-checked {
  border-color: #409EFF;
  background: #e6f7ff;
}

.option-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-left: 24px;
}

.option-label {
  font-weight: 600;
  color: #333;
}

.option-desc {
  font-size: 12px;
  color: #666;
}

.time-spent {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.time-input {
  width: 120px;
}

.time-label {
  font-size: 14px;
  color: #666;
}

.notes-input {
  width: 100%;
}

/* 暗色主题 */
.dark-theme .review-card {
  background: #2d2d2d;
  border-color: #404040;
}

.dark-theme .review-card.overdue {
  background: #3d2d2d;
}

.dark-theme .task-title {
  color: #fff;
}

.dark-theme .task-description {
  color: #ccc;
}

.dark-theme .detail-item {
  color: #999;
}

.dark-theme .review-progress {
  background: #404040;
}

.dark-theme .progress-label {
  color: #999;
}

.dark-theme .performance-question {
  color: #fff;
}

.dark-theme .option-label {
  color: #fff;
}

.dark-theme .option-desc {
  color: #999;
}

.dark-theme .performance-option {
  border-color: #404040;
  background: #2d2d2d;
}

.dark-theme .performance-option:hover {
  background: #404040;
}

.dark-theme .performance-option.is-checked {
  background: #1f3a8a;
}
</style>
