<template>
  <div class="task-list">
    <!-- 列表头部 -->
    <div class="list-header" v-if="showHeader">
      <div class="header-left">
        <h2 class="list-title">{{ title }}</h2>
        <el-badge :value="tasks.length" class="task-count-badge">
          <span class="task-count">{{ tasks.length }} 个任务</span>
        </el-badge>
      </div>
      
      <div class="header-right">
        <!-- 视图切换 -->
        <el-radio-group v-model="viewMode" size="small" class="view-toggle">
          <el-radio-button label="card">
            <el-icon><Grid /></el-icon>
          </el-radio-button>
          <el-radio-button label="list">
            <el-icon><List /></el-icon>
          </el-radio-button>
        </el-radio-group>
        
        <!-- 排序选择 -->
        <el-select 
          v-model="sortBy" 
          placeholder="排序方式" 
          size="small"
          style="width: 120px;"
          @change="handleSortChange"
        >
          <el-option label="创建时间" value="createdAt" />
          <el-option label="更新时间" value="updatedAt" />
          <el-option label="截止时间" value="dueDate" />
          <el-option label="优先级" value="priority" />
          <el-option label="难度" value="difficulty" />
        </el-select>
        
        <!-- 筛选按钮 -->
        <el-button @click="showFilters = !showFilters" size="small">
          <el-icon><Filter /></el-icon>
          筛选
        </el-button>
      </div>
    </div>
    
    <!-- 筛选面板 -->
    <div class="filter-panel" v-if="showFilters">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-select v-model="filters.status" placeholder="状态" clearable size="small">
            <el-option 
              v-for="(config, status) in TASK_CONFIG.STATUSES" 
              :key="status"
              :label="config.label" 
              :value="status" 
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="filters.priority" placeholder="优先级" clearable size="small">
            <el-option 
              v-for="(config, priority) in TASK_CONFIG.PRIORITIES" 
              :key="priority"
              :label="config.label" 
              :value="priority" 
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="filters.difficulty" placeholder="难度" clearable size="small">
            <el-option 
              v-for="(config, difficulty) in TASK_CONFIG.DIFFICULTIES" 
              :key="difficulty"
              :label="config.label" 
              :value="difficulty" 
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button @click="clearFilters" size="small">清除筛选</el-button>
        </el-col>
      </el-row>
    </div>
    
    <!-- 加载状态 -->
    <div class="loading-container" v-if="loading">
      <el-skeleton :rows="3" animated />
    </div>
    
    <!-- 空状态 -->
    <div class="empty-container" v-else-if="filteredTasks.length === 0">
      <el-empty description="暂无任务数据">
        <el-button type="primary" @click="$emit('create')">
          <el-icon><Plus /></el-icon>
          创建第一个任务
        </el-button>
      </el-empty>
    </div>
    
    <!-- 任务列表 -->
    <div v-else>
      <!-- 卡片视图 -->
      <div v-if="viewMode === 'card'" class="card-grid">
        <TaskCard
          v-for="task in paginatedTasks"
          :key="task.id"
          :task="task"
          :show-actions="showActions"
          :compact="compact"
          @edit="handleEdit"
          @delete="handleDelete"
          @status-change="handleStatusChange"
          @title-click="handleTitleClick"
        />
      </div>
      
      <!-- 列表视图 -->
      <div v-else class="list-view">
        <el-table :data="paginatedTasks" stripe>
          <el-table-column prop="title" label="任务名称" min-width="200">
            <template #default="{ row }">
              <div class="task-title-cell" @click="handleTitleClick(row)">
                {{ row.title }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusTagType(row.status)" size="small">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" width="100">
            <template #default="{ row }">
              <el-tag :color="styleUtils.getPriorityColor(row.priority)" size="small" effect="plain">
                {{ getPriorityLabel(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="difficulty" label="难度" width="100">
            <template #default="{ row }">
              <el-tag :color="styleUtils.getDifficultyColor(row.difficulty)" size="small" effect="plain">
                {{ getDifficultyLabel(row.difficulty) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="estimatedDuration" label="预计时长" width="120">
            <template #default="{ row }">
              {{ dateUtils.formatDuration(row.estimatedDuration) }}
            </template>
          </el-table-column>
          <el-table-column prop="createdAt" label="创建时间" width="150">
            <template #default="{ row }">
              {{ dateUtils.format(row.createdAt, 'MM-DD HH:mm') }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" v-if="showActions">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button type="text" size="small" @click="handleDelete(row.id)" class="danger-btn">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 分页 -->
      <div class="pagination-container" v-if="showPagination && totalTasks > pageSize">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="totalTasks"
          layout="total, prev, pager, next, jumper"
          @current-change="handlePageChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { dateUtils, styleUtils } from '@/utils'
import { TASK_CONFIG } from '@/constants'
import TaskCard from './TaskCard.vue'
import type { LearningTask, TaskStatus, TaskQueryParams } from '@/types'
import {
  Grid,
  List,
  Filter,
  Plus
} from '@element-plus/icons-vue'

// Props
interface Props {
  tasks: LearningTask[]
  loading?: boolean
  title?: string
  showHeader?: boolean
  showActions?: boolean
  showPagination?: boolean
  compact?: boolean
  pageSize?: number
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  title: '任务列表',
  showHeader: true,
  showActions: true,
  showPagination: true,
  compact: false,
  pageSize: 20
})

// Emits
const emit = defineEmits<{
  edit: [task: LearningTask]
  delete: [taskId: string]
  statusChange: [taskId: string, status: TaskStatus]
  titleClick: [task: LearningTask]
  create: []
  filtersChange: [filters: TaskQueryParams]
  sortChange: [sortBy: string]
}>()

// 响应式数据
const viewMode = ref<'card' | 'list'>('card')
const showFilters = ref(false)
const sortBy = ref('createdAt')
const currentPage = ref(1)
const filters = ref<TaskQueryParams>({})

// 计算属性
const filteredTasks = computed(() => {
  let result = [...props.tasks]
  
  // 应用筛选
  if (filters.value.status) {
    result = result.filter(task => task.status === filters.value.status)
  }
  if (filters.value.priority) {
    result = result.filter(task => task.priority === filters.value.priority)
  }
  if (filters.value.difficulty) {
    result = result.filter(task => task.difficulty === filters.value.difficulty)
  }
  
  // 应用排序
  result.sort((a, b) => {
    const aValue = a[sortBy.value as keyof LearningTask]
    const bValue = b[sortBy.value as keyof LearningTask]
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return bValue.localeCompare(aValue) // 降序
    }
    
    return 0
  })
  
  return result
})

const totalTasks = computed(() => filteredTasks.value.length)

const paginatedTasks = computed(() => {
  if (!props.showPagination) {
    return filteredTasks.value
  }
  
  const start = (currentPage.value - 1) * props.pageSize
  const end = start + props.pageSize
  return filteredTasks.value.slice(start, end)
})

// 方法
const getStatusTagType = (status: TaskStatus) => {
  const typeMap = {
    pending: '',
    in_progress: 'warning',
    completed: 'success',
    cancelled: 'danger'
  }
  return typeMap[status] || ''
}

const getStatusLabel = (status: TaskStatus) => {
  return TASK_CONFIG.STATUSES[status]?.label || status
}

const getPriorityLabel = (priority: string) => {
  return TASK_CONFIG.PRIORITIES[priority as keyof typeof TASK_CONFIG.PRIORITIES]?.label || priority
}

const getDifficultyLabel = (difficulty: string) => {
  return TASK_CONFIG.DIFFICULTIES[difficulty as keyof typeof TASK_CONFIG.DIFFICULTIES]?.label || difficulty
}

const handleEdit = (task: LearningTask) => {
  emit('edit', task)
}

const handleDelete = (taskId: string) => {
  emit('delete', taskId)
}

const handleStatusChange = (taskId: string, status: TaskStatus) => {
  emit('statusChange', taskId, status)
}

const handleTitleClick = (task: LearningTask) => {
  emit('titleClick', task)
}

const handleSortChange = () => {
  emit('sortChange', sortBy.value)
}

const handlePageChange = (page: number) => {
  currentPage.value = page
}

const clearFilters = () => {
  filters.value = {}
}

// 监听筛选变化
watch(filters, (newFilters) => {
  emit('filtersChange', newFilters)
  currentPage.value = 1 // 重置到第一页
}, { deep: true })
</script>

<style scoped>
.task-list {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.list-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.task-count {
  font-size: 14px;
  color: #666;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.view-toggle {
  margin-right: 8px;
}

.filter-panel {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.loading-container,
.empty-container {
  padding: 40px 0;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
}

.list-view {
  margin-bottom: 20px;
}

.task-title-cell {
  cursor: pointer;
  color: #333;
  font-weight: 500;
}

.task-title-cell:hover {
  color: #409EFF;
}

.danger-btn {
  color: #F56C6C;
}

.danger-btn:hover {
  color: #F56C6C;
  background: #FEF0F0;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e8e8e8;
}

/* 暗色主题 */
.dark-theme .task-list {
  background: #2d2d2d;
}

.dark-theme .list-title {
  color: #fff;
}

.dark-theme .task-count {
  color: #999;
}

.dark-theme .list-header {
  border-bottom-color: #404040;
}

.dark-theme .filter-panel {
  background: #404040;
}

.dark-theme .task-title-cell {
  color: #fff;
}

.dark-theme .pagination-container {
  border-top-color: #404040;
}
</style>
