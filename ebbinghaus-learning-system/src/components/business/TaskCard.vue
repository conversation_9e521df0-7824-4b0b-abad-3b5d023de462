<template>
  <div class="task-card" :class="{ 'compact': compact }">
    <div class="task-header">
      <div class="task-title-section">
        <h3 class="task-title" @click="handleTitleClick">{{ task.title }}</h3>
        <div class="task-meta">
          <el-tag
            :type="getStatusTagType(task.status)"
            size="small"
            class="status-tag"
          >
            {{ TASK_CONFIG.STATUSES[task.status]?.label || task.status }}
          </el-tag>
          <el-tag
            :color="styleUtils.getPriorityColor(task.priority)"
            size="small"
            effect="plain"
            class="priority-tag"
          >
            {{ TASK_CONFIG.PRIORITIES[task.priority]?.label || `优先级${task.priority}` }}
          </el-tag>
          <el-tag
            :color="styleUtils.getDifficultyColor(task.difficulty)"
            size="small"
            effect="plain"
            class="difficulty-tag"
          >
            {{ TASK_CONFIG.DIFFICULTIES[task.difficulty]?.label || `难度${task.difficulty}` }}
          </el-tag>
        </div>
      </div>
      
      <div class="task-actions" v-if="showActions">
        <el-dropdown @command="handleAction" trigger="click">
          <el-button type="text" class="action-btn">
            <el-icon><MoreFilled /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="start" v-if="task.status === 'pending'">
                <el-icon><VideoPlay /></el-icon>
                开始任务
              </el-dropdown-item>
              <el-dropdown-item command="complete" v-if="['pending', 'in_progress'].includes(task.status)">
                <el-icon><Check /></el-icon>
                完成任务
              </el-dropdown-item>
              <el-dropdown-item command="edit" divided>
                <el-icon><Edit /></el-icon>
                编辑
              </el-dropdown-item>
              <el-dropdown-item command="delete" class="danger-item">
                <el-icon><Delete /></el-icon>
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <div class="task-content" v-if="!compact">
      <p class="task-description">{{ task.description }}</p>
      
      <div class="task-details">
        <div class="detail-item">
          <el-icon><Clock /></el-icon>
          <span>预计 {{ dateUtils.formatDuration(task.estimatedDuration) }}</span>
        </div>
        <div class="detail-item" v-if="task.dueDate">
          <el-icon><Calendar /></el-icon>
          <span>{{ formatDueDate(task.dueDate) }}</span>
        </div>
        <div class="detail-item">
          <el-icon><User /></el-icon>
          <span>{{ TASK_CONFIG.TYPES[task.type]?.label || task.type }}</span>
        </div>
      </div>
      
      <div class="task-tags" v-if="task.tags && task.tags.length > 0">
        <el-tag 
          v-for="tag in task.tags" 
          :key="tag" 
          size="small"
          effect="plain"
          class="tag-item"
        >
          {{ tag }}
        </el-tag>
      </div>
    </div>
    
    <div class="task-footer" v-if="!compact">
      <div class="task-progress" v-if="task.status === 'in_progress'">
        <el-progress 
          :percentage="getTaskProgress()" 
          :stroke-width="6"
          :show-text="false"
        />
      </div>
      
      <div class="task-timestamps">
        <span class="timestamp">
          创建于 {{ dateUtils.fromNow(task.createdAt) }}
        </span>
        <span class="timestamp" v-if="task.completedAt">
          完成于 {{ dateUtils.fromNow(task.completedAt) }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

import { dateUtils, styleUtils } from '@/utils'
import { TASK_CONFIG } from '@/constants'
import type { LearningTask, TaskStatus } from '@/types'
import {
  MoreFilled,
  VideoPlay,
  VideoPause,
  Check,
  Edit,
  Delete,
  Clock,
  Calendar,
  User
} from '@element-plus/icons-vue'

// Props
interface Props {
  task: LearningTask
  showActions?: boolean
  compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showActions: true,
  compact: false
})

// Emits
const emit = defineEmits<{
  edit: [task: LearningTask]
  delete: [taskId: string]
  statusChange: [taskId: string, status: TaskStatus]
  titleClick: [task: LearningTask]
}>()

// 计算属性
const getStatusTagType = (status: TaskStatus) => {
  const typeMap = {
    pending: '',
    in_progress: 'warning',
    completed: 'success',
    cancelled: 'danger'
  }
  return typeMap[status] || ''
}

const getTaskProgress = () => {
  // 这里可以根据实际业务逻辑计算进度
  // 暂时返回一个模拟值
  return Math.floor(Math.random() * 100)
}

// 方法
const formatDueDate = (dueDate: string) => {
  const isOverdue = dateUtils.isOverdue(dueDate)
  const formatted = dateUtils.format(dueDate, 'MM-DD HH:mm')
  return isOverdue ? `已过期 ${formatted}` : formatted
}

const handleTitleClick = () => {
  emit('titleClick', props.task)
}

const handleAction = (command: string) => {
  switch (command) {
    case 'start':
      emit('statusChange', props.task.id, 'in_progress')
      break
    case 'complete':
      emit('statusChange', props.task.id, 'completed')
      break
    case 'edit':
      emit('edit', props.task)
      break
    case 'delete':
      emit('delete', props.task.id)
      break
  }
}
</script>

<style scoped>
.task-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e8e8e8;
}

.task-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.task-card.compact {
  padding: 12px;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.task-title-section {
  flex: 1;
  min-width: 0;
}

.task-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  cursor: pointer;
  line-height: 1.4;
  word-break: break-word;
}

.task-title:hover {
  color: #409EFF;
}

.task-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.status-tag,
.priority-tag,
.difficulty-tag {
  font-size: 12px;
}

.task-actions {
  flex-shrink: 0;
  margin-left: 12px;
}

.action-btn {
  padding: 4px;
  color: #999;
}

.action-btn:hover {
  color: #409EFF;
}

.task-content {
  margin-bottom: 12px;
}

.task-description {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 12px 0;
  word-break: break-word;
}

.task-details {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #666;
}

.detail-item .el-icon {
  font-size: 14px;
}

.task-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.tag-item {
  font-size: 12px;
}

.task-footer {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
}

.task-progress {
  margin-bottom: 8px;
}

.task-timestamps {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.timestamp {
  font-size: 12px;
  color: #999;
}

/* 下拉菜单危险项样式 */
:deep(.danger-item) {
  color: #F56C6C;
}

:deep(.danger-item:hover) {
  background-color: #FEF0F0;
  color: #F56C6C;
}

/* 暗色主题 */
.dark-theme .task-card {
  background: #2d2d2d;
  border-color: #404040;
}

.dark-theme .task-title {
  color: #fff;
}

.dark-theme .task-description {
  color: #ccc;
}

.dark-theme .detail-item {
  color: #999;
}

.dark-theme .task-footer {
  border-top-color: #404040;
}

.dark-theme .timestamp {
  color: #666;
}
</style>
