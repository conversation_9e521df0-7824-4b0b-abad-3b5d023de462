<template>
  <div class="mindmap-card" :class="{ 'compact': compact }">
    <div class="card-header">
      <div class="card-title-section">
        <h3 class="card-title" @click="handleTitleClick">{{ mindMap.title }}</h3>
        <div class="card-meta">
          <el-tag 
            :type="mindMap.isPublic ? 'success' : 'info'" 
            size="small"
            class="visibility-tag"
          >
            {{ mindMap.isPublic ? '公开' : '私有' }}
          </el-tag>
          <el-tag 
            v-if="mindMap.tags.length > 0"
            size="small"
            effect="plain"
            class="tag-count"
          >
            {{ mindMap.tags.length }} 个标签
          </el-tag>
        </div>
      </div>
      
      <div class="card-actions" v-if="showActions">
        <el-button 
          type="text" 
          @click="handleFavorite"
          :class="{ 'favorited': isFavorite }"
          class="favorite-btn"
        >
          <el-icon><StarFilled v-if="isFavorite" /><Star v-else /></el-icon>
        </el-button>
        
        <el-dropdown @command="handleAction" trigger="click">
          <el-button type="text" class="action-btn">
            <el-icon><MoreFilled /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="view">
                <el-icon><View /></el-icon>
                查看
              </el-dropdown-item>
              <el-dropdown-item command="edit">
                <el-icon><Edit /></el-icon>
                编辑
              </el-dropdown-item>
              <el-dropdown-item command="duplicate">
                <el-icon><CopyDocument /></el-icon>
                复制
              </el-dropdown-item>
              <el-dropdown-item command="export" divided>
                <el-icon><Download /></el-icon>
                导出
              </el-dropdown-item>
              <el-dropdown-item command="share">
                <el-icon><Share /></el-icon>
                分享
              </el-dropdown-item>
              <el-dropdown-item command="delete" class="danger-item">
                <el-icon><Delete /></el-icon>
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <div class="card-content" v-if="!compact">
      <p class="card-description">{{ mindMap.description || '暂无描述' }}</p>
      
      <div class="card-preview">
        <div class="preview-container">
          <div class="preview-nodes">
            <div 
              v-for="(node, index) in previewNodes" 
              :key="node.id"
              class="preview-node"
              :style="getPreviewNodeStyle(index)"
            >
              {{ node.title }}
            </div>
          </div>
          <div class="preview-overlay" v-if="mindMap.nodes.length > 3">
            +{{ mindMap.nodes.length - 3 }} 个节点
          </div>
        </div>
      </div>
      
      <div class="card-stats">
        <div class="stat-item">
          <el-icon><Connection /></el-icon>
          <span>{{ mindMap.nodes.length }} 个节点</span>
        </div>
        <div class="stat-item">
          <el-icon><Link /></el-icon>
          <span>{{ mindMap.edges.length }} 个连接</span>
        </div>
        <div class="stat-item" v-if="mindMap.tags.length > 0">
          <el-icon><PriceTag /></el-icon>
          <span>{{ mindMap.tags.length }} 个标签</span>
        </div>
      </div>
      
      <div class="card-tags" v-if="mindMap.tags.length > 0">
        <el-tag 
          v-for="tag in displayTags" 
          :key="tag" 
          size="small"
          effect="plain"
          class="tag-item"
        >
          {{ tag }}
        </el-tag>
        <span v-if="mindMap.tags.length > 3" class="more-tags">
          +{{ mindMap.tags.length - 3 }}
        </span>
      </div>
    </div>
    
    <div class="card-footer" v-if="!compact">
      <div class="card-timestamps">
        <span class="timestamp">
          创建于 {{ dateUtils.fromNow(mindMap.createdAt) }}
        </span>
        <span class="timestamp">
          更新于 {{ dateUtils.fromNow(mindMap.updatedAt) }}
        </span>
      </div>
      
      <div class="card-author" v-if="showAuthor">
        <el-avatar :size="20" :src="authorAvatar">
          {{ authorName.charAt(0).toUpperCase() }}
        </el-avatar>
        <span class="author-name">{{ authorName }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { dateUtils } from '@/utils'
import type { MindMap } from '@/types'
import {
  StarFilled,
  Star,
  MoreFilled,
  View,
  Edit,
  CopyDocument,
  Download,
  Share,
  Delete,
  Connection,
  Link,
  PriceTag
} from '@element-plus/icons-vue'

// Props
interface Props {
  mindMap: MindMap
  showActions?: boolean
  showAuthor?: boolean
  compact?: boolean
  isFavorite?: boolean
  authorName?: string
  authorAvatar?: string
}

const props = withDefaults(defineProps<Props>(), {
  showActions: true,
  showAuthor: false,
  compact: false,
  isFavorite: false,
  authorName: '未知用户',
  authorAvatar: ''
})

// Emits
const emit = defineEmits<{
  view: [mindMap: MindMap]
  edit: [mindMap: MindMap]
  duplicate: [mindMap: MindMap]
  export: [mindMap: MindMap]
  share: [mindMap: MindMap]
  delete: [mindMapId: string]
  favorite: [mindMapId: string]
  unfavorite: [mindMapId: string]
  titleClick: [mindMap: MindMap]
}>()

// 计算属性
const previewNodes = computed(() => {
  return props.mindMap.nodes.slice(0, 3)
})

const displayTags = computed(() => {
  return props.mindMap.tags.slice(0, 3)
})

// 方法
const getPreviewNodeStyle = (index: number) => {
  const positions = [
    { left: '20%', top: '30%' },
    { left: '60%', top: '20%' },
    { left: '40%', top: '60%' }
  ]

  return {
    position: 'absolute' as const,
    ...positions[index],
    transform: 'translate(-50%, -50%)'
  } as const
}

const handleTitleClick = () => {
  emit('titleClick', props.mindMap)
}

const handleFavorite = () => {
  if (props.isFavorite) {
    emit('unfavorite', props.mindMap.id)
  } else {
    emit('favorite', props.mindMap.id)
  }
}

const handleAction = (command: string) => {
  switch (command) {
    case 'view':
      emit('view', props.mindMap)
      break
    case 'edit':
      emit('edit', props.mindMap)
      break
    case 'duplicate':
      emit('duplicate', props.mindMap)
      break
    case 'export':
      emit('export', props.mindMap)
      break
    case 'share':
      emit('share', props.mindMap)
      break
    case 'delete':
      emit('delete', props.mindMap.id)
      break
  }
}
</script>

<style scoped>
.mindmap-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e8e8e8;
}

.mindmap-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.mindmap-card.compact {
  padding: 12px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.card-title-section {
  flex: 1;
  min-width: 0;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
  cursor: pointer;
  line-height: 1.4;
  word-break: break-word;
}

.card-title:hover {
  color: #409EFF;
}

.card-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.visibility-tag,
.tag-count {
  font-size: 12px;
}

.card-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
  margin-left: 12px;
}

.favorite-btn {
  padding: 4px;
  color: #999;
}

.favorite-btn.favorited {
  color: #F56C6C;
}

.favorite-btn:hover {
  color: #F56C6C;
}

.action-btn {
  padding: 4px;
  color: #999;
}

.action-btn:hover {
  color: #409EFF;
}

.card-content {
  margin-bottom: 12px;
}

.card-description {
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 12px 0;
  word-break: break-word;
}

.card-preview {
  height: 80px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 12px;
  overflow: hidden;
  position: relative;
}

.preview-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.preview-nodes {
  position: relative;
  width: 100%;
  height: 100%;
}

.preview-node {
  background: #409EFF;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 60px;
}

.preview-overlay {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
}

.card-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: #666;
}

.stat-item .el-icon {
  font-size: 14px;
}

.card-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  align-items: center;
}

.tag-item {
  font-size: 12px;
}

.more-tags {
  font-size: 12px;
  color: #999;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
}

.card-footer {
  border-top: 1px solid #f0f0f0;
  padding-top: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-timestamps {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.timestamp {
  font-size: 12px;
  color: #999;
}

.card-author {
  display: flex;
  align-items: center;
  gap: 6px;
}

.author-name {
  font-size: 12px;
  color: #666;
}

/* 下拉菜单危险项样式 */
:deep(.danger-item) {
  color: #F56C6C;
}

:deep(.danger-item:hover) {
  background-color: #FEF0F0;
  color: #F56C6C;
}

/* 暗色主题 */
.dark-theme .mindmap-card {
  background: #2d2d2d;
  border-color: #404040;
}

.dark-theme .card-title {
  color: #fff;
}

.dark-theme .card-description {
  color: #ccc;
}

.dark-theme .card-preview {
  background: #404040;
}

.dark-theme .stat-item {
  color: #999;
}

.dark-theme .card-footer {
  border-top-color: #404040;
}

.dark-theme .timestamp {
  color: #666;
}

.dark-theme .author-name {
  color: #999;
}

.dark-theme .more-tags {
  background: #404040;
  color: #ccc;
}
</style>
