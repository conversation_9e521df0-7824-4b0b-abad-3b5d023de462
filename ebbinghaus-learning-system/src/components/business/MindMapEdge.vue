<template>
  <g class="mindmap-edge" :class="`edge-${edge.type}`">
    <!-- 连接线路径 -->
    <path
      :d="pathData"
      :stroke="edgeStyle.color"
      :stroke-width="edgeStyle.width"
      :stroke-dasharray="edgeStyle.dashArray"
      fill="none"
      :marker-end="showArrow ? 'url(#arrowhead)' : ''"
      class="edge-path"
    />
    
    <!-- 连接线标签 -->
    <text
      v-if="edge.label"
      :x="labelPosition.x"
      :y="labelPosition.y"
      :font-size="labelStyle.fontSize"
      :fill="labelStyle.color"
      text-anchor="middle"
      dominant-baseline="middle"
      class="edge-label"
    >
      {{ edge.label }}
    </text>
    
    <!-- 箭头标记定义 -->
    <defs v-if="showArrow">
      <marker
        id="arrowhead"
        markerWidth="10"
        markerHeight="7"
        refX="9"
        refY="3.5"
        orient="auto"
      >
        <polygon
          points="0 0, 10 3.5, 0 7"
          :fill="edgeStyle.color"
        />
      </marker>
    </defs>
  </g>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { MindMapEdge as MindMapEdgeType, MindMapNode } from '@/types'

// Props
interface Props {
  edge: MindMapEdgeType
  nodes: MindMapNode[]
  zoom?: number
  showArrow?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  zoom: 1,
  showArrow: true
})

// 计算属性
const sourceNode = computed(() => {
  return props.nodes.find(node => node.id === props.edge.sourceId)
})

const targetNode = computed(() => {
  return props.nodes.find(node => node.id === props.edge.targetId)
})

const edgeStyle = computed(() => {
  const defaultStyle = {
    color: '#999',
    width: 2,
    dashArray: 'none'
  }

  if (props.edge.style) {
    return {
      color: props.edge.style.color || defaultStyle.color,
      width: (props.edge.style.width || defaultStyle.width) * props.zoom,
      dashArray: props.edge.style.dashArray || defaultStyle.dashArray
    }
  }

  // 根据边的类型设置默认样式
  switch (props.edge.type) {
    case 'solid':
      return { ...defaultStyle, width: defaultStyle.width * props.zoom }
    case 'dashed':
      return { 
        ...defaultStyle, 
        width: defaultStyle.width * props.zoom,
        dashArray: '5,5' 
      }
    case 'dotted':
      return { 
        ...defaultStyle, 
        width: defaultStyle.width * props.zoom,
        dashArray: '2,2' 
      }
    default:
      return { ...defaultStyle, width: defaultStyle.width * props.zoom }
  }
})

const labelStyle = computed(() => ({
  fontSize: 12 * props.zoom,
  color: '#666'
}))

const pathData = computed(() => {
  if (!sourceNode.value || !targetNode.value) return ''

  const source = sourceNode.value.position
  const target = targetNode.value.position

  // 计算节点的中心点
  const sourceCenter = {
    x: source.x + 100, // 假设节点宽度为200px，中心为100px
    y: source.y + 25   // 假设节点高度为50px，中心为25px
  }

  const targetCenter = {
    x: target.x + 100,
    y: target.y + 25
  }

  // 根据边的类型生成不同的路径
  switch (props.edge.type) {
    case 'curved':
      return generateCurvedPath(sourceCenter, targetCenter)
    case 'straight':
      return generateStraightPath(sourceCenter, targetCenter)
    case 'orthogonal':
      return generateOrthogonalPath(sourceCenter, targetCenter)
    default:
      return generateCurvedPath(sourceCenter, targetCenter)
  }
})

const labelPosition = computed(() => {
  if (!sourceNode.value || !targetNode.value) {
    return { x: 0, y: 0 }
  }

  const source = sourceNode.value.position
  const target = targetNode.value.position

  return {
    x: (source.x + target.x) / 2 + 100,
    y: (source.y + target.y) / 2 + 25
  }
})

// 方法
const generateStraightPath = (source: { x: number; y: number }, target: { x: number; y: number }) => {
  return `M ${source.x} ${source.y} L ${target.x} ${target.y}`
}

const generateCurvedPath = (source: { x: number; y: number }, target: { x: number; y: number }) => {
  const dx = target.x - source.x

  // 控制点偏移
  const controlOffset = Math.abs(dx) * 0.3
  
  const cp1x = source.x + controlOffset
  const cp1y = source.y
  const cp2x = target.x - controlOffset
  const cp2y = target.y

  return `M ${source.x} ${source.y} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${target.x} ${target.y}`
}

const generateOrthogonalPath = (source: { x: number; y: number }, target: { x: number; y: number }) => {
  const midX = (source.x + target.x) / 2
  
  return `M ${source.x} ${source.y} L ${midX} ${source.y} L ${midX} ${target.y} L ${target.x} ${target.y}`
}
</script>

<style scoped>
.mindmap-edge {
  pointer-events: none;
}

.edge-path {
  transition: all 0.3s ease;
}

.edge-path:hover {
  stroke-width: 3;
  filter: drop-shadow(0 0 3px rgba(64, 158, 255, 0.5));
}

.edge-label {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 500;
  pointer-events: none;
  user-select: none;
}

.edge-solid .edge-path {
  stroke-linecap: round;
}

.edge-dashed .edge-path {
  stroke-linecap: round;
}

.edge-dotted .edge-path {
  stroke-linecap: round;
}

/* 暗色主题 */
.dark-theme .edge-path {
  stroke: #666;
}

.dark-theme .edge-label {
  fill: #999;
}

.dark-theme marker polygon {
  fill: #666;
}
</style>
