<template>
  <div class="mindmap-detail-view">
    <AppLayout>
      <div class="mindmap-detail-container" v-if="mindMap">
        <!-- 页面头部 -->
        <div class="page-header">
          <div class="header-left">
            <el-button @click="$router.back()" class="back-btn">
              <el-icon><ArrowLeft /></el-icon>
              返回
            </el-button>
            <div class="header-info">
              <h1 class="page-title">{{ mindMap.title }}</h1>
              <div class="mindmap-meta">
                <el-tag :type="mindMap.isPublic ? 'success' : 'info'" size="small">
                  {{ mindMap.isPublic ? '公开' : '私有' }}
                </el-tag>
                <span class="node-count">{{ mindMap.nodes.length }} 个节点</span>
                <span class="edge-count">{{ mindMap.edges.length }} 个连接</span>
              </div>
            </div>
          </div>
          <div class="header-right">
            <el-button-group>
              <el-button @click="handleEdit">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button @click="toggleFullscreen">
                <el-icon><FullScreen /></el-icon>
                全屏
              </el-button>
              <el-dropdown @command="handleAction" trigger="click">
                <el-button>
                  <el-icon><MoreFilled /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="duplicate">
                      <el-icon><CopyDocument /></el-icon>
                      复制
                    </el-dropdown-item>
                    <el-dropdown-item command="export">
                      <el-icon><Download /></el-icon>
                      导出
                    </el-dropdown-item>
                    <el-dropdown-item command="share">
                      <el-icon><Share /></el-icon>
                      分享
                    </el-dropdown-item>
                    <el-dropdown-item 
                      :command="isFavorite ? 'unfavorite' : 'favorite'"
                    >
                      <el-icon><StarFilled v-if="isFavorite" /><Star v-else /></el-icon>
                      {{ isFavorite ? '取消收藏' : '收藏' }}
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" class="danger-item">
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-button-group>
          </div>
        </div>

        <!-- 主要内容 -->
        <el-row :gutter="20">
          <!-- 思维导图查看器 -->
          <el-col :span="18">
            <div class="viewer-container">
              <MindMapViewer
                :mind-map="mindMap"
                :show-toolbar="true"
                :show-actions="false"
                :show-zoom-indicator="true"
                :is-favorite="isFavorite"
                @node-click="handleNodeClick"
              />
            </div>
          </el-col>

          <!-- 侧边信息 -->
          <el-col :span="6">
            <!-- 思维导图信息 -->
            <div class="info-card">
              <h3 class="card-title">基本信息</h3>
              <div class="info-list">
                <div class="info-item">
                  <span class="info-label">标题</span>
                  <span class="info-value">{{ mindMap.title }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">描述</span>
                  <span class="info-value">{{ mindMap.description || '暂无描述' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">可见性</span>
                  <span class="info-value">{{ mindMap.isPublic ? '公开' : '私有' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">节点数量</span>
                  <span class="info-value">{{ mindMap.nodes.length }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">连接数量</span>
                  <span class="info-value">{{ mindMap.edges.length }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">创建时间</span>
                  <span class="info-value">{{ dateUtils.format(mindMap.createdAt, 'YYYY-MM-DD HH:mm') }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">更新时间</span>
                  <span class="info-value">{{ dateUtils.format(mindMap.updatedAt, 'YYYY-MM-DD HH:mm') }}</span>
                </div>
              </div>
            </div>

            <!-- 标签 -->
            <div class="info-card" v-if="mindMap.tags.length > 0">
              <h3 class="card-title">标签</h3>
              <div class="tags">
                <el-tag 
                  v-for="tag in mindMap.tags" 
                  :key="tag" 
                  size="small"
                  effect="plain"
                  class="tag-item"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>

            <!-- 布局信息 -->
            <div class="info-card">
              <h3 class="card-title">布局设置</h3>
              <div class="info-list">
                <div class="info-item">
                  <span class="info-label">布局类型</span>
                  <span class="info-value">{{ getLayoutTypeLabel(mindMap.layout.type) }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">方向</span>
                  <span class="info-value">{{ getDirectionLabel(mindMap.layout.direction) }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">节点间距</span>
                  <span class="info-value">{{ mindMap.layout.spacing.node }}px</span>
                </div>
                <div class="info-item">
                  <span class="info-label">层级间距</span>
                  <span class="info-value">{{ mindMap.layout.spacing.rank }}px</span>
                </div>
              </div>
            </div>

            <!-- 样式信息 -->
            <div class="info-card">
              <h3 class="card-title">样式设置</h3>
              <div class="info-list">
                <div class="info-item">
                  <span class="info-label">主题</span>
                  <span class="info-value">{{ getThemeLabel(mindMap.style.theme) }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">背景色</span>
                  <div class="color-preview" :style="{ backgroundColor: mindMap.style.backgroundColor }"></div>
                </div>
                <div class="info-item">
                  <span class="info-label">网格显示</span>
                  <span class="info-value">{{ mindMap.style.gridVisible ? '是' : '否' }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">网格对齐</span>
                  <span class="info-value">{{ mindMap.style.snapToGrid ? '是' : '否' }}</span>
                </div>
              </div>
            </div>

            <!-- 节点列表 -->
            <div class="info-card">
              <h3 class="card-title">节点列表</h3>
              <div class="nodes-list">
                <div 
                  v-for="node in mindMap.nodes" 
                  :key="node.id"
                  class="node-item"
                  @click="handleNodeClick(node)"
                >
                  <div class="node-icon" :class="`node-${node.type}`">
                    <el-icon><component :is="getNodeIcon(node.type)" /></el-icon>
                  </div>
                  <div class="node-info">
                    <div class="node-title">{{ node.title }}</div>
                    <div class="node-type">{{ getNodeTypeLabel(node.type) }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 快速操作 -->
            <div class="info-card">
              <h3 class="card-title">快速操作</h3>
              <div class="quick-actions">
                <el-button class="action-btn" @click="handleEdit">
                  <el-icon><Edit /></el-icon>
                  编辑思维导图
                </el-button>
                <el-button class="action-btn" @click="duplicateMindMap">
                  <el-icon><CopyDocument /></el-icon>
                  复制思维导图
                </el-button>
                <el-button class="action-btn" @click="exportMindMap">
                  <el-icon><Download /></el-icon>
                  导出思维导图
                </el-button>
                <el-button class="action-btn" @click="shareMindMap">
                  <el-icon><Share /></el-icon>
                  分享思维导图
                </el-button>
                <el-button class="action-btn" type="danger" @click="deleteMindMap">
                  <el-icon><Delete /></el-icon>
                  删除思维导图
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 加载状态 -->
      <div v-else-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- 错误状态 -->
      <div v-else class="error-container">
        <el-result
          icon="warning"
          title="思维导图不存在"
          sub-title="请检查思维导图ID是否正确"
        >
          <template #extra>
            <el-button type="primary" @click="$router.push('/mindmaps')">
              返回思维导图列表
            </el-button>
          </template>
        </el-result>
      </div>

      <!-- 节点详情对话框 -->
      <el-dialog v-model="showNodeDetail" title="节点详情" width="500px">
        <div v-if="selectedNode" class="node-detail">
          <div class="detail-section">
            <h4>基本信息</h4>
            <p><strong>标题:</strong> {{ selectedNode.title }}</p>
            <p><strong>内容:</strong> {{ selectedNode.content || '无' }}</p>
            <p><strong>类型:</strong> {{ getNodeTypeLabel(selectedNode.type) }}</p>
          </div>
          
          <div class="detail-section" v-if="selectedNode.tags && selectedNode.tags.length > 0">
            <h4>标签</h4>
            <div class="node-tags">
              <el-tag 
                v-for="tag in selectedNode.tags" 
                :key="tag" 
                size="small"
                effect="plain"
              >
                {{ tag }}
              </el-tag>
            </div>
          </div>
          
          <div class="detail-section" v-if="selectedNode.metadata">
            <h4>扩展信息</h4>
            <div v-for="(value, key) in selectedNode.metadata" :key="key" class="metadata-item">
              <strong>{{ key }}:</strong> {{ value }}
            </div>
          </div>
        </div>
      </el-dialog>
    </AppLayout>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMindMapStore, useAppStore } from '@/stores'
import { AppLayout, MindMapViewer } from '@/components'
import { dateUtils } from '@/utils'
import type { MindMap, MindMapNode } from '@/types'
import {
  ArrowLeft,
  Edit,
  FullScreen,
  MoreFilled,
  CopyDocument,
  Download,
  Share,
  Star,
  StarFilled,
  Delete,
  Document,
  Folder,
  Connection
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const mindmapStore = useMindMapStore()
const appStore = useAppStore()

// 响应式数据
const mindMap = ref<MindMap | null>(null)
const loading = ref(true)
const isFavorite = ref(false)
const showNodeDetail = ref(false)
const selectedNode = ref<MindMapNode | null>(null)

// 方法
const getLayoutTypeLabel = (type: string) => {
  const labels = {
    hierarchical: '层次布局',
    radial: '径向布局',
    force: '力导向布局',
    tree: '树形布局'
  }
  return labels[type as keyof typeof labels] || type
}

const getDirectionLabel = (direction: string) => {
  const labels = {
    'top-bottom': '从上到下',
    'bottom-top': '从下到上',
    'left-right': '从左到右',
    'right-left': '从右到左'
  }
  return labels[direction as keyof typeof labels] || direction
}

const getThemeLabel = (theme: string) => {
  const labels = {
    default: '默认主题',
    dark: '暗色主题',
    light: '浅色主题',
    colorful: '彩色主题'
  }
  return labels[theme as keyof typeof labels] || theme
}

const getNodeIcon = (type: string) => {
  switch (type) {
    case 'root': return Star
    case 'branch': return Folder
    case 'leaf': return Document
    default: return Connection
  }
}

const getNodeTypeLabel = (type: string) => {
  const labels = {
    root: '根节点',
    branch: '分支节点',
    leaf: '叶子节点'
  }
  return labels[type as keyof typeof labels] || type
}

const handleEdit = () => {
  router.push(`/mindmaps/${mindMap.value?.id}/edit`)
}

const toggleFullscreen = () => {
  if (document.fullscreenElement) {
    document.exitFullscreen()
  } else {
    document.documentElement.requestFullscreen()
  }
}

const handleAction = (command: string) => {
  switch (command) {
    case 'duplicate':
      duplicateMindMap()
      break
    case 'export':
      exportMindMap()
      break
    case 'share':
      shareMindMap()
      break
    case 'favorite':
      isFavorite.value = true
      appStore.showSuccess('已收藏', '思维导图已添加到收藏')
      break
    case 'unfavorite':
      isFavorite.value = false
      appStore.showInfo('已取消收藏', '思维导图已从收藏中移除')
      break
    case 'delete':
      deleteMindMap()
      break
  }
}

const handleNodeClick = (node: MindMapNode) => {
  selectedNode.value = node
  showNodeDetail.value = true
}

const duplicateMindMap = () => {
  appStore.showSuccess('复制成功', `已复制思维导图: ${mindMap.value?.title}`)
}

const exportMindMap = () => {
  appStore.showSuccess('导出成功', `已导出思维导图: ${mindMap.value?.title}`)
}

const shareMindMap = () => {
  appStore.showInfo('分享思维导图', `分享思维导图: ${mindMap.value?.title}`)
}

const deleteMindMap = async () => {
  if (!mindMap.value) return
  
  try {
    await mindmapStore.deleteMindMap(mindMap.value.id)
    appStore.showSuccess('删除成功', '思维导图已删除')
    router.push('/mindmaps')
  } catch (error) {
    appStore.showError('删除失败', '删除思维导图时发生错误')
  }
}

const loadMindMap = async () => {
  const mindMapId = route.params.id as string
  try {
    loading.value = true
    mindMap.value = await mindmapStore.fetchMindMapById(mindMapId)
    // 模拟收藏状态
    isFavorite.value = mindMapId === 'mindmap-1'
  } catch (error) {
    console.error('加载思维导图失败:', error)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(() => {
  loadMindMap()
})
</script>

<style scoped>
.mindmap-detail-view {
  min-height: 100vh;
}

.mindmap-detail-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.back-btn {
  margin-top: 8px;
}

.header-info {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
  line-height: 1.2;
}

.mindmap-meta {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.node-count,
.edge-count {
  font-size: 14px;
  color: #666;
}

.header-right {
  flex-shrink: 0;
  margin-left: 20px;
}

.viewer-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: 600px;
  overflow: hidden;
}

.info-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-size: 14px;
  color: #666;
  min-width: 80px;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  text-align: right;
  flex: 1;
}

.color-preview {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  font-size: 12px;
}

.nodes-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.node-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.node-item:hover {
  background-color: #f5f5f5;
}

.node-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
}

.node-icon.node-root {
  background: #667eea;
}

.node-icon.node-branch {
  background: #409EFF;
}

.node-icon.node-leaf {
  background: #67C23A;
}

.node-info {
  flex: 1;
}

.node-title {
  font-size: 13px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.node-type {
  font-size: 11px;
  color: #999;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  width: 100%;
  justify-content: flex-start;
}

.loading-container,
.error-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 40px 20px;
}

.node-detail {
  padding: 16px 0;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 8px;
}

.detail-section p {
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.5;
}

.node-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.metadata-item {
  margin: 8px 0;
  font-size: 14px;
}

/* 下拉菜单危险项样式 */
:deep(.danger-item) {
  color: #F56C6C;
}

:deep(.danger-item:hover) {
  background-color: #FEF0F0;
  color: #F56C6C;
}

/* 暗色主题 */
.dark-theme .page-title {
  color: #fff;
}

.dark-theme .node-count,
.dark-theme .edge-count {
  color: #999;
}

.dark-theme .viewer-container {
  background: #2d2d2d;
}

.dark-theme .info-card {
  background: #2d2d2d;
}

.dark-theme .card-title {
  color: #fff;
}

.dark-theme .info-label {
  color: #999;
}

.dark-theme .info-value {
  color: #fff;
}

.dark-theme .node-item:hover {
  background-color: #404040;
}

.dark-theme .node-title {
  color: #fff;
}

.dark-theme .node-type {
  color: #666;
}

.dark-theme .detail-section h4 {
  color: #fff;
  border-bottom-color: #404040;
}

.dark-theme .detail-section p {
  color: #ccc;
}

.dark-theme .metadata-item {
  color: #ccc;
}
</style>
