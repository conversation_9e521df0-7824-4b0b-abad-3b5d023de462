<template>
  <div class="review-session-view">
    <AppLayout>
      <div class="session-container">
        <!-- 会话头部 -->
        <div class="session-header">
          <div class="header-left">
            <el-button @click="exitSession" class="exit-btn">
              <el-icon><ArrowLeft /></el-icon>
              退出复习
            </el-button>
            <div class="session-info">
              <h1 class="session-title">复习会话</h1>
              <div class="session-meta">
                <span class="progress-text">{{ currentIndex + 1 }}/{{ totalReviews }}</span>
                <span class="time-text">已用时 {{ formatTime(elapsedTime) }}</span>
              </div>
            </div>
          </div>
          <div class="header-right">
            <div class="session-progress">
              <el-progress 
                :percentage="progressPercentage" 
                :stroke-width="8"
                :show-text="false"
              />
            </div>
          </div>
        </div>

        <!-- 复习内容 -->
        <div class="session-content" v-if="currentReview">
          <div class="review-card-container">
            <div class="review-task-info">
              <h2 class="task-title">{{ currentTask?.title }}</h2>
              <div class="task-meta">
                <el-tag size="small">第{{ currentReview.currentStage }}次复习</el-tag>
                <el-tag type="info" size="small">{{ getIntervalText() }}</el-tag>
              </div>
            </div>

            <div class="task-content">
              <div class="content-section">
                <h3>任务描述</h3>
                <p class="task-description">{{ currentTask?.description }}</p>
              </div>
              
              <div class="content-section">
                <h3>学习内容</h3>
                <div class="task-learning-content" v-html="formatContent(currentTask?.content || '')"></div>
              </div>
              
              <div class="content-section" v-if="currentTask?.tags && currentTask.tags.length > 0">
                <h3>相关标签</h3>
                <div class="task-tags">
                  <el-tag 
                    v-for="tag in currentTask.tags" 
                    :key="tag" 
                    size="small"
                    effect="plain"
                  >
                    {{ tag }}
                  </el-tag>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 复习操作 -->
        <div class="session-actions" v-if="currentReview">
          <div class="action-card">
            <h3>复习完成评价</h3>
            <p class="evaluation-prompt">请根据你对这个任务的掌握程度选择相应的评价：</p>
            
            <div class="performance-options">
              <div 
                v-for="option in performanceOptions" 
                :key="option.value"
                class="performance-option"
                :class="{ 'selected': selectedPerformance === option.value }"
                @click="selectPerformance(option.value)"
              >
                <div class="option-icon" :class="option.value">
                  <el-icon><component :is="option.icon" /></el-icon>
                </div>
                <div class="option-content">
                  <div class="option-label">{{ option.label }}</div>
                  <div class="option-description">{{ option.description }}</div>
                  <div class="option-interval">下次复习: {{ option.nextInterval }}</div>
                </div>
              </div>
            </div>

            <div class="review-notes">
              <el-input
                v-model="reviewNotes"
                type="textarea"
                placeholder="复习笔记（可选）"
                :rows="3"
                maxlength="500"
                show-word-limit
              />
            </div>

            <div class="action-buttons">
              <el-button @click="skipReview" size="large">
                <el-icon><Right /></el-icon>
                跳过
              </el-button>
              <el-button 
                type="primary" 
                @click="completeReview"
                :disabled="!selectedPerformance"
                size="large"
              >
                <el-icon><Check /></el-icon>
                完成复习
              </el-button>
            </div>
          </div>
        </div>

        <!-- 会话完成 -->
        <div class="session-complete" v-if="isSessionComplete">
          <div class="complete-card">
            <div class="complete-icon">
              <el-icon size="64"><Trophy /></el-icon>
            </div>
            <h2>复习会话完成！</h2>
            <div class="complete-stats">
              <div class="stat-item">
                <span class="stat-number">{{ completedCount }}</span>
                <span class="stat-label">已完成</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{ skippedCount }}</span>
                <span class="stat-label">已跳过</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">{{ formatTime(totalTime) }}</span>
                <span class="stat-label">总用时</span>
              </div>
            </div>
            <div class="complete-actions">
              <el-button @click="$router.push('/reviews')" size="large">
                返回复习管理
              </el-button>
              <el-button type="primary" @click="startNewSession" size="large">
                开始新会话
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </AppLayout>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useReviewStore, useTaskStore, useAppStore } from '@/stores'
import { AppLayout } from '@/components'
import type { ReviewSchedule, PerformanceLevel } from '@/types'
import {
  ArrowLeft,
  Right,
  Check,
  Trophy,
  Close,
  Warning,
  Star,
  TrendCharts
} from '@element-plus/icons-vue'

const router = useRouter()
const reviewStore = useReviewStore()
const taskStore = useTaskStore()
const appStore = useAppStore()

// 响应式数据
const currentIndex = ref(0)
const selectedPerformance = ref<PerformanceLevel | ''>('')
const reviewNotes = ref('')
const elapsedTime = ref(0)
const totalTime = ref(0)
const completedCount = ref(0)
const skippedCount = ref(0)
const isSessionComplete = ref(false)
const timer = ref<number | null>(null)

// 模拟复习数据
const reviewQueue = ref<ReviewSchedule[]>([])

// 计算属性
const totalReviews = computed(() => reviewQueue.value.length)
const currentReview = computed(() => reviewQueue.value[currentIndex.value])
const currentTask = computed(() => {
  if (!currentReview.value) return null
  return taskStore.tasks.find(task => task.id === currentReview.value.taskId)
})

const progressPercentage = computed(() => {
  if (totalReviews.value === 0) return 0
  return Math.round(((currentIndex.value + 1) / totalReviews.value) * 100)
})

const performanceOptions = computed(() => [
  {
    value: 'poor' as PerformanceLevel,
    label: '差',
    description: '完全不记得，需要重新学习',
    nextInterval: '1天后',
    icon: Close
  },
  {
    value: 'fair' as PerformanceLevel,
    label: '一般',
    description: '有些印象，但不够清晰',
    nextInterval: '2天后',
    icon: Warning
  },
  {
    value: 'good' as PerformanceLevel,
    label: '良好',
    description: '记得大部分内容',
    nextInterval: '4天后',
    icon: Star
  },
  {
    value: 'excellent' as PerformanceLevel,
    label: '优秀',
    description: '完全记得，非常清晰',
    nextInterval: '7天后',
    icon: TrendCharts
  }
])

// 方法
const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}

const formatContent = (content: string) => {
  return content.replace(/\n/g, '<br>')
}

const getIntervalText = () => {
  if (!currentReview.value) return ''
  const currentInterval = currentReview.value.intervals.find(
    interval => interval.stage === currentReview.value.currentStage
  )
  return currentInterval ? `间隔${currentInterval.interval}天` : ''
}

const selectPerformance = (performance: PerformanceLevel) => {
  selectedPerformance.value = performance
}

const completeReview = async () => {
  if (!selectedPerformance.value || !currentReview.value) return
  
  try {
    await reviewStore.completeReviewStage(currentReview.value.id, {
      performance: selectedPerformance.value,
      timeSpent: Math.floor(elapsedTime.value / 60),
      notes: reviewNotes.value || undefined
    })
    
    completedCount.value++
    nextReview()
  } catch (error) {
    appStore.showError('复习失败', '完成复习时发生错误')
  }
}

const skipReview = () => {
  if (!currentReview.value) return
  
  reviewStore.skipReview(currentReview.value.id)
  skippedCount.value++
  nextReview()
}

const nextReview = () => {
  // 重置选择
  selectedPerformance.value = ''
  reviewNotes.value = ''
  
  // 下一个复习
  if (currentIndex.value < totalReviews.value - 1) {
    currentIndex.value++
    elapsedTime.value = 0
  } else {
    // 会话完成
    completeSession()
  }
}

const completeSession = () => {
  isSessionComplete.value = true
  totalTime.value = elapsedTime.value
  stopTimer()
  
  appStore.showSuccess('会话完成', `完成了${completedCount.value}个复习任务`)
}

const exitSession = () => {
  if (completedCount.value > 0 || skippedCount.value > 0) {
    // 有进度，询问是否确认退出
    appStore.showWarning('退出确认', '确定要退出当前复习会话吗？')
  }
  router.push('/reviews')
}

const startNewSession = () => {
  // 重置状态
  currentIndex.value = 0
  completedCount.value = 0
  skippedCount.value = 0
  isSessionComplete.value = false
  elapsedTime.value = 0
  
  // 重新加载复习队列
  loadReviewQueue()
}

const startTimer = () => {
  timer.value = window.setInterval(() => {
    elapsedTime.value++
  }, 1000)
}

const stopTimer = () => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
}

const loadReviewQueue = async () => {
  try {
    await reviewStore.fetchTodayReviews()
    reviewQueue.value = [...reviewStore.todayReviews.pending]
    
    if (reviewQueue.value.length === 0) {
      appStore.showInfo('暂无复习', '今日暂无待复习任务')
      router.push('/reviews')
      return
    }
    
    startTimer()
  } catch (error) {
    appStore.showError('加载失败', '加载复习队列时发生错误')
    router.push('/reviews')
  }
}

// 生命周期
onMounted(() => {
  loadReviewQueue()
})

onUnmounted(() => {
  stopTimer()
})
</script>

<style scoped>
.review-session-view {
  min-height: 100vh;
  background: #f5f5f5;
}

.session-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.exit-btn {
  flex-shrink: 0;
}

.session-info {
  flex: 1;
}

.session-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.session-meta {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #666;
}

.header-right {
  flex-shrink: 0;
  width: 200px;
}

.session-progress {
  width: 100%;
}

.session-content {
  margin-bottom: 24px;
}

.review-card-container {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.review-task-info {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.task-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
}

.task-meta {
  display: flex;
  gap: 8px;
}

.task-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.content-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
}

.task-description {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
  margin: 0;
}

.task-learning-content {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
}

.task-tags {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.session-actions {
  margin-bottom: 24px;
}

.action-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.evaluation-prompt {
  font-size: 14px;
  color: #666;
  margin: 0 0 20px 0;
}

.performance-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.performance-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 2px solid #e8e8e8;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.performance-option:hover {
  border-color: #409EFF;
  background: #f0f8ff;
}

.performance-option.selected {
  border-color: #409EFF;
  background: #e6f7ff;
}

.option-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.option-icon.poor {
  background: #F56C6C;
}

.option-icon.fair {
  background: #E6A23C;
}

.option-icon.good {
  background: #409EFF;
}

.option-icon.excellent {
  background: #67C23A;
}

.option-content {
  flex: 1;
}

.option-label {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.option-description {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

.option-interval {
  font-size: 12px;
  color: #409EFF;
  font-weight: 500;
}

.review-notes {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.session-complete {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.complete-card {
  background: white;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  max-width: 400px;
}

.complete-icon {
  color: #67C23A;
  margin-bottom: 20px;
}

.complete-card h2 {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0 0 24px 0;
}

.complete-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: 32px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  color: #409EFF;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.complete-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

/* 暗色主题 */
.dark-theme .session-header {
  background: #2d2d2d;
}

.dark-theme .session-title {
  color: #fff;
}

.dark-theme .session-meta {
  color: #999;
}

.dark-theme .review-card-container {
  background: #2d2d2d;
}

.dark-theme .task-title {
  color: #fff;
}

.dark-theme .content-section h3 {
  color: #fff;
}

.dark-theme .task-description {
  color: #ccc;
}

.dark-theme .action-card {
  background: #2d2d2d;
}

.dark-theme .action-card h3 {
  color: #fff;
}

.dark-theme .evaluation-prompt {
  color: #999;
}

.dark-theme .performance-option {
  border-color: #404040;
  background: #2d2d2d;
}

.dark-theme .performance-option:hover {
  background: #404040;
}

.dark-theme .option-label {
  color: #fff;
}

.dark-theme .option-description {
  color: #999;
}

.dark-theme .complete-card {
  background: #2d2d2d;
}

.dark-theme .complete-card h2 {
  color: #fff;
}

.dark-theme .stat-label {
  color: #999;
}
</style>
