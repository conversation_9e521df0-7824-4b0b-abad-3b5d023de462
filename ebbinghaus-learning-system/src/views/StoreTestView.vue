<template>
  <div class="store-test-view">
    <div class="page-container">
      <div class="page-header">
        <h1 class="page-title">状态管理测试</h1>
        <p>测试5个状态管理模块的功能</p>
      </div>

      <el-row :gutter="20">
        <!-- 应用状态测试 -->
        <el-col :span="12">
          <div class="card">
            <h3>🏠 应用状态 (AppStore)</h3>
            <el-space direction="vertical" style="width: 100%">
              <div>
                <strong>当前主题:</strong> {{ appStore.theme }}
                <el-button @click="appStore.toggleTheme()" size="small" style="margin-left: 10px;">
                  切换主题
                </el-button>
              </div>
              <div>
                <strong>当前语言:</strong> {{ appStore.language }}
                <el-button @click="toggleLanguage" size="small" style="margin-left: 10px;">
                  切换语言
                </el-button>
              </div>
              <div>
                <strong>侧边栏状态:</strong> {{ appStore.sidebarCollapsed ? '收起' : '展开' }}
                <el-button @click="appStore.toggleSidebar()" size="small" style="margin-left: 10px;">
                  切换侧边栏
                </el-button>
              </div>
              <div>
                <strong>通知数量:</strong> {{ appStore.unreadNotifications.length }}
                <el-button @click="testNotifications" size="small" style="margin-left: 10px;">
                  测试通知
                </el-button>
              </div>
            </el-space>
          </div>
        </el-col>

        <!-- 用户状态测试 -->
        <el-col :span="12">
          <div class="card">
            <h3>👤 用户状态 (UserStore)</h3>
            <el-space direction="vertical" style="width: 100%">
              <div>
                <strong>登录状态:</strong> {{ userStore.isLoggedIn ? '已登录' : '未登录' }}
              </div>
              <div v-if="userStore.isLoggedIn">
                <strong>用户名:</strong> {{ userStore.userName }}
              </div>
              <div v-if="userStore.isLoggedIn">
                <strong>邮箱:</strong> {{ userStore.userEmail }}
              </div>
              <div>
                <strong>加载状态:</strong> {{ userStore.loading ? '加载中' : '空闲' }}
              </div>
              <div>
                <el-button @click="userStore.logout()" size="small" v-if="userStore.isLoggedIn">
                  登出
                </el-button>
                <div v-else>
                  <small>请前往登录页面进行真实登录</small>
                </div>
              </div>
            </el-space>
          </div>
        </el-col>

        <!-- 任务状态测试 -->
        <el-col :span="12">
          <div class="card">
            <h3>📋 任务状态 (TaskStore)</h3>
            <el-space direction="vertical" style="width: 100%">
              <div>
                <strong>任务总数:</strong> {{ taskStore.taskCount }}
              </div>
              <div>
                <strong>已完成:</strong> {{ taskStore.completedTasks.length }}
              </div>
              <div>
                <strong>进行中:</strong> {{ taskStore.inProgressTasks.length }}
              </div>
              <div>
                <strong>待开始:</strong> {{ taskStore.pendingTasks.length }}
              </div>
              <div>
                <strong>加载状态:</strong> {{ taskStore.isLoading ? '加载中' : '空闲' }}
              </div>
              <div>
                <el-button @click="testTaskOperations" size="small" :loading="taskStore.loading">
                  测试任务操作
                </el-button>
              </div>
            </el-space>
          </div>
        </el-col>

        <!-- 复习状态测试 -->
        <el-col :span="12">
          <div class="card">
            <h3>🧠 复习状态 (ReviewStore)</h3>
            <el-space direction="vertical" style="width: 100%">
              <div>
                <strong>复习计划数:</strong> {{ reviewStore.scheduleCount }}
              </div>
              <div>
                <strong>今日待复习:</strong> {{ reviewStore.todayPendingCount }}
              </div>
              <div>
                <strong>今日已完成:</strong> {{ reviewStore.todayCompletedCount }}
              </div>
              <div>
                <strong>今日过期:</strong> {{ reviewStore.todayOverdueCount }}
              </div>
              <div>
                <strong>活跃会话:</strong> {{ reviewStore.hasActiveSession ? '有' : '无' }}
              </div>
              <div>
                <el-button @click="testReviewOperations" size="small" :loading="reviewStore.loading">
                  测试复习操作
                </el-button>
              </div>
            </el-space>
          </div>
        </el-col>

        <!-- 思维导图状态测试 -->
        <el-col :span="12">
          <div class="card">
            <h3>🗺️ 思维导图状态 (MindMapStore)</h3>
            <el-space direction="vertical" style="width: 100%">
              <div>
                <strong>思维导图数:</strong> {{ mindmapStore.mindMapCount }}
              </div>
              <div>
                <strong>公开数量:</strong> {{ mindmapStore.publicMindMaps.length }}
              </div>
              <div>
                <strong>私有数量:</strong> {{ mindmapStore.privateMindMaps.length }}
              </div>
              <div>
                <strong>收藏数量:</strong> {{ mindmapStore.favoriteMindMaps.length }}
              </div>
              <div>
                <strong>保存状态:</strong> {{ mindmapStore.isSaving ? '保存中' : '已保存' }}
              </div>
              <div>
                <el-button @click="testMindMapOperations" size="small" :loading="mindmapStore.loading">
                  测试思维导图操作
                </el-button>
              </div>
            </el-space>
          </div>
        </el-col>

        <!-- 状态管理统计 -->
        <el-col :span="12">
          <div class="card">
            <h3>📊 状态管理统计</h3>
            <el-space direction="vertical" style="width: 100%">
              <div>
                <strong>Store模块数:</strong> 5个
              </div>
              <div>
                <strong>初始化状态:</strong> {{ initStatus }}
              </div>
              <div>
                <strong>响应式状态:</strong> 正常
              </div>
              <div>
                <strong>计算属性:</strong> 正常
              </div>
              <div>
                <el-button @click="testAllStores" size="small">
                  测试所有Store
                </el-button>
                <el-button @click="resetAllStores" size="small" type="danger">
                  重置所有状态
                </el-button>
              </div>
            </el-space>
          </div>
        </el-col>
      </el-row>

      <!-- 测试结果 -->
      <div class="card" style="margin-top: 20px;">
        <h3>🧪 测试结果</h3>
        <el-space direction="vertical" style="width: 100%">
          <div v-for="result in testResults" :key="result.store">
            <el-tag :type="result.success ? 'success' : 'danger'">
              {{ result.store }}: {{ result.success ? '✅ 正常' : '❌ 异常' }}
            </el-tag>
            <span style="margin-left: 10px;">{{ result.message }}</span>
          </div>
        </el-space>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { 
  useAppStore, 
  useUserStore, 
  useTaskStore, 
  useReviewStore, 
  useMindMapStore,
  resetAllStores as resetStores
} from '@/stores'

// 使用状态管理
const appStore = useAppStore()
const userStore = useUserStore()
const taskStore = useTaskStore()
const reviewStore = useReviewStore()
const mindmapStore = useMindMapStore()

// 响应式数据
const initStatus = ref('检查中...')
const testResults = ref<Array<{
  store: string
  success: boolean
  message: string
}>>([])

// 方法
const toggleLanguage = () => {
  const newLang = appStore.language === 'zh-CN' ? 'en-US' : 'zh-CN'
  appStore.setLanguage(newLang)
}

const testNotifications = () => {
  appStore.showSuccess('测试成功', '这是一个成功通知')
  setTimeout(() => {
    appStore.showWarning('测试警告', '这是一个警告通知')
  }, 1000)
  setTimeout(() => {
    appStore.showError('测试错误', '这是一个错误通知')
  }, 2000)
  setTimeout(() => {
    appStore.showInfo('测试信息', '这是一个信息通知')
  }, 3000)
}



const testTaskOperations = async () => {
  try {
    await taskStore.fetchTasks()
    appStore.showSuccess('任务数据已加载', `从API获取了${taskStore.tasks.length}个任务`)
  } catch (error) {
    appStore.showError('任务加载失败', '请检查后端服务是否正常运行')
  }
}

const testReviewOperations = async () => {
  try {
    await reviewStore.fetchTodayReviews()
    appStore.showSuccess('复习数据已加载', `从API获取了${reviewStore.todayReviews.total}个复习项目`)
  } catch (error) {
    appStore.showError('复习数据加载失败', '请检查后端服务是否正常运行')
  }
}

const testMindMapOperations = async () => {
  try {
    await mindmapStore.fetchMindMaps()
    appStore.showSuccess('思维导图数据已加载', `从API获取了${mindmapStore.mindMaps.length}个思维导图`)
  } catch (error) {
    appStore.showError('思维导图数据加载失败', '请检查后端服务是否正常运行')
  }
}

const testAllStores = () => {
  const results = []
  
  // 测试AppStore
  try {
    appStore.setTheme('dark')
    appStore.setTheme('light')
    results.push({ store: 'AppStore', success: true, message: '主题切换正常' })
  } catch (error) {
    results.push({ store: 'AppStore', success: false, message: `错误: ${error}` })
  }
  
  // 测试UserStore
  try {
    const hasUserMethods = typeof userStore.setUser === 'function'
    results.push({ store: 'UserStore', success: hasUserMethods, message: hasUserMethods ? '方法可用' : '方法缺失' })
  } catch (error) {
    results.push({ store: 'UserStore', success: false, message: `错误: ${error}` })
  }
  
  // 测试TaskStore
  try {
    const hasTaskMethods = typeof taskStore.setTasks === 'function'
    results.push({ store: 'TaskStore', success: hasTaskMethods, message: hasTaskMethods ? '方法可用' : '方法缺失' })
  } catch (error) {
    results.push({ store: 'TaskStore', success: false, message: `错误: ${error}` })
  }
  
  // 测试ReviewStore
  try {
    const hasReviewMethods = typeof reviewStore.setSchedules === 'function'
    results.push({ store: 'ReviewStore', success: hasReviewMethods, message: hasReviewMethods ? '方法可用' : '方法缺失' })
  } catch (error) {
    results.push({ store: 'ReviewStore', success: false, message: `错误: ${error}` })
  }
  
  // 测试MindMapStore
  try {
    const hasMindMapMethods = typeof mindmapStore.setMindMaps === 'function'
    results.push({ store: 'MindMapStore', success: hasMindMapMethods, message: hasMindMapMethods ? '方法可用' : '方法缺失' })
  } catch (error) {
    results.push({ store: 'MindMapStore', success: false, message: `错误: ${error}` })
  }
  
  testResults.value = results
}

const resetAllStores = () => {
  resetStores()
  appStore.showInfo('状态已重置', '所有Store状态已重置')
}

// 生命周期
onMounted(() => {
  initStatus.value = '已初始化'
  
  // 自动测试所有Store
  setTimeout(() => {
    testAllStores()
  }, 1000)
})
</script>

<style scoped>
.store-test-view {
  min-height: 100vh;
  background: #f5f5f5;
}

.card h3 {
  margin-bottom: 16px;
  color: #333;
  font-size: 16px;
}

.el-tag {
  margin: 2px;
}
</style>
