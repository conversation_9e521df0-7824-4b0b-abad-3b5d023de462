<template>
  <div class="component-test-view">
    <div class="page-container">
      <div class="page-header">
        <h1 class="page-title">组件功能测试</h1>
        <p>测试已完成的布局组件和业务组件</p>
      </div>

      <el-tabs v-model="activeTab" type="card">
        <!-- 任务组件测试 -->
        <el-tab-pane label="任务组件" name="task">
          <div class="test-section">
            <h3>TaskCard 组件测试</h3>
            <div class="component-demo">
              <el-row :gutter="20">
                <el-col :span="8" v-for="task in mockTasks" :key="task.id">
                  <TaskCard
                    :task="task"
                    :show-actions="true"
                    @edit="handleTaskEdit"
                    @delete="handleTaskDelete"
                    @status-change="handleTaskStatusChange"
                    @title-click="handleTaskTitleClick"
                  />
                </el-col>
              </el-row>
            </div>

            <h3 style="margin-top: 40px;">TaskList 组件测试</h3>
            <div class="component-demo">
              <TaskList
                :tasks="mockTasks"
                :loading="taskListLoading"
                title="测试任务列表"
                :show-header="true"
                :show-actions="true"
                :show-pagination="true"
                @edit="handleTaskEdit"
                @delete="handleTaskDelete"
                @status-change="handleTaskStatusChange"
                @title-click="handleTaskTitleClick"
                @create="handleTaskCreate"
              />
            </div>
          </div>
        </el-tab-pane>

        <!-- 复习组件测试 -->
        <el-tab-pane label="复习组件" name="review">
          <div class="test-section">
            <h3>ReviewCard 组件测试</h3>
            <div class="component-demo">
              <el-row :gutter="20">
                <el-col :span="12" v-for="(schedule, index) in mockReviewSchedules" :key="schedule.id">
                  <ReviewCard
                    :schedule="schedule"
                    :task="mockTasks[index]"
                    @complete="handleReviewComplete"
                    @skip="handleReviewSkip"
                    @adjust="handleReviewAdjust"
                    @reset="handleReviewReset"
                  />
                </el-col>
              </el-row>
            </div>
          </div>
        </el-tab-pane>

        <!-- 思维导图组件测试 -->
        <el-tab-pane label="思维导图组件" name="mindmap">
          <div class="test-section">
            <h3>MindMapCard 组件测试</h3>
            <div class="component-demo">
              <el-row :gutter="20">
                <el-col :span="8" v-for="mindMap in mockMindMaps" :key="mindMap.id">
                  <MindMapCard
                    :mind-map="mindMap"
                    :show-actions="true"
                    :show-author="true"
                    :is-favorite="mindMap.id === 'mindmap-1'"
                    author-name="测试用户"
                    @view="handleMindMapView"
                    @edit="handleMindMapEdit"
                    @duplicate="handleMindMapDuplicate"
                    @export="handleMindMapExport"
                    @share="handleMindMapShare"
                    @delete="handleMindMapDelete"
                    @favorite="handleMindMapFavorite"
                    @unfavorite="handleMindMapUnfavorite"
                    @title-click="handleMindMapTitleClick"
                  />
                </el-col>
              </el-row>
            </div>

            <h3 style="margin-top: 40px;">MindMapViewer 组件测试</h3>
            <div class="component-demo" style="height: 400px;">
              <MindMapViewer
                :mind-map="mockMindMaps[0]"
                :show-toolbar="true"
                :show-actions="true"
                :show-zoom-indicator="true"
                :is-favorite="true"
                @edit="handleMindMapEdit"
                @export="handleMindMapExport"
                @share="handleMindMapShare"
                @favorite="handleMindMapFavorite"
                @unfavorite="handleMindMapUnfavorite"
                @node-click="handleNodeClick"
              />
            </div>
          </div>
        </el-tab-pane>

        <!-- 通知系统测试 -->
        <el-tab-pane label="通知系统" name="notification">
          <div class="test-section">
            <h3>AppNotifications 组件测试</h3>
            <div class="component-demo">
              <el-space wrap>
                <el-button type="success" @click="testSuccessNotification">
                  测试成功通知
                </el-button>
                <el-button type="warning" @click="testWarningNotification">
                  测试警告通知
                </el-button>
                <el-button type="danger" @click="testErrorNotification">
                  测试错误通知
                </el-button>
                <el-button type="info" @click="testInfoNotification">
                  测试信息通知
                </el-button>
                <el-button @click="testMultipleNotifications">
                  测试多个通知
                </el-button>
                <el-button @click="appStore.clearAllNotifications()">
                  清除所有通知
                </el-button>
              </el-space>
              
              <div class="notification-info">
                <p><strong>当前通知数量:</strong> {{ appStore.notifications.length }}</p>
                <p><strong>未读通知数量:</strong> {{ appStore.unreadNotifications.length }}</p>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 主题测试 -->
        <el-tab-pane label="主题测试" name="theme">
          <div class="test-section">
            <h3>主题切换测试</h3>
            <div class="component-demo">
              <el-space wrap>
                <el-button @click="appStore.setTheme('light')">
                  浅色主题
                </el-button>
                <el-button @click="appStore.setTheme('dark')">
                  暗色主题
                </el-button>
                <el-button @click="appStore.toggleTheme()">
                  切换主题
                </el-button>
              </el-space>
              
              <div class="theme-info">
                <p><strong>当前主题:</strong> {{ appStore.theme }}</p>
                <p><strong>是否暗色:</strong> {{ appStore.isDark ? '是' : '否' }}</p>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 侧边栏测试 -->
        <el-tab-pane label="侧边栏测试" name="sidebar">
          <div class="test-section">
            <h3>侧边栏状态测试</h3>
            <div class="component-demo">
              <el-space wrap>
                <el-button @click="appStore.setSidebarCollapsed(false)">
                  展开侧边栏
                </el-button>
                <el-button @click="appStore.setSidebarCollapsed(true)">
                  收起侧边栏
                </el-button>
                <el-button @click="appStore.toggleSidebar()">
                  切换侧边栏
                </el-button>
              </el-space>
              
              <div class="sidebar-info">
                <p><strong>侧边栏状态:</strong> {{ appStore.sidebarCollapsed ? '收起' : '展开' }}</p>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 测试结果 -->
      <div class="test-results">
        <h3>测试操作日志</h3>
        <div class="log-container">
          <div v-for="(log, index) in testLogs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-action">{{ log.action }}</span>
            <span class="log-detail">{{ log.detail }}</span>
          </div>
        </div>
        <el-button @click="clearLogs" size="small">清除日志</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useAppStore } from '@/stores'
import { TaskCard, TaskList, ReviewCard, MindMapCard, MindMapViewer } from '@/components'
import type { LearningTask, ReviewSchedule, TaskStatus, PerformanceLevel, MindMap, MindMapNode } from '@/types'

const appStore = useAppStore()

// 响应式数据
const activeTab = ref('task')
const taskListLoading = ref(false)
const testLogs = ref<Array<{
  time: string
  action: string
  detail: string
}>>([])

// 模拟任务数据
const mockTasks = ref<LearningTask[]>([
  {
    id: 'task-1',
    title: '学习Vue 3 Composition API',
    description: '深入学习Vue 3的Composition API，包括响应式原理、生命周期钩子等核心概念。',
    content: 'Vue 3 Composition API学习内容...',
    type: 'reading',
    difficulty: 'intermediate',
    estimatedDuration: 120,
    tags: ['Vue', 'JavaScript', 'Frontend'],
    status: 'pending',
    priority: 'high',
    userId: 'user-1',
    dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
    attachments: [],
    metadata: {},
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'task-2',
    title: 'TypeScript高级类型练习',
    description: '练习TypeScript的高级类型，包括泛型、条件类型、映射类型等。',
    content: 'TypeScript高级类型练习内容...',
    type: 'practice',
    difficulty: 'advanced',
    estimatedDuration: 90,
    tags: ['TypeScript', 'Programming'],
    status: 'in_progress',
    priority: 'medium',
    userId: 'user-1',
    attachments: [],
    metadata: {},
    createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'task-3',
    title: '算法题：二叉树遍历',
    description: '掌握二叉树的前序、中序、后序遍历算法。',
    content: '二叉树遍历算法学习内容...',
    type: 'memorization',
    difficulty: 'beginner',
    estimatedDuration: 60,
    tags: ['Algorithm', 'DataStructure'],
    status: 'completed',
    priority: 'low',
    userId: 'user-1',
    completedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
    attachments: [],
    metadata: {},
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString()
  }
])

// 模拟复习计划数据
const mockReviewSchedules = ref<ReviewSchedule[]>([
  {
    id: 'review-1',
    taskId: 'task-1',
    userId: 'user-1',
    intervals: [
      { stage: 1, interval: 1, isCompleted: true, description: '第1次复习' },
      { stage: 2, interval: 2, isCompleted: true, description: '第2次复习' },
      { stage: 3, interval: 4, isCompleted: false, description: '第3次复习' }
    ],
    currentStage: 3,
    nextReviewDate: new Date().toISOString(),
    isCompleted: false,
    performance: [
      { stage: 1, performance: 'good', timeSpent: 25, date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString() },
      { stage: 2, performance: 'excellent', timeSpent: 20, date: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString() }
    ],
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'review-2',
    taskId: 'task-2',
    userId: 'user-1',
    intervals: [
      { stage: 1, interval: 1, isCompleted: true, description: '第1次复习' },
      { stage: 2, interval: 2, isCompleted: false, description: '第2次复习' }
    ],
    currentStage: 2,
    nextReviewDate: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 过期2小时
    isCompleted: false,
    performance: [
      { stage: 1, performance: 'fair', timeSpent: 35, date: new Date(Date.now() - 48 * 60 * 60 * 1000).toISOString() }
    ],
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString()
  }
])

// 模拟思维导图数据
const mockMindMaps = ref<MindMap[]>([
  {
    id: 'mindmap-1',
    title: '前端学习路线图',
    description: '完整的前端开发学习路径，包含HTML、CSS、JavaScript、Vue等技术栈。',
    userId: 'user-1',
    isPublic: true,
    tags: ['前端', '学习', 'JavaScript', 'Vue'],
    nodes: [
      {
        id: 'node-1',
        title: '前端基础',
        content: 'HTML、CSS、JavaScript基础知识',
        type: 'root',
        position: { x: 300, y: 200 },
        style: {
          backgroundColor: '#667eea',
          color: '#fff',
          borderColor: '#667eea',
          borderWidth: '2px',
          borderRadius: '8px'
        },
        tags: ['基础'],
        metadata: {}
      },
      {
        id: 'node-2',
        title: 'HTML',
        content: '超文本标记语言',
        type: 'branch',
        position: { x: 100, y: 100 },
        style: {
          backgroundColor: '#f8f9fa',
          color: '#333',
          borderColor: '#409EFF',
          borderWidth: '1px',
          borderRadius: '6px'
        },
        tags: ['标记语言'],
        metadata: {}
      },
      {
        id: 'node-3',
        title: 'CSS',
        content: '层叠样式表',
        type: 'branch',
        position: { x: 300, y: 100 },
        style: {
          backgroundColor: '#f8f9fa',
          color: '#333',
          borderColor: '#409EFF',
          borderWidth: '1px',
          borderRadius: '6px'
        },
        tags: ['样式'],
        metadata: {}
      },
      {
        id: 'node-4',
        title: 'JavaScript',
        content: '编程语言',
        type: 'branch',
        position: { x: 500, y: 100 },
        style: {
          backgroundColor: '#f8f9fa',
          color: '#333',
          borderColor: '#409EFF',
          borderWidth: '1px',
          borderRadius: '6px'
        },
        tags: ['编程语言'],
        metadata: {}
      }
    ],
    edges: [
      {
        id: 'edge-1',
        sourceId: 'node-1',
        targetId: 'node-2',
        type: 'curved',
        label: '基础',
        style: {
          color: '#409EFF',
          width: 2
        }
      },
      {
        id: 'edge-2',
        sourceId: 'node-1',
        targetId: 'node-3',
        type: 'curved',
        label: '样式',
        style: {
          color: '#409EFF',
          width: 2
        }
      },
      {
        id: 'edge-3',
        sourceId: 'node-1',
        targetId: 'node-4',
        type: 'curved',
        label: '逻辑',
        style: {
          color: '#409EFF',
          width: 2
        }
      }
    ],
    layout: {
      type: 'hierarchical',
      direction: 'top-bottom',
      spacing: { node: 50, rank: 100 }
    },
    style: {
      theme: 'default',
      backgroundColor: '#ffffff',
      gridVisible: true,
      snapToGrid: true
    },
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
  },
  {
    id: 'mindmap-2',
    title: '算法学习计划',
    description: '系统性的算法学习计划，涵盖数据结构和常见算法。',
    userId: 'user-1',
    isPublic: false,
    tags: ['算法', '数据结构'],
    nodes: [
      {
        id: 'node-5',
        title: '算法基础',
        content: '时间复杂度、空间复杂度',
        type: 'root',
        position: { x: 300, y: 200 },
        style: {
          backgroundColor: '#764ba2',
          color: '#fff',
          borderColor: '#764ba2',
          borderWidth: '2px',
          borderRadius: '8px'
        },
        tags: ['基础'],
        metadata: {}
      }
    ],
    edges: [],
    layout: {
      type: 'radial',
      direction: 'top-bottom',
      spacing: { node: 60, rank: 120 }
    },
    style: {
      theme: 'dark',
      backgroundColor: '#f8f9fa',
      gridVisible: false,
      snapToGrid: false
    },
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()
  }
])

// 方法
const addLog = (action: string, detail: string) => {
  testLogs.value.unshift({
    time: new Date().toLocaleTimeString(),
    action,
    detail
  })
}

const handleTaskEdit = (task: LearningTask) => {
  addLog('编辑任务', `任务: ${task.title}`)
  appStore.showInfo('编辑任务', `编辑任务: ${task.title}`)
}

const handleTaskDelete = (taskId: string) => {
  const task = mockTasks.value.find(t => t.id === taskId)
  addLog('删除任务', `任务ID: ${taskId}`)
  appStore.showWarning('删除任务', `删除任务: ${task?.title || taskId}`)
}

const handleTaskStatusChange = (taskId: string, status: TaskStatus) => {
  const task = mockTasks.value.find(t => t.id === taskId)
  if (task) {
    task.status = status
    addLog('状态变更', `${task.title} -> ${status}`)
    appStore.showSuccess('状态更新', `任务状态已更新为: ${status}`)
  }
}

const handleTaskTitleClick = (task: LearningTask) => {
  addLog('点击标题', `任务: ${task.title}`)
  appStore.showInfo('查看任务', `查看任务详情: ${task.title}`)
}

const handleTaskCreate = () => {
  addLog('创建任务', '点击创建新任务')
  appStore.showInfo('创建任务', '跳转到任务创建页面')
}

const handleReviewComplete = (scheduleId: string, performance: PerformanceLevel, timeSpent: number, notes?: string) => {
  const logDetail = notes
    ? `计划ID: ${scheduleId}, 表现: ${performance}, 用时: ${timeSpent}分钟, 笔记: ${notes}`
    : `计划ID: ${scheduleId}, 表现: ${performance}, 用时: ${timeSpent}分钟`
  addLog('完成复习', logDetail)
  appStore.showSuccess('复习完成', `表现: ${performance}, 用时: ${timeSpent}分钟`)
}

const handleReviewSkip = (scheduleId: string) => {
  addLog('跳过复习', `计划ID: ${scheduleId}`)
  appStore.showWarning('跳过复习', '复习已跳过')
}

const handleReviewAdjust = (scheduleId: string) => {
  addLog('调整间隔', `计划ID: ${scheduleId}`)
  appStore.showInfo('调整间隔', '调整复习间隔')
}

const handleReviewReset = (scheduleId: string) => {
  addLog('重置计划', `计划ID: ${scheduleId}`)
  appStore.showWarning('重置计划', '复习计划已重置')
}

const testSuccessNotification = () => {
  appStore.showSuccess('成功通知', '这是一个成功通知的示例')
  addLog('通知测试', '发送成功通知')
}

const testWarningNotification = () => {
  appStore.showWarning('警告通知', '这是一个警告通知的示例')
  addLog('通知测试', '发送警告通知')
}

const testErrorNotification = () => {
  appStore.showError('错误通知', '这是一个错误通知的示例')
  addLog('通知测试', '发送错误通知')
}

const testInfoNotification = () => {
  appStore.showInfo('信息通知', '这是一个信息通知的示例')
  addLog('通知测试', '发送信息通知')
}

const testMultipleNotifications = () => {
  appStore.showSuccess('通知1', '第一个通知')
  setTimeout(() => appStore.showWarning('通知2', '第二个通知'), 500)
  setTimeout(() => appStore.showError('通知3', '第三个通知'), 1000)
  setTimeout(() => appStore.showInfo('通知4', '第四个通知'), 1500)
  addLog('通知测试', '发送多个通知')
}

const clearLogs = () => {
  testLogs.value = []
  appStore.showInfo('日志清除', '测试日志已清除')
}

// 思维导图相关方法
const handleMindMapView = (mindMap: MindMap) => {
  addLog('查看思维导图', `思维导图: ${mindMap.title}`)
  appStore.showInfo('查看思维导图', `查看思维导图: ${mindMap.title}`)
}

const handleMindMapEdit = (mindMap: MindMap) => {
  addLog('编辑思维导图', `思维导图: ${mindMap.title}`)
  appStore.showInfo('编辑思维导图', `编辑思维导图: ${mindMap.title}`)
}

const handleMindMapDuplicate = (mindMap: MindMap) => {
  addLog('复制思维导图', `思维导图: ${mindMap.title}`)
  appStore.showSuccess('复制成功', `已复制思维导图: ${mindMap.title}`)
}

const handleMindMapExport = (mindMap: MindMap) => {
  addLog('导出思维导图', `思维导图: ${mindMap.title}`)
  appStore.showSuccess('导出成功', `已导出思维导图: ${mindMap.title}`)
}

const handleMindMapShare = (mindMap: MindMap) => {
  addLog('分享思维导图', `思维导图: ${mindMap.title}`)
  appStore.showInfo('分享思维导图', `分享思维导图: ${mindMap.title}`)
}

const handleMindMapDelete = (mindMapId: string) => {
  const mindMap = mockMindMaps.value.find(m => m.id === mindMapId)
  addLog('删除思维导图', `思维导图ID: ${mindMapId}`)
  appStore.showWarning('删除思维导图', `删除思维导图: ${mindMap?.title || mindMapId}`)
}

const handleMindMapFavorite = (mindMapId: string) => {
  const mindMap = mockMindMaps.value.find(m => m.id === mindMapId)
  addLog('收藏思维导图', `思维导图ID: ${mindMapId}`)
  appStore.showSuccess('已收藏', `已收藏思维导图: ${mindMap?.title || mindMapId}`)
}

const handleMindMapUnfavorite = (mindMapId: string) => {
  const mindMap = mockMindMaps.value.find(m => m.id === mindMapId)
  addLog('取消收藏思维导图', `思维导图ID: ${mindMapId}`)
  appStore.showInfo('已取消收藏', `已取消收藏思维导图: ${mindMap?.title || mindMapId}`)
}

const handleMindMapTitleClick = (mindMap: MindMap) => {
  addLog('点击思维导图标题', `思维导图: ${mindMap.title}`)
  appStore.showInfo('查看详情', `查看思维导图详情: ${mindMap.title}`)
}

const handleNodeClick = (node: MindMapNode) => {
  addLog('点击思维导图节点', `节点: ${node.title}`)
  appStore.showInfo('节点详情', `查看节点详情: ${node.title}`)
}

// 生命周期
onMounted(() => {
  addLog('页面加载', '组件测试页面已加载')
})
</script>

<style scoped>
.component-test-view {
  min-height: 100vh;
  background: #f5f5f5;
}

.test-section {
  padding: 20px 0;
}

.test-section h3 {
  margin-bottom: 20px;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.component-demo {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.notification-info,
.theme-info,
.sidebar-info {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.notification-info p,
.theme-info p,
.sidebar-info p {
  margin: 8px 0;
  font-size: 14px;
}

.test-results {
  margin-top: 40px;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-results h3 {
  margin-bottom: 16px;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  margin-bottom: 16px;
}

.log-item {
  display: flex;
  gap: 12px;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 13px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #999;
  min-width: 80px;
}

.log-action {
  color: #409EFF;
  font-weight: 600;
  min-width: 80px;
}

.log-detail {
  color: #666;
  flex: 1;
}

/* 暗色主题 */
.dark-theme .test-section h3 {
  color: #fff;
}

.dark-theme .component-demo {
  background: #2d2d2d;
}

.dark-theme .notification-info,
.dark-theme .theme-info,
.dark-theme .sidebar-info {
  background: #404040;
}

.dark-theme .notification-info p,
.dark-theme .theme-info p,
.dark-theme .sidebar-info p {
  color: #ccc;
}

.dark-theme .test-results {
  background: #2d2d2d;
}

.dark-theme .test-results h3 {
  color: #fff;
}

.dark-theme .log-container {
  border-color: #404040;
  background: #1f1f1f;
}

.dark-theme .log-item {
  border-bottom-color: #333;
}

.dark-theme .log-time {
  color: #666;
}

.dark-theme .log-detail {
  color: #999;
}
</style>
