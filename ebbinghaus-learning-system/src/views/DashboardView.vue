<template>
  <div class="dashboard-view">
    <AppLayout>
      <div class="dashboard-container">
        <!-- 欢迎区域 -->
        <div class="welcome-section">
          <div class="welcome-content">
            <h1 class="welcome-title">
              {{ getGreeting() }}，{{ userStore.userName || '学习者' }}！
            </h1>
            <p class="welcome-subtitle">
              今天是学习的好日子，让我们继续你的学习之旅吧
            </p>
          </div>
          <div class="welcome-stats">
            <div class="stat-card">
              <div class="stat-number">{{ todayStats.studyTime }}</div>
              <div class="stat-label">今日学习时长(分钟)</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ todayStats.completedTasks }}</div>
              <div class="stat-label">今日完成任务</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ todayStats.reviewsCompleted }}</div>
              <div class="stat-label">今日复习完成</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{ todayStats.streakDays }}</div>
              <div class="stat-label">连续学习天数</div>
            </div>
          </div>
        </div>

        <!-- 主要内容区域 -->
        <el-row :gutter="20" class="main-content">
          <!-- 左侧列 -->
          <el-col :span="16">
            <!-- 今日任务 -->
            <div class="section-card">
              <div class="section-header">
                <h2 class="section-title">
                  <el-icon><Calendar /></el-icon>
                  今日任务
                </h2>
                <el-button type="primary" size="small" @click="$router.push('/tasks/create')">
                  <el-icon><Plus /></el-icon>
                  新建任务
                </el-button>
              </div>
              <div class="section-content">
                <TaskList
                  :tasks="todayTasks"
                  :loading="taskStore.isLoading"
                  :show-header="false"
                  :show-pagination="false"
                  :compact="true"
                  @edit="handleTaskEdit"
                  @delete="handleTaskDelete"
                  @status-change="handleTaskStatusChange"
                  @title-click="handleTaskTitleClick"
                />
              </div>
            </div>

            <!-- 今日复习 -->
            <div class="section-card">
              <div class="section-header">
                <h2 class="section-title">
                  <el-icon><Refresh /></el-icon>
                  今日复习
                </h2>
                <el-button type="success" size="small" @click="startReviewSession">
                  <el-icon><VideoPlay /></el-icon>
                  开始复习
                </el-button>
              </div>
              <div class="section-content">
                <el-row :gutter="16" v-if="todayReviews.length > 0">
                  <el-col :span="12" v-for="review in todayReviews.slice(0, 4)" :key="review.id">
                    <ReviewCard
                      v-if="getTaskById(review.taskId)"
                      :schedule="review"
                      :task="getTaskById(review.taskId)!"
                      @complete="handleReviewComplete"
                      @skip="handleReviewSkip"
                      @adjust="handleReviewAdjust"
                      @reset="handleReviewReset"
                    />
                  </el-col>
                </el-row>
                <el-empty v-else description="今日暂无复习任务" />
              </div>
            </div>
          </el-col>

          <!-- 右侧列 -->
          <el-col :span="8">
            <!-- 学习进度 -->
            <div class="section-card">
              <div class="section-header">
                <h2 class="section-title">
                  <el-icon><TrendCharts /></el-icon>
                  学习进度
                </h2>
              </div>
              <div class="section-content">
                <div class="progress-item">
                  <div class="progress-label">
                    <span>今日目标</span>
                    <span>{{ todayStats.studyTime }}/{{ dailyGoal }}分钟</span>
                  </div>
                  <el-progress 
                    :percentage="Math.min(100, (todayStats.studyTime / dailyGoal) * 100)" 
                    :stroke-width="8"
                    :show-text="false"
                  />
                </div>
                <div class="progress-item">
                  <div class="progress-label">
                    <span>本周任务</span>
                    <span>{{ weekStats.completedTasks }}/{{ weekStats.totalTasks }}</span>
                  </div>
                  <el-progress 
                    :percentage="weekStats.totalTasks > 0 ? (weekStats.completedTasks / weekStats.totalTasks) * 100 : 0" 
                    :stroke-width="8"
                    :show-text="false"
                    color="#67C23A"
                  />
                </div>
                <div class="progress-item">
                  <div class="progress-label">
                    <span>复习完成率</span>
                    <span>{{ weekStats.reviewCompletionRate }}%</span>
                  </div>
                  <el-progress 
                    :percentage="weekStats.reviewCompletionRate" 
                    :stroke-width="8"
                    :show-text="false"
                    color="#E6A23C"
                  />
                </div>
              </div>
            </div>

            <!-- 最近思维导图 -->
            <div class="section-card">
              <div class="section-header">
                <h2 class="section-title">
                  <el-icon><Connection /></el-icon>
                  最近思维导图
                </h2>
                <el-button type="info" size="small" @click="$router.push('/mindmaps')">
                  查看全部
                </el-button>
              </div>
              <div class="section-content">
                <div class="mindmap-list">
                  <div 
                    v-for="mindmap in recentMindMaps" 
                    :key="mindmap.id"
                    class="mindmap-item"
                    @click="$router.push(`/mindmaps/${mindmap.id}`)"
                  >
                    <div class="mindmap-icon">
                      <el-icon><Grid /></el-icon>
                    </div>
                    <div class="mindmap-info">
                      <div class="mindmap-title">{{ mindmap.title }}</div>
                      <div class="mindmap-meta">
                        {{ mindmap.nodes.length }}个节点 · {{ dateUtils.fromNow(mindmap.updatedAt) }}
                      </div>
                    </div>
                  </div>
                </div>
                <el-empty v-if="recentMindMaps.length === 0" description="暂无思维导图" />
              </div>
            </div>

            <!-- 快速操作 -->
            <div class="section-card">
              <div class="section-header">
                <h2 class="section-title">
                  <el-icon><Lightning /></el-icon>
                  快速操作
                </h2>
              </div>
              <div class="section-content">
                <div class="quick-actions">
                  <el-button 
                    type="primary" 
                    class="quick-action-btn"
                    @click="$router.push('/tasks/create')"
                  >
                    <el-icon><Document /></el-icon>
                    创建任务
                  </el-button>
                  <el-button 
                    type="success" 
                    class="quick-action-btn"
                    @click="startReviewSession"
                  >
                    <el-icon><Refresh /></el-icon>
                    开始复习
                  </el-button>
                  <el-button 
                    type="info" 
                    class="quick-action-btn"
                    @click="$router.push('/mindmaps/create')"
                  >
                    <el-icon><Connection /></el-icon>
                    新建导图
                  </el-button>
                  <el-button 
                    type="warning" 
                    class="quick-action-btn"
                    @click="$router.push('/analytics')"
                  >
                    <el-icon><TrendCharts /></el-icon>
                    查看分析
                  </el-button>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </AppLayout>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore, useUserStore, useTaskStore, useReviewStore, useMindMapStore } from '@/stores'
import { AppLayout, TaskList, ReviewCard } from '@/components'
import { dateUtils } from '@/utils'
import type { LearningTask, TaskStatus, PerformanceLevel } from '@/types'
import {
  Calendar,
  Plus,
  Refresh,
  VideoPlay,
  TrendCharts,
  Connection,
  Lightning,
  Document,
  Grid
} from '@element-plus/icons-vue'

const router = useRouter()
const appStore = useAppStore()
const userStore = useUserStore()
const taskStore = useTaskStore()
const reviewStore = useReviewStore()
const mindmapStore = useMindMapStore()

// 响应式数据
const dailyGoal = ref(120) // 每日学习目标（分钟）
const todayStats = ref({
  studyTime: 85,
  completedTasks: 3,
  reviewsCompleted: 5,
  streakDays: 7
})
const weekStats = ref({
  completedTasks: 12,
  totalTasks: 18,
  reviewCompletionRate: 78
})

// 计算属性
const todayTasks = computed(() => taskStore.todayTasks.scheduled.slice(0, 6))
const todayReviews = computed(() => reviewStore.todayReviews.pending.slice(0, 4))
const recentMindMaps = computed(() => mindmapStore.recentMindMaps.slice(0, 3))

// 方法
const getGreeting = () => {
  const hour = new Date().getHours()
  if (hour < 12) return '早上好'
  if (hour < 18) return '下午好'
  return '晚上好'
}

const getTaskById = (taskId: string): LearningTask | undefined => {
  return taskStore.tasks.find(task => task.id === taskId)
}

const handleTaskEdit = (task: LearningTask) => {
  router.push(`/tasks/${task.id}/edit`)
}

const handleTaskDelete = (taskId: string) => {
  taskStore.deleteTask(taskId)
}

const handleTaskStatusChange = (taskId: string, status: TaskStatus) => {
  taskStore.updateTaskStatus(taskId, status)
}

const handleTaskTitleClick = (task: LearningTask) => {
  router.push(`/tasks/${task.id}`)
}

const handleReviewComplete = (scheduleId: string, performance: PerformanceLevel, timeSpent: number, notes?: string) => {
  reviewStore.completeReviewStage(scheduleId, { performance, timeSpent, notes })
}

const handleReviewSkip = (scheduleId: string) => {
  reviewStore.skipReview(scheduleId)
}

const handleReviewAdjust = (scheduleId: string) => {
  // 打开调整间隔对话框
  appStore.showInfo('调整间隔', `调整复习间隔功能 - ${scheduleId}`)
}

const handleReviewReset = (scheduleId: string) => {
  // 重置复习计划
  appStore.showWarning('重置计划', `重置复习计划功能 - ${scheduleId}`)
}

const startReviewSession = () => {
  if (todayReviews.value.length > 0) {
    router.push('/reviews/session')
  } else {
    appStore.showInfo('暂无复习', '今日暂无待复习任务')
  }
}

// 生命周期
onMounted(async () => {
  // 加载今日数据
  try {
    await Promise.all([
      taskStore.fetchTodayTasks(),
      reviewStore.fetchTodayReviews(),
      mindmapStore.fetchRecentMindMaps()
    ])
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
  }
})
</script>

<style scoped>
.dashboard-view {
  min-height: 100vh;
}

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.welcome-section {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px;
  border-radius: 12px;
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.welcome-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
}

.welcome-stats {
  display: flex;
  gap: 24px;
}

.stat-card {
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  opacity: 0.8;
}

.main-content {
  margin-top: 24px;
}

.section-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-content {
  /* 内容区域样式 */
}

.progress-item {
  margin-bottom: 20px;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.mindmap-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.mindmap-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.mindmap-item:hover {
  background-color: #f5f5f5;
}

.mindmap-icon {
  width: 40px;
  height: 40px;
  background: #409EFF;
  color: white;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mindmap-info {
  flex: 1;
}

.mindmap-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.mindmap-meta {
  font-size: 12px;
  color: #999;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.quick-action-btn {
  width: 100%;
  height: 48px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

/* 暗色主题 */
.dark-theme .section-card {
  background: #2d2d2d;
}

.dark-theme .section-title {
  color: #fff;
}

.dark-theme .progress-label {
  color: #999;
}

.dark-theme .mindmap-item:hover {
  background-color: #404040;
}

.dark-theme .mindmap-title {
  color: #fff;
}

.dark-theme .mindmap-meta {
  color: #666;
}
</style>
