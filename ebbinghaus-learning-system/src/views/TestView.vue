<template>
  <div class="test-view">
    <div class="page-container">
      <div class="page-header">
        <h1 class="page-title">阶段2功能测试</h1>
        <p>测试类型定义、工具函数和常量配置</p>
      </div>

      <el-row :gutter="20">
        <!-- 日期工具测试 -->
        <el-col :span="12">
          <div class="card">
            <h3>📅 日期工具测试</h3>
            <el-space direction="vertical" style="width: 100%">
              <div>
                <strong>当前时间:</strong> {{ currentTime }}
              </div>
              <div>
                <strong>格式化时间:</strong> {{ formattedTime }}
              </div>
              <div>
                <strong>相对时间:</strong> {{ relativeTime }}
              </div>
              <div>
                <strong>持续时间:</strong> {{ durationText }}
              </div>
            </el-space>
          </div>
        </el-col>

        <!-- 验证工具测试 -->
        <el-col :span="12">
          <div class="card">
            <h3>✅ 验证工具测试</h3>
            <el-space direction="vertical" style="width: 100%">
              <div>
                <el-input 
                  v-model="testEmail" 
                  placeholder="输入邮箱测试"
                  @input="validateEmail"
                />
                <span :style="{ color: emailValid ? '#67C23A' : '#F56C6C' }">
                  {{ emailValid ? '✓ 邮箱格式正确' : '✗ 邮箱格式错误' }}
                </span>
              </div>
              <div>
                <el-input 
                  v-model="testPassword" 
                  type="password"
                  placeholder="输入密码测试"
                  @input="validatePassword"
                />
                <span :style="{ color: passwordResult.valid ? '#67C23A' : '#F56C6C' }">
                  {{ passwordResult.message }}
                </span>
              </div>
            </el-space>
          </div>
        </el-col>

        <!-- 格式化工具测试 -->
        <el-col :span="12">
          <div class="card">
            <h3>🔧 格式化工具测试</h3>
            <el-space direction="vertical" style="width: 100%">
              <div>
                <strong>文件大小:</strong> {{ formatters.fileSize(1024 * 1024 * 5.5) }}
              </div>
              <div>
                <strong>数字格式:</strong> {{ formatters.number(1234567) }}
              </div>
              <div>
                <strong>百分比:</strong> {{ formatters.percentage(75, 100) }}
              </div>
              <div>
                <strong>文本截断:</strong> {{ formatters.truncate('这是一个很长的文本内容，需要被截断显示', 10) }}
              </div>
            </el-space>
          </div>
        </el-col>

        <!-- 艾宾浩斯工具测试 -->
        <el-col :span="12">
          <div class="card">
            <h3>🧠 艾宾浩斯工具测试</h3>
            <el-space direction="vertical" style="width: 100%">
              <div>
                <strong>标准间隔:</strong> {{ ebbinghausUtils.getStandardIntervals().join(', ') }} 天
              </div>
              <div>
                <strong>调整间隔 (good):</strong> {{ ebbinghausUtils.adjustInterval(7, 'good') }} 天
              </div>
              <div>
                <strong>记忆保持率 (excellent):</strong> {{ (ebbinghausUtils.calculateRetentionRate('excellent') * 100).toFixed(0) }}%
              </div>
            </el-space>
          </div>
        </el-col>

        <!-- 样式工具测试 -->
        <el-col :span="12">
          <div class="card">
            <h3>🎨 样式工具测试</h3>
            <el-space direction="vertical" style="width: 100%">
              <div v-for="status in Object.keys(TASK_CONFIG.STATUSES)" :key="status">
                <el-tag :color="styleUtils.getStatusColor(status as TaskStatus)">
                  {{ TASK_CONFIG.STATUSES[status as TaskStatus].label }}
                </el-tag>
              </div>
            </el-space>
          </div>
        </el-col>

        <!-- 常量配置测试 -->
        <el-col :span="12">
          <div class="card">
            <h3>⚙️ 常量配置测试</h3>
            <el-space direction="vertical" style="width: 100%">
              <div>
                <strong>应用名称:</strong> {{ APP_CONFIG.NAME }}
              </div>
              <div>
                <strong>版本:</strong> {{ APP_CONFIG.VERSION }}
              </div>
              <div>
                <strong>默认分页大小:</strong> {{ PAGINATION_CONFIG.DEFAULT_PAGE_SIZE }}
              </div>
              <div>
                <strong>最大文件大小:</strong> {{ formatters.fileSize(UPLOAD_CONFIG.MAX_FILE_SIZE) }}
              </div>
            </el-space>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { dateUtils, validators, formatters, ebbinghausUtils, styleUtils } from '@/utils'
import { APP_CONFIG, PAGINATION_CONFIG, UPLOAD_CONFIG, TASK_CONFIG } from '@/constants'
import type { TaskStatus } from '@/types'

// 响应式数据
const testEmail = ref('<EMAIL>')
const testPassword = ref('Test123456')
const currentTime = ref('')

// 计算属性
const formattedTime = computed(() => {
  return dateUtils.format(new Date(), 'YYYY年MM月DD日 HH:mm:ss')
})

const relativeTime = computed(() => {
  const yesterday = new Date()
  yesterday.setDate(yesterday.getDate() - 1)
  return dateUtils.fromNow(yesterday)
})

const durationText = computed(() => {
  return dateUtils.formatDuration(125) // 125分钟
})

const emailValid = computed(() => {
  return validators.email(testEmail.value)
})

const passwordResult = computed(() => {
  return validators.password(testPassword.value)
})

// 方法
const validateEmail = () => {
  // 触发计算属性更新
}

const validatePassword = () => {
  // 触发计算属性更新
}

const updateCurrentTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  updateCurrentTime()
  setInterval(updateCurrentTime, 1000)
})
</script>

<style scoped>
.test-view {
  min-height: 100vh;
  background: #f5f5f5;
}

.card h3 {
  margin-bottom: 16px;
  color: #333;
  font-size: 16px;
}

.el-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}
</style>
