<template>
  <div class="reviews-view">
    <AppLayout>
      <div class="reviews-container">
        <!-- 页面头部 -->
        <div class="page-header">
          <div class="header-left">
            <h1 class="page-title">复习管理</h1>
            <p class="page-subtitle">基于艾宾浩斯遗忘曲线的智能复习系统</p>
          </div>
          <div class="header-right">
            <el-button type="primary" @click="startReviewSession" :disabled="todayPendingCount === 0">
              <el-icon><VideoPlay /></el-icon>
              开始复习会话
            </el-button>
          </div>
        </div>

        <!-- 统计卡片 -->
        <el-row :gutter="20" class="stats-section">
          <el-col :span="6">
            <div class="stat-card pending">
              <div class="stat-icon">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ todayPendingCount }}</div>
                <div class="stat-label">今日待复习</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card completed">
              <div class="stat-icon">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ todayCompletedCount }}</div>
                <div class="stat-label">今日已完成</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card overdue">
              <div class="stat-icon">
                <el-icon><Warning /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ todayOverdueCount }}</div>
                <div class="stat-label">今日过期</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card total">
              <div class="stat-icon">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ reviewStore.scheduleCount }}</div>
                <div class="stat-label">总复习计划</div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 复习标签页 -->
        <div class="reviews-section">
          <el-tabs v-model="activeTab" type="card" class="review-tabs">
            <el-tab-pane label="今日复习" name="today">
              <div class="tab-content">
                <div class="section-header">
                  <h3>今日复习任务</h3>
                  <div class="header-actions">
                    <el-select v-model="todayFilter" placeholder="筛选" size="small" style="width: 120px;">
                      <el-option label="全部" value="all" />
                      <el-option label="待复习" value="pending" />
                      <el-option label="已完成" value="completed" />
                      <el-option label="已过期" value="overdue" />
                    </el-select>
                  </div>
                </div>
                
                <el-row :gutter="16" v-if="filteredTodayReviews.length > 0">
                  <el-col :span="12" v-for="review in filteredTodayReviews" :key="review.id">
                    <ReviewCard
                      v-if="getTaskById(review.taskId)"
                      :schedule="review"
                      :task="getTaskById(review.taskId)!"
                      @complete="handleReviewComplete"
                      @skip="handleReviewSkip"
                      @adjust="handleReviewAdjust"
                      @reset="handleReviewReset"
                    />
                  </el-col>
                </el-row>
                <el-empty v-else description="暂无复习任务" />
              </div>
            </el-tab-pane>

            <el-tab-pane label="复习计划" name="schedules">
              <div class="tab-content">
                <div class="section-header">
                  <h3>所有复习计划</h3>
                  <div class="header-actions">
                    <el-input
                      v-model="searchQuery"
                      placeholder="搜索任务..."
                      size="small"
                      style="width: 200px; margin-right: 12px;"
                      clearable
                    >
                      <template #prefix>
                        <el-icon><Search /></el-icon>
                      </template>
                    </el-input>
                    <el-select v-model="statusFilter" placeholder="状态" size="small" style="width: 120px;">
                      <el-option label="全部" value="all" />
                      <el-option label="进行中" value="active" />
                      <el-option label="已完成" value="completed" />
                    </el-select>
                  </div>
                </div>

                <div class="schedules-list">
                  <el-table :data="filteredSchedules" stripe>
                    <el-table-column prop="taskTitle" label="任务名称" min-width="200">
                      <template #default="{ row }">
                        <div class="task-cell">
                          <div class="task-title">{{ getTaskById(row.taskId)?.title || '未知任务' }}</div>
                          <div class="task-meta">
                            第{{ row.currentStage }}次复习 · {{ getCompletedStages(row) }}/{{ getTotalStages(row) }}
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="status" label="状态" width="100">
                      <template #default="{ row }">
                        <el-tag :type="row.isCompleted ? 'success' : 'warning'" size="small">
                          {{ row.isCompleted ? '已完成' : '进行中' }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="nextReviewDate" label="下次复习" width="150">
                      <template #default="{ row }">
                        <span :class="{ 'overdue': dateUtils.isOverdue(row.nextReviewDate) }">
                          {{ formatNextReview(row.nextReviewDate) }}
                        </span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="performance" label="最近表现" width="100">
                      <template #default="{ row }">
                        <el-tag 
                          v-if="getLastPerformance(row)"
                          :type="getPerformanceTagType(getLastPerformance(row))"
                          size="small"
                        >
                          {{ getPerformanceLabel(getLastPerformance(row)) }}
                        </el-tag>
                        <span v-else class="no-performance">暂无</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="createdAt" label="创建时间" width="120">
                      <template #default="{ row }">
                        {{ dateUtils.format(row.createdAt, 'MM-DD') }}
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="150">
                      <template #default="{ row }">
                        <el-button 
                          type="text" 
                          size="small" 
                          @click="startSingleReview(row)"
                          :disabled="row.isCompleted"
                        >
                          复习
                        </el-button>
                        <el-button type="text" size="small" @click="viewScheduleDetail(row)">
                          详情
                        </el-button>
                        <el-button 
                          type="text" 
                          size="small" 
                          @click="deleteSchedule(row.id)"
                          class="danger-btn"
                        >
                          删除
                        </el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-tab-pane>

            <el-tab-pane label="复习统计" name="statistics">
              <div class="tab-content">
                <div class="statistics-content">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <div class="chart-card">
                        <h4>本周复习完成情况</h4>
                        <div class="chart-placeholder">
                          <el-icon size="48"><TrendCharts /></el-icon>
                          <p>图表功能开发中...</p>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="12">
                      <div class="chart-card">
                        <h4>复习表现分布</h4>
                        <div class="chart-placeholder">
                          <el-icon size="48"><PieChart /></el-icon>
                          <p>图表功能开发中...</p>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                  
                  <div class="stats-summary">
                    <h4>复习统计摘要</h4>
                    <el-descriptions :column="2" border>
                      <el-descriptions-item label="总复习次数">{{ totalReviewCount }}</el-descriptions-item>
                      <el-descriptions-item label="平均复习时长">{{ averageReviewTime }}分钟</el-descriptions-item>
                      <el-descriptions-item label="复习完成率">{{ reviewCompletionRate }}%</el-descriptions-item>
                      <el-descriptions-item label="连续复习天数">{{ streakDays }}天</el-descriptions-item>
                    </el-descriptions>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </AppLayout>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useReviewStore, useTaskStore, useAppStore } from '@/stores'
import { AppLayout, ReviewCard } from '@/components'
import { dateUtils } from '@/utils'
import type { ReviewSchedule, LearningTask, PerformanceLevel } from '@/types'
import {
  VideoPlay,
  Clock,
  Check,
  Warning,
  Document,
  Search,
  TrendCharts,
  PieChart
} from '@element-plus/icons-vue'

const router = useRouter()
const reviewStore = useReviewStore()
const taskStore = useTaskStore()
const appStore = useAppStore()

// 响应式数据
const activeTab = ref('today')
const todayFilter = ref('all')
const statusFilter = ref('all')
const searchQuery = ref('')

// 模拟统计数据
const totalReviewCount = ref(156)
const averageReviewTime = ref(28)
const reviewCompletionRate = ref(85)
const streakDays = ref(12)

// 计算属性
const todayPendingCount = computed(() => reviewStore.todayReviews.pending.length)
const todayCompletedCount = computed(() => reviewStore.todayReviews.completed.length)
const todayOverdueCount = computed(() => reviewStore.todayReviews.overdue.length)

const allTodayReviews = computed(() => [
  ...reviewStore.todayReviews.pending,
  ...reviewStore.todayReviews.completed,
  ...reviewStore.todayReviews.overdue
])

const filteredTodayReviews = computed(() => {
  if (todayFilter.value === 'all') return allTodayReviews.value
  if (todayFilter.value === 'pending') return reviewStore.todayReviews.pending
  if (todayFilter.value === 'completed') return reviewStore.todayReviews.completed
  if (todayFilter.value === 'overdue') return reviewStore.todayReviews.overdue
  return []
})

const filteredSchedules = computed(() => {
  let schedules = [...reviewStore.schedules]
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    schedules = schedules.filter(schedule => {
      const task = getTaskById(schedule.taskId)
      return task?.title.toLowerCase().includes(query) || 
             task?.description.toLowerCase().includes(query)
    })
  }
  
  // 状态过滤
  if (statusFilter.value === 'active') {
    schedules = schedules.filter(schedule => !schedule.isCompleted)
  } else if (statusFilter.value === 'completed') {
    schedules = schedules.filter(schedule => schedule.isCompleted)
  }
  
  return schedules
})

// 方法
const getTaskById = (taskId: string): LearningTask | undefined => {
  return taskStore.tasks.find(task => task.id === taskId)
}

const getCompletedStages = (schedule: ReviewSchedule) => {
  return schedule.intervals.filter(interval => interval.isCompleted).length
}

const getTotalStages = (schedule: ReviewSchedule) => {
  return schedule.intervals.length
}

const formatNextReview = (date: string) => {
  if (dateUtils.isToday(date)) return '今天'
  if (dateUtils.isOverdue(date)) return `过期 ${dateUtils.fromNow(date)}`
  return dateUtils.fromNow(date)
}

const getLastPerformance = (schedule: ReviewSchedule) => {
  return schedule.performance[schedule.performance.length - 1]?.performance
}

const getPerformanceLabel = (performance: PerformanceLevel) => {
  const labels = {
    poor: '差',
    fair: '一般',
    good: '良好',
    excellent: '优秀'
  }
  return labels[performance]
}

const getPerformanceTagType = (performance: PerformanceLevel) => {
  const types = {
    poor: 'danger',
    fair: 'warning',
    good: 'primary',
    excellent: 'success'
  }
  return types[performance]
}

const startReviewSession = () => {
  if (todayPendingCount.value > 0) {
    router.push('/reviews/session')
  } else {
    appStore.showInfo('暂无复习', '今日暂无待复习任务')
  }
}

const startSingleReview = (schedule: ReviewSchedule) => {
  router.push(`/reviews/${schedule.id}`)
}

const viewScheduleDetail = (schedule: ReviewSchedule) => {
  router.push(`/reviews/${schedule.id}/detail`)
}

const deleteSchedule = async (scheduleId: string) => {
  try {
    // TODO: 实现deleteSchedule方法
    // await reviewStore.deleteSchedule(scheduleId)
    appStore.showSuccess('删除成功', `复习计划已删除 - ${scheduleId}`)
  } catch (error) {
    appStore.showError('删除失败', '删除复习计划时发生错误')
  }
}

const handleReviewComplete = (scheduleId: string, performance: PerformanceLevel, timeSpent: number, notes?: string) => {
  reviewStore.completeReviewStage(scheduleId, { performance, timeSpent, notes })
}

const handleReviewSkip = (scheduleId: string) => {
  reviewStore.skipReview(scheduleId)
}

const handleReviewAdjust = (scheduleId: string) => {
  appStore.showInfo('调整间隔', `调整复习间隔功能 - ${scheduleId}`)
}

const handleReviewReset = (scheduleId: string) => {
  appStore.showWarning('重置计划', `重置复习计划功能 - ${scheduleId}`)
}

// 生命周期
onMounted(async () => {
  try {
    await Promise.all([
      reviewStore.fetchTodayReviews(),
      reviewStore.fetchSchedules(),
      taskStore.fetchTasks()
    ])
  } catch (error) {
    appStore.showError('加载失败', '加载复习数据时发生错误')
  }
})
</script>

<style scoped>
.reviews-view {
  min-height: 100vh;
}

.reviews-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.header-right {
  flex-shrink: 0;
  margin-left: 20px;
}

.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-card.pending .stat-icon {
  background: #E6A23C;
}

.stat-card.completed .stat-icon {
  background: #67C23A;
}

.stat-card.overdue .stat-icon {
  background: #F56C6C;
}

.stat-card.total .stat-icon {
  background: #909399;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.reviews-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.review-tabs {
  margin: -20px -20px 0 -20px;
}

.tab-content {
  padding: 20px 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.schedules-list {
  margin-top: 20px;
}

.task-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.task-title {
  font-weight: 500;
  color: #333;
}

.task-meta {
  font-size: 12px;
  color: #999;
}

.overdue {
  color: #F56C6C;
}

.no-performance {
  color: #999;
  font-size: 12px;
}

.danger-btn {
  color: #F56C6C;
}

.danger-btn:hover {
  color: #F56C6C;
  background: #FEF0F0;
}

.statistics-content {
  padding: 20px 0;
}

.chart-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.chart-card h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.chart-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
}

.chart-placeholder p {
  margin: 8px 0 0 0;
  font-size: 14px;
}

.stats-summary {
  margin-top: 20px;
}

.stats-summary h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

/* 暗色主题 */
.dark-theme .page-title {
  color: #fff;
}

.dark-theme .page-subtitle {
  color: #999;
}

.dark-theme .stat-card {
  background: #2d2d2d;
}

.dark-theme .stat-number {
  color: #fff;
}

.dark-theme .stat-label {
  color: #999;
}

.dark-theme .reviews-section {
  background: #2d2d2d;
}

.dark-theme .section-header h3 {
  color: #fff;
}

.dark-theme .task-title {
  color: #fff;
}

.dark-theme .task-meta {
  color: #666;
}

.dark-theme .chart-card {
  background: #404040;
}

.dark-theme .chart-card h4 {
  color: #fff;
}

.dark-theme .stats-summary h4 {
  color: #fff;
}
</style>
