<template>
  <div class="home">
    <div class="page-container">
      <div class="page-header">
        <h1 class="page-title">艾宾浩斯记忆曲线学习管理系统</h1>
      </div>
      
      <div class="card">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="feature-card">
              <el-icon size="48" color="#409EFF">
                <Document />
              </el-icon>
              <h3>学习任务管理</h3>
              <p>创建和管理学习任务，制定学习计划</p>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="feature-card">
              <el-icon size="48" color="#67C23A">
                <Clock />
              </el-icon>
              <h3>艾宾浩斯复习</h3>
              <p>基于记忆曲线的智能复习提醒</p>
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="feature-card">
              <el-icon size="48" color="#E6A23C">
                <Connection />
              </el-icon>
              <h3>思维导图</h3>
              <p>可视化知识结构，构建知识网络</p>
            </div>
          </el-col>
        </el-row>

        <div style="text-align: center; margin-top: 40px;">
          <div style="margin-bottom: 20px;">
            <h3 style="color: #333; margin-bottom: 16px;">🚀 主要功能页面</h3>
            <el-button type="primary" @click="$router.push('/dashboard')" style="margin-right: 10px;">
              📊 仪表板
            </el-button>
            <el-button type="success" @click="$router.push('/tasks')" style="margin-right: 10px;">
              📋 任务管理
            </el-button>
            <el-button type="warning" @click="$router.push('/tasks/1')" style="margin-right: 10px;">
              📄 任务详情
            </el-button>
            <el-button type="info" @click="$router.push('/reviews')" style="margin-right: 10px;">
              🧠 复习管理
            </el-button>
            <el-button type="warning" @click="$router.push('/mindmaps')">
              🗺️ 思维导图
            </el-button>
          </div>

          <div>
            <h3 style="color: #666; margin-bottom: 16px;">🧪 开发测试页面</h3>
            <el-button type="primary" @click="$router.push('/test')" style="margin-right: 10px;">
              🧪 测试阶段2功能
            </el-button>
            <el-button type="success" @click="$router.push('/api-test')" style="margin-right: 10px;">
              🔌 测试阶段3 API服务
            </el-button>
            <el-button type="warning" @click="$router.push('/store-test')" style="margin-right: 10px;">
              🏪 测试阶段4 状态管理
            </el-button>
            <el-button type="info" @click="$router.push('/component-test')">
              🧩 测试阶段5 核心组件
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Document, Clock, Connection } from '@element-plus/icons-vue'
</script>

<style scoped>
.home {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.feature-card {
  text-align: center;
  padding: 30px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-card h3 {
  margin: 16px 0 8px;
  font-size: 18px;
  color: #333;
}

.feature-card p {
  color: #666;
  line-height: 1.6;
}
</style>
