<template>
  <div class="tasks-view">
    <div class="tasks-container">
        <!-- 页面头部 -->
        <div class="page-header">
          <div class="header-left">
            <h1 class="page-title">任务管理</h1>
            <p class="page-subtitle">管理你的学习任务，制定学习计划</p>
          </div>
          <div class="header-right">
            <el-button type="primary" @click="$router.push('/tasks/create')">
              <el-icon><Plus /></el-icon>
              新建任务
            </el-button>
          </div>
        </div>

        <!-- 统计卡片 -->
        <el-row :gutter="20" class="stats-section">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon pending">
                <el-icon><Clock /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ taskStore.pendingTasks.length }}</div>
                <div class="stat-label">待开始</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon in-progress">
                <el-icon><Loading /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ taskStore.inProgressTasks.length }}</div>
                <div class="stat-label">进行中</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon completed">
                <el-icon><Check /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ taskStore.completedTasks.length }}</div>
                <div class="stat-label">已完成</div>
              </div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-icon total">
                <el-icon><Document /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-number">{{ taskStore.taskCount }}</div>
                <div class="stat-label">总任务数</div>
              </div>
            </div>
          </el-col>
        </el-row>

        <!-- 筛选和搜索 -->
        <div class="filter-section">
          <el-row :gutter="16" align="middle">
            <el-col :span="8">
              <el-input
                v-model="searchQuery"
                placeholder="搜索任务标题或内容..."
                @input="handleSearch"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-col>
            <el-col :span="4">
              <el-select v-model="filters.status" placeholder="状态" clearable @change="handleFilterChange">
                <el-option label="待开始" value="pending" />
                <el-option label="进行中" value="in_progress" />
                <el-option label="已完成" value="completed" />
                <el-option label="已暂停" value="paused" />
                <el-option label="已取消" value="cancelled" />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select v-model="filters.priority" placeholder="优先级" clearable @change="handleFilterChange">
                <el-option label="高" value="high" />
                <el-option label="中" value="medium" />
                <el-option label="低" value="low" />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-select v-model="filters.difficulty" placeholder="难度" clearable @change="handleFilterChange">
                <el-option label="简单" value="beginner" />
                <el-option label="中等" value="intermediate" />
                <el-option label="困难" value="advanced" />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-button @click="clearFilters">清除筛选</el-button>
            </el-col>
          </el-row>
        </div>

        <!-- 任务列表 -->
        <div class="tasks-section">
          <TaskList
            :tasks="filteredTasks"
            :loading="taskStore.isLoading"
            title="任务列表"
            :show-header="true"
            :show-actions="true"
            :show-pagination="true"
            @edit="handleTaskEdit"
            @delete="handleTaskDelete"
            @status-change="handleTaskStatusChange"
            @title-click="handleTaskTitleClick"
            @create="handleTaskCreate"
            @filters-change="handleFiltersChange"
            @sort-change="handleSortChange"
          />
        </div>
      </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useTaskStore, useAppStore } from '@/stores'
import { TaskList } from '@/components'
import type { LearningTask, TaskStatus, TaskQueryParams } from '@/types'
import {
  Plus,
  Clock,
  Loading,
  Check,
  Document,
  Search
} from '@element-plus/icons-vue'

const router = useRouter()
const taskStore = useTaskStore()
const appStore = useAppStore()

// 响应式数据
const searchQuery = ref('')
const filters = ref<TaskQueryParams>({})
const sortBy = ref('createdAt')

// 计算属性
const filteredTasks = computed(() => {
  let tasks = [...(taskStore.tasks || [])]
  
  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    tasks = tasks.filter(task => 
      task.title.toLowerCase().includes(query) ||
      task.description.toLowerCase().includes(query) ||
      task.content.toLowerCase().includes(query)
    )
  }
  
  // 状态过滤
  if (filters.value.status) {
    tasks = tasks.filter(task => task.status === filters.value.status)
  }
  
  // 优先级过滤
  if (filters.value.priority) {
    tasks = tasks.filter(task => task.priority === filters.value.priority)
  }
  
  // 难度过滤
  if (filters.value.difficulty) {
    tasks = tasks.filter(task => task.difficulty === filters.value.difficulty)
  }
  
  // 排序
  tasks.sort((a, b) => {
    const aValue = a[sortBy.value as keyof LearningTask]
    const bValue = b[sortBy.value as keyof LearningTask]
    
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return bValue.localeCompare(aValue) // 降序
    }
    
    return 0
  })
  
  return tasks
})

// 方法
const handleSearch = () => {
  // 搜索逻辑在计算属性中处理
}

const handleFilterChange = () => {
  // 筛选逻辑在计算属性中处理
}

const clearFilters = () => {
  searchQuery.value = ''
  filters.value = {}
}

const handleTaskEdit = (task: LearningTask) => {
  router.push(`/tasks/${task.id}/edit`)
}

const handleTaskDelete = async (taskId: string) => {
  try {
    await taskStore.deleteTask(taskId)
    appStore.showSuccess('删除成功', '任务已删除')
  } catch (error) {
    appStore.showError('删除失败', '删除任务时发生错误')
  }
}

const handleTaskStatusChange = async (taskId: string, status: TaskStatus) => {
  try {
    await taskStore.updateTaskStatus(taskId, status)
    appStore.showSuccess('状态更新', '任务状态已更新')
  } catch (error) {
    appStore.showError('更新失败', '更新任务状态时发生错误')
  }
}

const handleTaskTitleClick = (task: LearningTask) => {
  router.push(`/tasks/${task.id}`)
}

const handleTaskCreate = () => {
  router.push('/tasks/create')
}

const handleFiltersChange = (newFilters: TaskQueryParams) => {
  filters.value = { ...filters.value, ...newFilters }
}

const handleSortChange = (newSortBy: string) => {
  sortBy.value = newSortBy
}

// 生命周期
onMounted(async () => {
  try {
    await taskStore.fetchTasks()
  } catch (error) {
    appStore.showError('加载失败', '加载任务列表时发生错误')
  }
})

// 监听路由变化，刷新数据
watch(() => router.currentRoute.value, () => {
  if (router.currentRoute.value.path === '/tasks') {
    taskStore.fetchTasks()
  }
})
</script>

<style scoped>
.tasks-view {
  min-height: 100vh;
}

.tasks-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.page-subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.header-right {
  flex-shrink: 0;
  margin-left: 20px;
}

.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.stat-icon.pending {
  background: #E6A23C;
}

.stat-icon.in-progress {
  background: #409EFF;
}

.stat-icon.completed {
  background: #67C23A;
}

.stat-icon.total {
  background: #909399;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.filter-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.tasks-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 暗色主题 */
.dark-theme .page-title {
  color: #fff;
}

.dark-theme .page-subtitle {
  color: #999;
}

.dark-theme .stat-card {
  background: #2d2d2d;
}

.dark-theme .stat-number {
  color: #fff;
}

.dark-theme .stat-label {
  color: #999;
}

.dark-theme .filter-section {
  background: #2d2d2d;
}

.dark-theme .tasks-section {
  background: #2d2d2d;
}
</style>
