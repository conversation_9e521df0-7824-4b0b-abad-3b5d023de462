<template>
  <div class="task-detail-view">
    <AppLayout>
      <div class="task-detail-container" v-if="task">
        <!-- 页面头部 -->
        <div class="page-header">
          <div class="header-left">
            <el-button @click="$router.back()" class="back-btn">
              <el-icon><ArrowLeft /></el-icon>
              返回
            </el-button>
            <div class="header-info">
              <h1 class="page-title">{{ task.title }}</h1>
              <div class="task-meta">
                <el-tag :type="getStatusTagType(task.status)" size="small">
                  {{ TASK_CONFIG.STATUSES[task.status].label }}
                </el-tag>
                <el-tag :color="styleUtils.getPriorityColor(task.priority)" size="small" effect="plain">
                  {{ TASK_CONFIG.PRIORITIES[task.priority].label }}
                </el-tag>
                <el-tag :color="styleUtils.getDifficultyColor(task.difficulty)" size="small" effect="plain">
                  {{ TASK_CONFIG.DIFFICULTIES[task.difficulty].label }}
                </el-tag>
              </div>
            </div>
          </div>
          <div class="header-right">
            <el-button-group>
              <el-button @click="handleEdit">
                <el-icon><Edit /></el-icon>
                编辑
              </el-button>
              <el-button type="primary" @click="handleStatusAction" v-if="canChangeStatus">
                <el-icon><component :is="getStatusActionIcon()" /></el-icon>
                {{ getStatusActionText() }}
              </el-button>
            </el-button-group>
          </div>
        </div>

        <!-- 主要内容 -->
        <el-row :gutter="20">
          <!-- 左侧内容 -->
          <el-col :span="16">
            <!-- 任务描述 -->
            <div class="content-card">
              <h3 class="card-title">任务描述</h3>
              <div class="task-description">
                {{ task.description }}
              </div>
            </div>

            <!-- 任务内容 -->
            <div class="content-card">
              <h3 class="card-title">学习内容</h3>
              <div class="task-content">
                <div v-html="formatContent(task.content)"></div>
              </div>
            </div>

            <!-- 附件 -->
            <div class="content-card" v-if="task.attachments.length > 0">
              <h3 class="card-title">附件</h3>
              <div class="attachments">
                <div 
                  v-for="attachment in task.attachments" 
                  :key="attachment.id"
                  class="attachment-item"
                >
                  <el-icon><Paperclip /></el-icon>
                  <span class="attachment-name">{{ attachment.name }}</span>
                  <span class="attachment-size">{{ formatFileSize(attachment.size || 0) }}</span>
                  <el-button type="text" @click="downloadAttachment(attachment)">
                    下载
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 学习记录 -->
            <div class="content-card">
              <h3 class="card-title">学习记录</h3>
              <div class="study-records">
                <el-timeline>
                  <el-timeline-item
                    v-for="record in studyRecords"
                    :key="record.id"
                    :timestamp="dateUtils.format(record.createdAt, 'MM-DD HH:mm')"
                    :type="getRecordType(record.type)"
                  >
                    <div class="record-content">
                      <div class="record-title">{{ record.title }}</div>
                      <div class="record-description">{{ record.description }}</div>
                    </div>
                  </el-timeline-item>
                </el-timeline>
                <el-empty v-if="studyRecords.length === 0" description="暂无学习记录" />
              </div>
            </div>
          </el-col>

          <!-- 右侧信息 -->
          <el-col :span="8">
            <!-- 任务信息 -->
            <div class="info-card">
              <h3 class="card-title">任务信息</h3>
              <div class="info-list">
                <div class="info-item">
                  <span class="info-label">任务类型</span>
                  <span class="info-value">{{ TASK_CONFIG.TYPES[task.type].label }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">预计时长</span>
                  <span class="info-value">{{ dateUtils.formatDuration(task.estimatedDuration) }}</span>
                </div>
                <div class="info-item" v-if="task.dueDate">
                  <span class="info-label">截止时间</span>
                  <span class="info-value" :class="{ 'overdue': dateUtils.isOverdue(task.dueDate) }">
                    {{ dateUtils.format(task.dueDate, 'YYYY-MM-DD HH:mm') }}
                  </span>
                </div>
                <div class="info-item">
                  <span class="info-label">创建时间</span>
                  <span class="info-value">{{ dateUtils.format(task.createdAt, 'YYYY-MM-DD HH:mm') }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">更新时间</span>
                  <span class="info-value">{{ dateUtils.format(task.updatedAt, 'YYYY-MM-DD HH:mm') }}</span>
                </div>
                <div class="info-item" v-if="task.completedAt">
                  <span class="info-label">完成时间</span>
                  <span class="info-value">{{ dateUtils.format(task.completedAt, 'YYYY-MM-DD HH:mm') }}</span>
                </div>
              </div>
            </div>

            <!-- 标签 -->
            <div class="info-card" v-if="task.tags.length > 0">
              <h3 class="card-title">标签</h3>
              <div class="tags">
                <el-tag 
                  v-for="tag in task.tags" 
                  :key="tag" 
                  size="small"
                  effect="plain"
                  class="tag-item"
                >
                  {{ tag }}
                </el-tag>
              </div>
            </div>

            <!-- 复习计划 -->
            <div class="info-card">
              <h3 class="card-title">复习计划</h3>
              <div class="review-info">
                <div v-if="reviewSchedule">
                  <div class="review-status">
                    <span class="status-label">状态:</span>
                    <el-tag :type="reviewSchedule.isCompleted ? 'success' : 'warning'" size="small">
                      {{ reviewSchedule.isCompleted ? '已完成' : '进行中' }}
                    </el-tag>
                  </div>
                  <div class="review-progress">
                    <span class="progress-label">进度:</span>
                    <span class="progress-text">
                      {{ getCompletedStages() }}/{{ getTotalStages() }}
                    </span>
                  </div>
                  <el-button type="primary" size="small" @click="startReview" v-if="!reviewSchedule.isCompleted">
                    开始复习
                  </el-button>
                </div>
                <div v-else>
                  <p class="no-review">暂无复习计划</p>
                  <el-button type="primary" size="small" @click="createReviewSchedule">
                    创建复习计划
                  </el-button>
                </div>
              </div>
            </div>

            <!-- 快速操作 -->
            <div class="info-card">
              <h3 class="card-title">快速操作</h3>
              <div class="quick-actions">
                <el-button class="action-btn" @click="duplicateTask">
                  <el-icon><CopyDocument /></el-icon>
                  复制任务
                </el-button>
                <el-button class="action-btn" @click="exportTask">
                  <el-icon><Download /></el-icon>
                  导出任务
                </el-button>
                <el-button class="action-btn" @click="shareTask">
                  <el-icon><Share /></el-icon>
                  分享任务
                </el-button>
                <el-button class="action-btn" type="danger" @click="deleteTask">
                  <el-icon><Delete /></el-icon>
                  删除任务
                </el-button>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 加载状态 -->
      <div v-else-if="loading" class="loading-container">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- 错误状态 -->
      <div v-else class="error-container">
        <el-result
          icon="warning"
          title="任务不存在"
          sub-title="请检查任务ID是否正确"
        >
          <template #extra>
            <el-button type="primary" @click="$router.push('/tasks')">
              返回任务列表
            </el-button>
          </template>
        </el-result>
      </div>
    </AppLayout>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useTaskStore, useReviewStore, useAppStore } from '@/stores'
import { AppLayout } from '@/components'
import { dateUtils, styleUtils } from '@/utils'
import { TASK_CONFIG } from '@/constants'
import type { LearningTask, ReviewSchedule, TaskStatus } from '@/types'
import {
  ArrowLeft,
  Edit,
  VideoPlay,
  Check,
  Paperclip,
  CopyDocument,
  Download,
  Share,
  Delete
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const taskStore = useTaskStore()
const reviewStore = useReviewStore()
const appStore = useAppStore()

// 响应式数据
const task = ref<LearningTask | null>(null)
const reviewSchedule = ref<ReviewSchedule | null>(null)
const loading = ref(true)
const studyRecords = ref<Array<{
  id: string
  type: string
  title: string
  description: string
  createdAt: string
}>>([])

// 计算属性
const canChangeStatus = computed(() => {
  return task.value && ['pending', 'in_progress', 'paused'].includes(task.value.status)
})

// 方法
const getStatusTagType = (status: TaskStatus) => {
  const typeMap = {
    pending: '',
    in_progress: 'warning',
    completed: 'success',
    paused: 'info',
    cancelled: 'danger'
  }
  return typeMap[status] || ''
}

const getStatusActionIcon = () => {
  if (!task.value) return VideoPlay
  switch (task.value.status) {
    case 'pending': return VideoPlay
    case 'in_progress': return Check
    case 'paused': return VideoPlay
    default: return VideoPlay
  }
}

const getStatusActionText = () => {
  if (!task.value) return '开始'
  switch (task.value.status) {
    case 'pending': return '开始任务'
    case 'in_progress': return '完成任务'
    case 'paused': return '继续任务'
    default: return '开始任务'
  }
}

const formatContent = (content: string) => {
  // 简单的内容格式化，将换行转换为<br>
  return content.replace(/\n/g, '<br>')
}

const formatFileSize = (size: number) => {
  if (size < 1024) return `${size} B`
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`
  return `${(size / (1024 * 1024)).toFixed(1)} MB`
}

const getRecordType = (type: string) => {
  const typeMap = {
    start: 'primary',
    pause: 'warning',
    complete: 'success',
    note: 'info'
  }
  return typeMap[type as keyof typeof typeMap] || 'info'
}

const getCompletedStages = () => {
  return reviewSchedule.value?.intervals.filter(interval => interval.isCompleted).length || 0
}

const getTotalStages = () => {
  return reviewSchedule.value?.intervals.length || 0
}

const handleEdit = () => {
  router.push(`/tasks/${task.value?.id}/edit`)
}

const handleStatusAction = async () => {
  if (!task.value) return
  
  try {
    let newStatus: TaskStatus
    switch (task.value.status) {
      case 'pending':
        newStatus = 'in_progress'
        break
      case 'in_progress':
        newStatus = 'completed'
        break
      case 'paused':
        newStatus = 'in_progress'
        break
      default:
        return
    }
    
    await taskStore.updateTaskStatus(task.value.id, newStatus)
    task.value.status = newStatus
    appStore.showSuccess('状态更新', '任务状态已更新')
  } catch (error) {
    appStore.showError('更新失败', '更新任务状态时发生错误')
  }
}

const downloadAttachment = (attachment: any) => {
  // 下载附件逻辑
  appStore.showInfo('下载', `下载附件: ${attachment.name}`)
}

const startReview = () => {
  router.push(`/reviews/${reviewSchedule.value?.id}`)
}

const createReviewSchedule = async () => {
  if (!task.value) return
  
  try {
    await reviewStore.createSchedule({ taskId: task.value.id })
    appStore.showSuccess('创建成功', '复习计划已创建')
    // 重新加载复习计划
    await loadReviewSchedule()
  } catch (error) {
    appStore.showError('创建失败', '创建复习计划时发生错误')
  }
}

const duplicateTask = () => {
  appStore.showInfo('复制任务', '复制任务功能')
}

const exportTask = () => {
  appStore.showInfo('导出任务', '导出任务功能')
}

const shareTask = () => {
  appStore.showInfo('分享任务', '分享任务功能')
}

const deleteTask = async () => {
  if (!task.value) return
  
  try {
    await taskStore.deleteTask(task.value.id)
    appStore.showSuccess('删除成功', '任务已删除')
    router.push('/tasks')
  } catch (error) {
    appStore.showError('删除失败', '删除任务时发生错误')
  }
}

const loadTask = async () => {
  const taskId = route.params.id as string
  try {
    loading.value = true
    task.value = await taskStore.fetchTaskById(taskId)
  } catch (error) {
    console.error('加载任务失败:', error)
  } finally {
    loading.value = false
  }
}

const loadReviewSchedule = async () => {
  if (!task.value) return
  
  try {
    // 这里应该根据taskId查找复习计划
    // 暂时使用模拟数据
    reviewSchedule.value = null
  } catch (error) {
    console.error('加载复习计划失败:', error)
  }
}

// 生命周期
onMounted(async () => {
  await loadTask()
  if (task.value) {
    await loadReviewSchedule()
  }
})
</script>

<style scoped>
.task-detail-view {
  min-height: 100vh;
}

.task-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.header-left {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.back-btn {
  margin-top: 8px;
}

.header-info {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #333;
  margin: 0 0 12px 0;
  line-height: 1.2;
}

.task-meta {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.header-right {
  flex-shrink: 0;
  margin-left: 20px;
}

.content-card,
.info-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.task-description,
.task-content {
  font-size: 14px;
  line-height: 1.6;
  color: #666;
}

.attachments {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}

.attachment-name {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.attachment-size {
  font-size: 12px;
  color: #999;
}

.study-records {
  max-height: 400px;
  overflow-y: auto;
}

.record-content {
  margin-bottom: 8px;
}

.record-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.record-description {
  font-size: 13px;
  color: #666;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-size: 14px;
  color: #666;
}

.info-value {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.info-value.overdue {
  color: #F56C6C;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  font-size: 12px;
}

.review-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.review-status,
.review-progress {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-label,
.progress-label {
  font-size: 14px;
  color: #666;
}

.progress-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.no-review {
  font-size: 14px;
  color: #999;
  margin: 0 0 12px 0;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  width: 100%;
  justify-content: flex-start;
}

.loading-container,
.error-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

/* 暗色主题 */
.dark-theme .page-title {
  color: #fff;
}

.dark-theme .content-card,
.dark-theme .info-card {
  background: #2d2d2d;
}

.dark-theme .card-title {
  color: #fff;
}

.dark-theme .task-description,
.dark-theme .task-content {
  color: #ccc;
}

.dark-theme .attachment-item {
  border-color: #404040;
}

.dark-theme .attachment-name {
  color: #fff;
}

.dark-theme .attachment-size {
  color: #666;
}

.dark-theme .record-title {
  color: #fff;
}

.dark-theme .record-description {
  color: #999;
}

.dark-theme .info-label {
  color: #999;
}

.dark-theme .info-value {
  color: #fff;
}

.dark-theme .status-label,
.dark-theme .progress-label {
  color: #999;
}

.dark-theme .progress-text {
  color: #fff;
}

.dark-theme .no-review {
  color: #666;
}
</style>
