<template>
  <div class="api-test-view">
    <div class="page-container">
      <div class="page-header">
        <h1 class="page-title">API服务层测试</h1>
        <p>测试6个API服务模块的类型定义和方法调用</p>
      </div>

      <el-row :gutter="20">
        <!-- 任务管理API测试 -->
        <el-col :span="12">
          <div class="card">
            <h3>📋 任务管理API (TaskService)</h3>
            <el-space direction="vertical" style="width: 100%">
              <div>
                <strong>可用方法数量:</strong> {{ taskMethods.length }}
              </div>
              <div>
                <strong>主要方法:</strong>
                <el-tag v-for="method in taskMethods.slice(0, 5)" :key="method" size="small" style="margin: 2px;">
                  {{ method }}
                </el-tag>
              </div>
              <el-button @click="testTaskService" type="primary" size="small">
                测试类型检查
              </el-button>
            </el-space>
          </div>
        </el-col>

        <!-- 复习管理API测试 -->
        <el-col :span="12">
          <div class="card">
            <h3>🧠 复习管理API (ReviewService)</h3>
            <el-space direction="vertical" style="width: 100%">
              <div>
                <strong>可用方法数量:</strong> {{ reviewMethods.length }}
              </div>
              <div>
                <strong>主要方法:</strong>
                <el-tag v-for="method in reviewMethods.slice(0, 5)" :key="method" size="small" style="margin: 2px;">
                  {{ method }}
                </el-tag>
              </div>
              <el-button @click="testReviewService" type="primary" size="small">
                测试类型检查
              </el-button>
            </el-space>
          </div>
        </el-col>

        <!-- 思维导图API测试 -->
        <el-col :span="12">
          <div class="card">
            <h3>🗺️ 思维导图API (MindMapService)</h3>
            <el-space direction="vertical" style="width: 100%">
              <div>
                <strong>可用方法数量:</strong> {{ mindmapMethods.length }}
              </div>
              <div>
                <strong>主要方法:</strong>
                <el-tag v-for="method in mindmapMethods.slice(0, 5)" :key="method" size="small" style="margin: 2px;">
                  {{ method }}
                </el-tag>
              </div>
              <el-button @click="testMindMapService" type="primary" size="small">
                测试类型检查
              </el-button>
            </el-space>
          </div>
        </el-col>

        <!-- 用户管理API测试 -->
        <el-col :span="12">
          <div class="card">
            <h3>👤 用户管理API (UserService)</h3>
            <el-space direction="vertical" style="width: 100%">
              <div>
                <strong>可用方法数量:</strong> {{ userMethods.length }}
              </div>
              <div>
                <strong>主要方法:</strong>
                <el-tag v-for="method in userMethods.slice(0, 5)" :key="method" size="small" style="margin: 2px;">
                  {{ method }}
                </el-tag>
              </div>
              <el-button @click="testUserService" type="primary" size="small">
                测试类型检查
              </el-button>
            </el-space>
          </div>
        </el-col>

        <!-- 通知API测试 -->
        <el-col :span="12">
          <div class="card">
            <h3>🔔 通知API (NotificationService)</h3>
            <el-space direction="vertical" style="width: 100%">
              <div>
                <strong>可用方法数量:</strong> {{ notificationMethods.length }}
              </div>
              <div>
                <strong>主要方法:</strong>
                <el-tag v-for="method in notificationMethods.slice(0, 5)" :key="method" size="small" style="margin: 2px;">
                  {{ method }}
                </el-tag>
              </div>
              <el-button @click="testNotificationService" type="primary" size="small">
                测试类型检查
              </el-button>
            </el-space>
          </div>
        </el-col>

        <!-- 分析统计API测试 -->
        <el-col :span="12">
          <div class="card">
            <h3>📊 分析统计API (AnalyticsService)</h3>
            <el-space direction="vertical" style="width: 100%">
              <div>
                <strong>可用方法数量:</strong> {{ analyticsMethods.length }}
              </div>
              <div>
                <strong>主要方法:</strong>
                <el-tag v-for="method in analyticsMethods.slice(0, 5)" :key="method" size="small" style="margin: 2px;">
                  {{ method }}
                </el-tag>
              </div>
              <el-button @click="testAnalyticsService" type="primary" size="small">
                测试类型检查
              </el-button>
            </el-space>
          </div>
        </el-col>
      </el-row>

      <!-- 测试结果 -->
      <div class="card" style="margin-top: 20px;">
        <h3>🧪 测试结果</h3>
        <el-space direction="vertical" style="width: 100%">
          <div v-for="result in testResults" :key="result.service">
            <el-tag :type="result.success ? 'success' : 'danger'">
              {{ result.service }}: {{ result.success ? '✅ 通过' : '❌ 失败' }}
            </el-tag>
            <span style="margin-left: 10px;">{{ result.message }}</span>
          </div>
        </el-space>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { 
  TaskService, 
  ReviewService, 
  MindMapService, 
  UserService, 
  NotificationService, 
  AnalyticsService 
} from '@/services/api'

// 响应式数据
const testResults = ref<Array<{
  service: string
  success: boolean
  message: string
}>>([])

// 计算属性 - 获取各服务的方法列表
const taskMethods = computed(() => {
  return Object.getOwnPropertyNames(TaskService).filter(name => 
    typeof TaskService[name as keyof typeof TaskService] === 'function' && name !== 'constructor'
  )
})

const reviewMethods = computed(() => {
  return Object.getOwnPropertyNames(ReviewService).filter(name => 
    typeof ReviewService[name as keyof typeof ReviewService] === 'function' && name !== 'constructor'
  )
})

const mindmapMethods = computed(() => {
  return Object.getOwnPropertyNames(MindMapService).filter(name => 
    typeof MindMapService[name as keyof typeof MindMapService] === 'function' && name !== 'constructor'
  )
})

const userMethods = computed(() => {
  return Object.getOwnPropertyNames(UserService).filter(name => 
    typeof UserService[name as keyof typeof UserService] === 'function' && name !== 'constructor'
  )
})

const notificationMethods = computed(() => {
  return Object.getOwnPropertyNames(NotificationService).filter(name => 
    typeof NotificationService[name as keyof typeof NotificationService] === 'function' && name !== 'constructor'
  )
})

const analyticsMethods = computed(() => {
  return Object.getOwnPropertyNames(AnalyticsService).filter(name => 
    typeof AnalyticsService[name as keyof typeof AnalyticsService] === 'function' && name !== 'constructor'
  )
})

// 测试方法
const testTaskService = () => {
  try {
    // 测试类型检查和方法存在性
    const hasGetTasks = typeof TaskService.getTasks === 'function'
    const hasCreateTask = typeof TaskService.createTask === 'function'
    const hasUpdateTask = typeof TaskService.updateTask === 'function'
    
    if (hasGetTasks && hasCreateTask && hasUpdateTask) {
      addTestResult('TaskService', true, `${taskMethods.value.length}个方法可用`)
    } else {
      addTestResult('TaskService', false, '关键方法缺失')
    }
  } catch (error) {
    addTestResult('TaskService', false, `错误: ${error}`)
  }
}

const testReviewService = () => {
  try {
    const hasGetSchedules = typeof ReviewService.getReviewSchedules === 'function'
    const hasCreateSchedule = typeof ReviewService.createReviewSchedule === 'function'
    const hasCompleteStage = typeof ReviewService.completeReviewStage === 'function'
    
    if (hasGetSchedules && hasCreateSchedule && hasCompleteStage) {
      addTestResult('ReviewService', true, `${reviewMethods.value.length}个方法可用`)
    } else {
      addTestResult('ReviewService', false, '关键方法缺失')
    }
  } catch (error) {
    addTestResult('ReviewService', false, `错误: ${error}`)
  }
}

const testMindMapService = () => {
  try {
    const hasGetMindMaps = typeof MindMapService.getMindMaps === 'function'
    const hasCreateMindMap = typeof MindMapService.createMindMap === 'function'
    const hasAddNode = typeof MindMapService.addNode === 'function'
    
    if (hasGetMindMaps && hasCreateMindMap && hasAddNode) {
      addTestResult('MindMapService', true, `${mindmapMethods.value.length}个方法可用`)
    } else {
      addTestResult('MindMapService', false, '关键方法缺失')
    }
  } catch (error) {
    addTestResult('MindMapService', false, `错误: ${error}`)
  }
}

const testUserService = () => {
  try {
    const hasLogin = typeof UserService.login === 'function'
    const hasGetCurrentUser = typeof UserService.getCurrentUser === 'function'
    const hasUpdateProfile = typeof UserService.updateProfile === 'function'
    
    if (hasLogin && hasGetCurrentUser && hasUpdateProfile) {
      addTestResult('UserService', true, `${userMethods.value.length}个方法可用`)
    } else {
      addTestResult('UserService', false, '关键方法缺失')
    }
  } catch (error) {
    addTestResult('UserService', false, `错误: ${error}`)
  }
}

const testNotificationService = () => {
  try {
    const hasGetNotifications = typeof NotificationService.getNotifications === 'function'
    const hasMarkAsRead = typeof NotificationService.markAsRead === 'function'
    const hasGetSettings = typeof NotificationService.getNotificationSettings === 'function'
    
    if (hasGetNotifications && hasMarkAsRead && hasGetSettings) {
      addTestResult('NotificationService', true, `${notificationMethods.value.length}个方法可用`)
    } else {
      addTestResult('NotificationService', false, '关键方法缺失')
    }
  } catch (error) {
    addTestResult('NotificationService', false, `错误: ${error}`)
  }
}

const testAnalyticsService = () => {
  try {
    const hasGetStudyAnalytics = typeof AnalyticsService.getStudyAnalytics === 'function'
    const hasGetDashboard = typeof AnalyticsService.getDashboardData === 'function'
    const hasGetTimeStats = typeof AnalyticsService.getTimeStats === 'function'
    
    if (hasGetStudyAnalytics && hasGetDashboard && hasGetTimeStats) {
      addTestResult('AnalyticsService', true, `${analyticsMethods.value.length}个方法可用`)
    } else {
      addTestResult('AnalyticsService', false, '关键方法缺失')
    }
  } catch (error) {
    addTestResult('AnalyticsService', false, `错误: ${error}`)
  }
}

const addTestResult = (service: string, success: boolean, message: string) => {
  // 移除之前的测试结果
  testResults.value = testResults.value.filter(r => r.service !== service)
  // 添加新的测试结果
  testResults.value.push({ service, success, message })
}

// 生命周期
onMounted(() => {
  // 页面加载时显示基本信息
  console.log('API服务测试页面已加载')
  console.log('TaskService方法:', taskMethods.value)
  console.log('ReviewService方法:', reviewMethods.value)
})
</script>

<style scoped>
.api-test-view {
  min-height: 100vh;
  background: #f5f5f5;
}

.card h3 {
  margin-bottom: 16px;
  color: #333;
  font-size: 16px;
}

.el-tag {
  margin: 2px;
}
</style>
