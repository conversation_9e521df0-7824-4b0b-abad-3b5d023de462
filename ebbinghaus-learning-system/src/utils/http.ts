import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import { ElMessage } from 'element-plus'
import type { ApiResponse } from '@/types'

// 扩展请求配置类型
interface CustomAxiosRequestConfig extends Partial<InternalAxiosRequestConfig> {
  showLoading?: boolean
  params?: any
  data?: any
}

// 创建axios实例
const http: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
http.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 添加认证token
    const token = localStorage.getItem('access_token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加请求时间戳
    if (config.params) {
      config.params._t = Date.now()
    } else {
      config.params = { _t: Date.now() }
    }

    // 显示加载状态
    const customConfig = config as CustomAxiosRequestConfig
    if (customConfig.showLoading !== false) {
      // 可以在这里添加全局loading状态
    }

    return config
  },
  (error: AxiosError) => {
    return Promise.reject(error)
  }
)

// 是否正在刷新token
let isRefreshing = false
// 等待刷新token的请求队列
let failedQueue: Array<{
  resolve: (value: any) => void
  reject: (reason: any) => void
}> = []

// 处理队列中的请求
const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error)
    } else {
      resolve(token)
    }
  })

  failedQueue = []
}

// 创建独立的axios实例用于刷新token，避免循环引用
const refreshAxios = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 刷新token
const refreshToken = async (): Promise<string> => {
  const refreshToken = localStorage.getItem('refresh_token')
  if (!refreshToken) {
    throw new Error('No refresh token available')
  }

  try {
    const response = await refreshAxios.post('/auth/refresh', { refreshToken })
    const { data } = response.data
    const { tokens } = data

    // 保存新的token
    localStorage.setItem('access_token', tokens.accessToken)
    localStorage.setItem('refresh_token', tokens.refreshToken)

    return tokens.accessToken
  } catch (error) {
    // 刷新失败，清除所有token
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    throw error
  }
}

// 响应拦截器
http.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response

    // 隐藏加载状态
    // 可以在这里隐藏全局loading状态

    // 检查业务状态码
    if (data.success === false) {
      const message = data.message || '请求失败'
      ElMessage.error(message)
      return Promise.reject(new Error(message))
    }

    return response
  },
  async (error: AxiosError<ApiResponse>) => {
    // 隐藏加载状态
    // 可以在这里隐藏全局loading状态

    const { response, config } = error

    if (response) {
      const { status, data } = response

      switch (status) {
        case 401:
          // 如果是刷新token的请求失败，直接清除token
          if (config?.url?.includes('/auth/refresh')) {
            localStorage.removeItem('access_token')
            localStorage.removeItem('refresh_token')
            ElMessage.error('登录已过期，请重新登录')
            break
          }

          // 尝试刷新token
          if (!isRefreshing) {
            isRefreshing = true

            try {
              const newToken = await refreshToken()
              isRefreshing = false
              processQueue(null, newToken)

              // 重新发送原始请求
              if (config && config.headers) {
                config.headers.Authorization = `Bearer ${newToken}`
                return http.request(config)
              }
            } catch (refreshError) {
              isRefreshing = false
              processQueue(refreshError, null)
              localStorage.removeItem('access_token')
              localStorage.removeItem('refresh_token')
              ElMessage.error('登录已过期，请重新登录')
            }
          } else {
            // 如果正在刷新token，将请求加入队列
            return new Promise((resolve, reject) => {
              failedQueue.push({ resolve, reject })
            }).then(token => {
              if (config && config.headers) {
                config.headers.Authorization = `Bearer ${token}`
                return http.request(config)
              }
            }).catch(err => {
              return Promise.reject(err)
            })
          }
          break

        case 403:
          ElMessage.error('没有权限访问该资源')
          break

        case 404:
          ElMessage.error('请求的资源不存在')
          break

        case 422:
          // 表单验证错误
          if (data?.errors && Array.isArray(data.errors)) {
            data.errors.forEach((err: string) => {
              ElMessage.error(err)
            })
          } else {
            ElMessage.error(data?.message || '请求参数错误')
          }
          break

        case 429:
          ElMessage.error('请求过于频繁，请稍后再试')
          break

        case 500:
          ElMessage.error('服务器内部错误')
          break

        case 502:
        case 503:
        case 504:
          ElMessage.error('服务暂时不可用，请稍后再试')
          break

        default:
          ElMessage.error(data?.message || `请求失败 (${status})`)
      }
    } else if (error.code === 'ECONNABORTED') {
      ElMessage.error('请求超时，请检查网络连接')
    } else if (error.message === 'Network Error') {
      ElMessage.error('网络连接失败，请检查网络设置')
    } else {
      ElMessage.error('请求失败，请稍后再试')
    }

    return Promise.reject(error)
  }
)

// 封装常用的HTTP方法
export const httpClient = {
  get: <T = any>(url: string, config?: CustomAxiosRequestConfig): Promise<ApiResponse<T>> => {
    return http.get(url, config).then(res => res.data)
  },

  post: <T = any>(url: string, data?: any, config?: CustomAxiosRequestConfig): Promise<ApiResponse<T>> => {
    return http.post(url, data, config).then(res => res.data)
  },

  put: <T = any>(url: string, data?: any, config?: CustomAxiosRequestConfig): Promise<ApiResponse<T>> => {
    return http.put(url, data, config).then(res => res.data)
  },

  patch: <T = any>(url: string, data?: any, config?: CustomAxiosRequestConfig): Promise<ApiResponse<T>> => {
    return http.patch(url, data, config).then(res => res.data)
  },

  delete: <T = any>(url: string, config?: CustomAxiosRequestConfig): Promise<ApiResponse<T>> => {
    return http.delete(url, config).then(res => res.data)
  },

  upload: <T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<ApiResponse<T>> => {
    const formData = new FormData()
    formData.append('file', file)

    return http.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      },
    }).then(res => res.data)
  },

  download: (url: string, filename?: string): Promise<void> => {
    return http.get(url, {
      responseType: 'blob',
    }).then(response => {
      const blob = new Blob([response.data])
      const downloadUrl = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = downloadUrl
      link.download = filename || 'download'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(downloadUrl)
    })
  },
}

// 请求重试机制
export const retryRequest = async <T>(
  requestFn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error

  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await requestFn()
    } catch (error) {
      lastError = error as Error
      
      if (i === maxRetries) {
        break
      }

      // 等待指定时间后重试
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)))
    }
  }

  throw lastError!
}

// 并发请求控制
export const concurrentRequest = async <T>(
  requests: (() => Promise<T>)[],
  limit: number = 5
): Promise<T[]> => {
  const results: T[] = []
  const executing: Promise<void>[] = []

  for (const request of requests) {
    const promise = request().then(result => {
      results.push(result)
    })

    executing.push(promise)

    if (executing.length >= limit) {
      await Promise.race(executing)
      executing.splice(executing.findIndex(p => p === promise), 1)
    }
  }

  await Promise.all(executing)
  return results
}

export default http
