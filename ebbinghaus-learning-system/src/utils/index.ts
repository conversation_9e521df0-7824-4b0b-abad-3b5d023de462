import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'
import duration from 'dayjs/plugin/duration'
import type { PerformanceLevel, DifficultyLevel, TaskStatus, Priority } from '@/types'

// 配置dayjs
dayjs.locale('zh-cn')
dayjs.extend(relativeTime)
dayjs.extend(duration)

// 日期时间工具函数
export const dateUtils = {
  /**
   * 格式化日期
   */
  format: (date: string | Date, format: string = 'YYYY-MM-DD HH:mm:ss'): string => {
    return dayjs(date).format(format)
  },

  /**
   * 相对时间
   */
  fromNow: (date: string | Date): string => {
    return dayjs(date).fromNow()
  },

  /**
   * 计算两个日期之间的天数差
   */
  daysBetween: (start: string | Date, end: string | Date): number => {
    return dayjs(end).diff(dayjs(start), 'day')
  },

  /**
   * 添加天数
   */
  addDays: (date: string | Date, days: number): string => {
    return dayjs(date).add(days, 'day').toISOString()
  },

  /**
   * 检查是否是今天
   */
  isToday: (date: string | Date): boolean => {
    return dayjs(date).isSame(dayjs(), 'day')
  },

  /**
   * 检查是否过期
   */
  isOverdue: (date: string | Date): boolean => {
    return dayjs(date).isBefore(dayjs(), 'day')
  },

  /**
   * 获取本周开始和结束日期
   */
  getWeekRange: (): { start: string; end: string } => {
    const start = dayjs().startOf('week').toISOString()
    const end = dayjs().endOf('week').toISOString()
    return { start, end }
  },

  /**
   * 获取本月开始和结束日期
   */
  getMonthRange: (): { start: string; end: string } => {
    const start = dayjs().startOf('month').toISOString()
    const end = dayjs().endOf('month').toISOString()
    return { start, end }
  },

  /**
   * 格式化持续时间（分钟转换为可读格式）
   */
  formatDuration: (minutes: number): string => {
    const duration = dayjs.duration(minutes, 'minutes')
    const hours = duration.hours()
    const mins = duration.minutes()
    
    if (hours > 0) {
      return `${hours}小时${mins > 0 ? mins + '分钟' : ''}`
    }
    return `${mins}分钟`
  }
}

// 验证工具函数
export const validators = {
  /**
   * 验证邮箱格式
   */
  email: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },

  /**
   * 验证密码强度
   */
  password: (password: string): { valid: boolean; message: string } => {
    if (password.length < 8) {
      return { valid: false, message: '密码长度至少8位' }
    }
    if (!/(?=.*[a-z])/.test(password)) {
      return { valid: false, message: '密码必须包含小写字母' }
    }
    if (!/(?=.*[A-Z])/.test(password)) {
      return { valid: false, message: '密码必须包含大写字母' }
    }
    if (!/(?=.*\d)/.test(password)) {
      return { valid: false, message: '密码必须包含数字' }
    }
    return { valid: true, message: '密码强度良好' }
  },

  /**
   * 验证用户名
   */
  username: (username: string): boolean => {
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/
    return usernameRegex.test(username)
  },

  /**
   * 验证URL格式
   */
  url: (url: string): boolean => {
    try {
      new URL(url)
      return true
    } catch {
      return false
    }
  },

  /**
   * 验证手机号码
   */
  phone: (phone: string): boolean => {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  }
}

// 格式化工具函数
export const formatters = {
  /**
   * 格式化文件大小
   */
  fileSize: (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  },

  /**
   * 格式化数字（添加千分位分隔符）
   */
  number: (num: number): string => {
    return num.toLocaleString('zh-CN')
  },

  /**
   * 格式化百分比
   */
  percentage: (value: number, total: number, decimals: number = 1): string => {
    if (total === 0) return '0%'
    const percentage = (value / total) * 100
    return `${percentage.toFixed(decimals)}%`
  },

  /**
   * 截断文本
   */
  truncate: (text: string, length: number = 50, suffix: string = '...'): string => {
    if (text.length <= length) return text
    return text.substring(0, length) + suffix
  },

  /**
   * 首字母大写
   */
  capitalize: (str: string): string => {
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
  },

  /**
   * 驼峰转短横线
   */
  kebabCase: (str: string): string => {
    return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase()
  }
}

// 艾宾浩斯记忆曲线相关工具
export const ebbinghausUtils = {
  /**
   * 获取标准艾宾浩斯复习间隔（天数）
   */
  getStandardIntervals: (): number[] => {
    return [1, 2, 4, 7, 15, 30, 60, 120]
  },

  /**
   * 根据表现调整复习间隔
   */
  adjustInterval: (currentInterval: number, performance: PerformanceLevel): number => {
    const multipliers = {
      poor: 0.5,
      fair: 0.8,
      good: 1.2,
      excellent: 1.5
    }
    
    const newInterval = Math.round(currentInterval * multipliers[performance])
    return Math.max(1, Math.min(365, newInterval)) // 限制在1-365天之间
  },

  /**
   * 计算下次复习日期
   */
  getNextReviewDate: (lastReviewDate: string, interval: number): string => {
    return dateUtils.addDays(lastReviewDate, interval)
  },

  /**
   * 计算记忆保持率
   */
  calculateRetentionRate: (performance: PerformanceLevel): number => {
    const rates = {
      poor: 0.2,
      fair: 0.5,
      good: 0.8,
      excellent: 0.95
    }
    return rates[performance]
  }
}

// 颜色和样式工具
export const styleUtils = {
  /**
   * 根据任务状态获取颜色
   */
  getStatusColor: (status: TaskStatus): string => {
    const colors = {
      pending: '#909399',
      in_progress: '#409EFF',
      completed: '#67C23A',
      cancelled: '#F56C6C'
    }
    return colors[status] || '#909399'
  },

  /**
   * 根据优先级获取颜色
   */
  getPriorityColor: (priority: Priority): string => {
    const colors = {
      1: '#C0C4CC', // 很低
      2: '#909399', // 低
      3: '#409EFF', // 中
      4: '#E6A23C', // 高
      5: '#F56C6C'  // 很高
    }
    return colors[priority] || '#409EFF'
  },

  /**
   * 根据难度获取颜色
   */
  getDifficultyColor: (difficulty: DifficultyLevel): string => {
    const colors = {
      1: '#67C23A', // 很简单
      2: '#95D475', // 简单
      3: '#409EFF', // 中等
      4: '#E6A23C', // 困难
      5: '#F56C6C'  // 很困难
    }
    return colors[difficulty] || '#409EFF'
  },

  /**
   * 根据表现获取颜色
   */
  getPerformanceColor: (performance: PerformanceLevel): string => {
    const colors = {
      poor: '#F56C6C',
      fair: '#E6A23C',
      good: '#409EFF',
      excellent: '#67C23A'
    }
    return colors[performance]
  },

  /**
   * 生成随机颜色
   */
  randomColor: (): string => {
    const colors = [
      '#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399',
      '#8E44AD', '#3498DB', '#2ECC71', '#F39C12', '#E74C3C'
    ]
    return colors[Math.floor(Math.random() * colors.length)]
  }
}

// 本地存储工具
export const storageUtils = {
  /**
   * 设置本地存储
   */
  set: (key: string, value: any): void => {
    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('Failed to set localStorage:', error)
    }
  },

  /**
   * 获取本地存储
   */
  get: <T = any>(key: string, defaultValue?: T): T | null => {
    try {
      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : defaultValue || null
    } catch (error) {
      console.error('Failed to get localStorage:', error)
      return defaultValue || null
    }
  },

  /**
   * 删除本地存储
   */
  remove: (key: string): void => {
    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error('Failed to remove localStorage:', error)
    }
  },

  /**
   * 清空本地存储
   */
  clear: (): void => {
    try {
      localStorage.clear()
    } catch (error) {
      console.error('Failed to clear localStorage:', error)
    }
  }
}

// 通用工具函数
export const utils = {
  /**
   * 生成唯一ID
   */
  generateId: (): string => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2)
  },

  /**
   * 防抖函数
   */
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void => {
    let timeout: NodeJS.Timeout
    return (...args: Parameters<T>) => {
      clearTimeout(timeout)
      timeout = setTimeout(() => func.apply(null, args), wait)
    }
  },

  /**
   * 节流函数
   */
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void => {
    let inThrottle: boolean
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func.apply(null, args)
        inThrottle = true
        setTimeout(() => (inThrottle = false), limit)
      }
    }
  },

  /**
   * 深拷贝
   */
  deepClone: <T>(obj: T): T => {
    return JSON.parse(JSON.stringify(obj))
  },

  /**
   * 数组去重
   */
  unique: <T>(arr: T[]): T[] => {
    return [...new Set(arr)]
  },

  /**
   * 随机打乱数组
   */
  shuffle: <T>(arr: T[]): T[] => {
    const newArr = [...arr]
    for (let i = newArr.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1))
      ;[newArr[i], newArr[j]] = [newArr[j], newArr[i]]
    }
    return newArr
  },

  /**
   * 延迟执行
   */
  sleep: (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}
